import { type TFunction } from 'i18next'

import type { CardWithTile } from '~/components/cards-with-title'
import { PurchaseNftModal } from '~/components/gem-launch-platform'
import { Button, ButtonVariant, Icon, IconName } from '~/tenset-components'

const { Secondary } = ButtonVariant

export const accessLaunchpad = (
  isConnected: boolean,
  t: TFunction,
  localizePathname: (pathname: string) => string
): CardWithTile[] => [
  {
    title: t('access-launchpad.subscribe.title'),
    description: t('access-launchpad.subscribe.description'),
    image: IconName.YearlyAccess,
    action: (
      <Button
        variant={ButtonVariant.Secondary}
        to={localizePathname('/gem-launch-platform/panel/subscribe')}
      >
        {t('access-launchpad.subscribe.action')}

        <Icon name={IconName.ChevronRight16} />
      </Button>
    ),
  },
  {
    title: t('access-launchpad.burn.title'),
    description: t('access-launchpad.burn.description'),
    image: IconName.BurnTokens,
    action: (
      <Button variant={Secondary} disabled>
        {t('access-launchpad.burn.action')}

        <Icon name={IconName.ChevronRight16} color="currentColor" />
      </Button>
    ),
    // isConnected ? (
    //   <Button
    //     variant={Secondary}
    //     to={localizePathname('/gem-launch-platform/panel/upgrade')}
    //   >
    //     {t('access-launchpad.burn.action')}

    //     <Icon name={IconName.ChevronRight16} color="currentColor" />
    //   </Button>
    // ) : (
    //   <ConnectWallet
    //     buttonConnectLabel={t('access-launchpad.burn.action')}
    //     variant={ButtonVariant.Secondary}
    //     buttonIcon={IconName.ChevronRight16}
    //   />
    // ),
  },
  {
    title: t('access-launchpad.purchase-nft.title'),
    description: t('access-launchpad.purchase-nft.description'),
    image: IconName.BuyNftFromMarket,
    action: <PurchaseNftModal />,
  },
]
