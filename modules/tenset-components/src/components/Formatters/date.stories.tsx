import { Meta } from '@storybook/react'

import { defaultDateFormatterOptions, defaultLocale } from './defaults'

import { DateFormatter } from './index'

type DateFormatterType = typeof DateFormatter

export default {
  title: 'Components/Formatters/Date',
  component: DateFormatter,
  args: {
    date: new Date(),
    locales: defaultLocale,
    options: defaultDateFormatterOptions.full,
  },
} as Meta<DateFormatterType>

export const Preview = {}

export const DateOnly = {
  args: {
    options: defaultDateFormatterOptions.dateOnly,
  },
}
