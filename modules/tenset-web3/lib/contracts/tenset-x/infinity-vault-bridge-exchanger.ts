import { BigNumber, ContractTransaction } from 'ethers'

import { tensetXInfinityVaultBridgeExchangerAbi } from '../../assets'
import { config } from '../../config'
import { Utils } from '../../types'
import { Contract } from '../base'

export class TensetXInfinityVaultBridgeExchangerContract extends Contract {
  constructor(chain: Utils.Network) {
    super(
      config.tensetX.infinityVaultBridgeExchanger[chain]!,
      chain,
      tensetXInfinityVaultBridgeExchangerAbi
    )
  }

  public async exchange(
    network: Utils.Network,
    depositId: number,
    amount: BigNumber,
    overrides: Utils.ContractMethodOverrides = {}
  ) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.exchange(
        network,
        depositId,
        amount,
        overrides
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
