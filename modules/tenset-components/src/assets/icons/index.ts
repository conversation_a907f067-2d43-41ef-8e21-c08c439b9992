export interface AssetProps {
  className?: string
  size?: number
  color?: string
  idPrefix?: string | number
  /**
   * Why idPrefix? If you found better solution feel free to change it
   * @see https://github.com/exogen/babel-plugin-inline-react-svg/pull/1
   */
}

export { default as General } from '../icons/general.svg'
export { default as Job } from '../icons/job.svg'
export { default as Airdrop } from './airdrops.svg'
export { default as AllGems } from './all-gems.svg'
export { default as AutomaticSwapback } from './automatic-swapback.svg'
export { default as BaseScanner } from './base-scanner.svg'
export { default as BinanceLogo } from './binance.svg'
export { default as BitcoinLogo } from './bitcoin.svg'
export { default as BitgetLogo } from './bitget.svg'
export { default as Blur } from './blur.svg'
export { default as BnbChain } from './bnb-chain.svg'
export { default as BscScanner } from './bsc-scanner.svg'
export { default as BurnTokens } from './burn-tokens.svg'
export { default as Business } from './business.svg'
export { default as BuyNftFromMarket } from './buy-nft-from-market.svg'
export { default as Buyback } from './buyback.svg'
export { default as ChevronDown16 } from './chevron-down-16.svg'
export { default as ChevronDown24 } from './chevron-down-24.svg'
export { default as ChevronRight16 } from './chevron-right-16.svg'
export { default as ChevronRight24 } from './chevron-right-24.svg'
export { default as ChevronUp16 } from './chevron-up-16.svg'
export { default as ChevronUp24 } from './chevron-up-24.svg'
export { default as ChevronUp } from './chevron-up.svg'
export { default as Clock } from './clock.svg'
export { default as CoinGeckoMonoLogo } from './coin-gecko-mono.svg'
export { default as CoinGeckoLogo } from './coin-gecko.svg'
export { default as CoinWLogo } from './coinw.svg'
export { default as ConnectWallet } from './connect-wallet.svg'
export { default as Discord } from './discord.svg'
export { default as Done } from './done.svg'
export { default as Duplicate16 } from './duplicate-16.svg'
export { default as Duplicate24 } from './duplicate-24.svg'
export { default as DuplicateFilled } from './duplicate-filled.svg'
export { default as ErrorAlert } from './error-alert.svg'
export { default as EthScanner } from './eth-scanner.svg'
export { default as EtherumLogo } from './etherum.svg'
export { default as EverdomeLogo } from './everdome.svg'
export { default as Exit24 } from './exit-24.svg'
export { default as FameLogo } from './fame.svg'
export { default as GateLogo } from './gate.svg'
export { default as Gear } from './gear.svg'
export { default as GemTglp } from './gem-tglp.svg'
export { default as GemTpl } from './gem-tpl.svg'
export { default as HeroLogo } from './hero.svg'
export { default as InfoAlert } from './info-alert.svg'
export { default as InfoCircleIcon } from './info-circle.svg'
export { default as InfoIcon } from './info.svg'
export { default as IPhone } from './iphone.svg'
export { default as KakaoTalkMonoLogo } from './kakao-talk-mono.svg'
export { default as KakaoTalkLogo } from './kakao-talk.svg'
export { default as KangaLogo } from './kanga.svg'
export { default as Lamborghini } from './lamborghini.svg'
export { default as LargeBudget } from './large-budget.svg'
export { LifetimeAccess } from './lifetime-access.svg'
export { default as LineMonoLogo } from './line-mono.svg'
export { default as LineLogo } from './line.svg'
export { default as LinkOut16 } from './link-out-16.svg'
export { default as LinkOut24 } from './link-out-24.svg'
export { default as LinkOut } from './link-out.svg'
export { default as LinkedinMonoLogo } from './linkedin-mono.svg'
export { default as LinkedinLogo } from './linkedin.svg'
export { default as Loading2 } from './loading2'
export { default as Lock } from './lock.svg'
export { default as LogoIcon } from './logo.svg'
export { default as Mail } from './mail.svg'
export { default as ManualSwapback } from './manual-swapback.svg'
export { default as MarketingDS } from './marketing-design-solution.svg'
export { default as MarketingOptimize } from './marketing-optimize.svg'
export { default as MarketingUP } from './marketing-understand-problem.svg'
export { default as MediumBudget } from './medium-budget.svg'
export { default as MetaMaskLogo } from './metamask.svg'
export { default as MmproLogo } from './mmpro.svg'
export { default as NftLaunchpad } from './nft-launchpad.svg'
export { default as NftMarketplace } from './nft-marketplace.svg'
export { default as OKXLogo } from './okx.svg'
export { default as OpenSeaLogo } from './open-sea.svg'
export { default as PancakeLogo } from './pancake-swap.svg'
export { default as PurchaseTenset } from './purchase-tenset.svg'
export { default as ReducedSupply } from './reduced-supply.svg'
export { default as Refund } from './refund.svg'
export { default as Rolex } from './rolex.svg'
export { default as SatoshiIslandLogo } from './satoshi-island.svg'
export { default as ScAuditAI } from './sc-audit-ai.svg'
export { default as ScAuditModel } from './sc-audit-model.svg'
export { default as ScAuditsProcessing } from './sc-audits-processing.svg'
export { default as ScAudits } from './sc-audits.svg'
export { default as ScDevelopmentCrosschain } from './sc-development-crosschain.svg'
export { default as ScDevelopmentTokens } from './sc-development-tokens.svg'
export { default as ScDevelopmentVesting } from './sc-development-vesting.svg'
export { default as ScDevelopment } from './sc-development.svg'
export { default as Security } from './security.svg'
export { default as Selection } from './selection.svg'
export { default as Subscription } from './subscription.svg'
export { default as SuccessAlert } from './success-alert.svg'
export { default as Success } from './success.svg'
export { default as Supply } from './supply.svg'
export { default as TelegramAnnouncements } from './telegram-announcements.svg'
export { default as TelegramCommunity } from './telegram-community.svg'
export { default as TelegramMonoLogo } from './telegram-mono.svg'
export { default as TelegramOutline } from './telegram-outline.svg'
export { default as TelegramLogo } from './telegram.svg'
export { default as TensetLogoWithLabelMono } from './tenset-logo-with-label-mono.svg'
export { default as TensetLogoWithLabel } from './tenset-logo-with-label.svg'
export { default as TensetLogo } from './tenset.svg'
export { TglpLogotype } from './tglp-logotype.svg'
export { default as Tglp } from './tglp.svg'
export { default as Tiers } from './tiers.svg'
export { default as TradableVesting } from './tradable-vesting.svg'
export { default as TransactionFee } from './transaction-fee.svg'
export { default as TransferCompleted } from './transfer-completed.svg'
export { default as Transfer } from './transfer.svg'
export { default as TwitterMonoLogo } from './twitter-mono.svg'
export { default as TwitterLogo } from './twitter.svg'
export { default as UiUx } from './ui.svg'
export { default as Verification } from './verification.svg'
export { default as Voting } from './voting.svg'
export { default as WalletConnectLogo } from './wallet-connect.svg'
export { default as Wallet } from './wallet.svg'
export { default as Website } from './website.svg'
export { default as WhaleHunt } from './whale-hunt.svg'
export { YearlyAccess } from './yearly-access.svg'
export { default as YoutubeMonoLogo } from './youtube-mono.svg'
export { default as YoutubeLogo } from './youtube.svg'
export { default as ZealyLogo } from './zealy.svg'
export { default as BlockchainSecurity } from './blockchain-security.svg'
export { default as ExclusiveOpportunity } from './exclusive-opportunity.svg'
export { default as HigherReturn } from './higher-return.svg'
