import { AssetProps } from './index'

export default function PurchaseTenset({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M80.4806 64.3098C80.2791 63.9376 79.814 63.7825 79.4265 63.9841C79.0544 64.1857 78.8994 64.651 79.1009 65.0388L80.4651 67.5824C80.6046 67.846 80.8681 67.9856 81.1472 67.9856C81.2712 67.9856 81.3952 67.9546 81.5192 67.8926C81.8913 67.6909 82.0463 67.2256 81.8448 66.8379L80.4806 64.2943V64.3098ZM94.262 43.1391L97.1454 43.3562C97.1454 43.3562 97.1764 43.3562 97.2074 43.3562C97.6104 43.3562 97.9515 43.046 97.9825 42.6428C98.0135 42.2085 97.688 41.8518 97.2694 41.8053L94.386 41.5881C93.9674 41.5571 93.5799 41.8828 93.5489 42.3016C93.5179 42.7358 93.8434 43.0926 94.262 43.1391ZM8.42671 119.493C9.01579 119.493 9.49635 119.013 9.49635 118.423C9.49635 117.834 9.01579 117.353 8.42671 117.353C7.83763 117.353 7.35706 117.834 7.35706 118.423C7.35706 119.013 7.83763 119.493 8.42671 119.493ZM8.42671 117.942C8.69024 117.942 8.90727 118.159 8.90727 118.423C8.90727 118.687 8.69024 118.904 8.42671 118.904C8.16317 118.904 7.94614 118.687 7.94614 118.423C7.94614 118.159 8.16317 117.942 8.42671 117.942ZM115.438 104.976C113.159 105.1 109.609 105.317 105.687 105.566C107.253 104.201 108.245 101.952 109.299 99.5944L113.19 90.9555C113.686 89.8543 113.531 88.629 112.772 87.5744C111.857 86.3026 110.276 85.6046 108.741 85.7442C107.671 85.8528 105.656 86.5042 104.245 89.6061C101.315 96.1202 99.7497 98.5397 96.2928 99.9821C95.6572 100.246 94.7271 100.618 93.6729 101.037C94.2465 99.5634 94.5565 97.8263 94.8511 95.9961L96.4788 86.6593C96.6803 85.4805 96.2308 84.3173 95.2231 83.4798C94.0294 82.4872 92.3087 82.1925 90.867 82.7198C89.8439 83.0921 88.0611 84.2243 87.4721 87.5744C86.2474 94.6158 85.3328 97.3455 82.2633 99.6719C82.0153 99.858 81.8758 99.9666 81.5502 100.199C81.3332 100.029 81.1007 99.8891 80.8681 99.7805C77.5817 98.2916 68.2959 95.2051 58.4521 92.584C52.2667 90.94 48.2362 91.9791 44.9962 92.8321C42.6864 93.437 40.8727 93.9023 38.8884 93.2819C33.7262 91.6534 22.9057 88.3498 22.9057 88.3498C22.9057 88.3498 22.9057 88.3498 22.8902 88.3498L16.3483 86.5662C15.9452 86.4577 15.5112 86.6903 15.4027 87.1091C15.2942 87.5278 15.5267 87.9466 15.9452 88.0552L20.5029 89.2959C17.821 91.8861 15.3872 99.2221 14.054 106.713C13.6974 108.668 13.4339 110.637 13.2479 112.545C13.2479 112.545 13.2479 112.56 13.2479 112.576C12.7363 117.849 12.9068 122.611 14.023 125.123L4.89222 123.696C4.47367 123.634 4.07061 123.929 4.0086 124.348C3.94659 124.767 4.22563 125.17 4.65969 125.232L15.9297 126.984C15.9297 126.984 16.0072 126.984 16.0538 126.984C16.1313 126.984 16.1933 127 16.2708 127C19.5572 127 22.1926 118.78 23.1847 115.244C31.6024 116.919 43.3685 119.338 47.9726 120.781C50.484 121.572 54.7006 121.99 59.1962 121.99C62.0486 121.99 64.994 121.82 67.7068 121.479C70.0167 121.184 75.768 120.393 82.6199 119.447C96.1222 117.586 114.616 115.042 118.368 114.67C121.592 114.344 123.158 111.863 122.987 109.691C122.786 107.132 120.383 104.666 115.484 104.93L115.438 104.976ZM96.8663 101.425C101.021 99.6874 102.804 96.5235 105.641 90.242C106.447 88.4584 107.594 87.4193 108.88 87.2797C109.873 87.1866 110.911 87.6519 111.5 88.4739C111.748 88.8306 112.12 89.5286 111.764 90.3196L107.873 98.9585C106.555 101.936 105.424 104.495 103.346 105.147C102.866 105.302 102.184 105.535 101.393 105.814C93.9054 106.279 86.1234 106.791 83.1935 106.977C83.24 106.853 83.2865 106.729 83.333 106.589C87.4256 105.069 94.2465 102.51 96.8663 101.409V101.425ZM83.2555 100.866C86.852 98.1365 87.7976 94.6468 88.9758 87.8535C89.3013 85.9303 90.1539 84.6275 91.3786 84.1932C92.3242 83.852 93.4404 84.0381 94.2155 84.6896C94.541 84.9687 95.0836 85.5581 94.9286 86.4111L93.3009 95.748C92.8978 98.152 92.5102 100.432 91.5181 101.843C91.5181 101.843 91.5181 101.874 91.5026 101.874C88.7897 102.913 85.7048 104.061 83.581 104.852C83.581 103.58 83.209 102.34 82.5734 101.378C82.8369 101.192 82.9764 101.083 83.2555 100.866ZM16.1468 125.496C14.9376 125.278 14.0695 120.765 14.6586 113.646C15.8987 113.879 18.4256 114.36 21.6035 114.995C19.5262 122.44 17.1854 125.697 16.1313 125.496H16.1468ZM118.166 113.165C114.384 113.538 95.8742 116.081 82.3564 117.942C75.5044 118.888 69.7531 119.679 67.4588 119.974C60.8084 120.843 52.2512 120.563 48.3912 119.338C43.539 117.818 31.2613 115.29 22.7817 113.615C19.0922 112.886 16.1313 112.328 14.8291 112.08C14.9996 110.482 15.2321 108.792 15.5577 106.993C17.4955 96.1667 20.9679 89.6216 22.5026 89.8698C23.2622 90.1024 33.4316 93.2199 38.3923 94.7709C40.7951 95.5308 42.9034 94.9725 45.3528 94.3366C48.5772 93.4991 52.2357 92.553 58.018 94.0884C68.7765 96.9577 77.4577 99.9666 80.1861 101.207C81.4262 101.766 82.2478 103.766 81.9688 105.566C81.8138 106.62 81.1472 108.435 78.5428 108.761C76.3725 109.024 74.5278 108.668 72.2025 108.187C67.7223 107.272 61.5835 106.046 48.6547 108.621C48.2362 108.699 47.9571 109.117 48.0501 109.536C48.1277 109.955 48.5462 110.234 48.9648 110.141C61.599 107.613 67.5518 108.823 71.9079 109.707C73.7217 110.079 75.3339 110.405 77.0856 110.405C77.6282 110.405 78.1863 110.374 78.7444 110.296C80.2016 110.11 81.3952 109.49 82.2323 108.559C84.1081 108.435 107.516 106.93 115.531 106.481C119.391 106.279 121.267 108.001 121.406 109.815C121.515 111.289 120.445 112.917 118.182 113.134L118.166 113.165ZM72.9311 37.6176L67.8619 32.546C67.2728 31.9566 66.2651 31.9721 65.6916 32.546L60.6224 37.6176C60.3278 37.9123 60.1728 38.3001 60.1728 38.7033C60.1728 39.1066 60.3278 39.4943 60.6224 39.789V54.0734C60.3433 54.3681 60.1728 54.7558 60.1728 55.1591C60.1728 55.5624 60.3278 55.9501 60.6224 56.2448L65.6916 61.3164C65.9861 61.6111 66.3737 61.7662 66.7767 61.7662C67.1798 61.7662 67.5673 61.6111 67.8619 61.3164L72.9311 56.2448C73.2256 55.9501 73.3806 55.5624 73.3806 55.1591C73.3806 54.7558 73.2256 54.3681 72.9311 54.0889V39.8045C73.2101 39.5098 73.3806 39.1221 73.3806 38.7188C73.3806 38.3156 73.2256 37.9278 72.9311 37.6331V37.6176ZM66.7767 60.2153L61.7075 55.1591C62.0021 54.8644 62.1571 54.4767 62.1571 54.0734V39.789C62.1571 39.3857 62.0021 38.998 61.7075 38.7188L66.7612 33.6472L71.8304 38.7033C71.5359 38.998 71.3809 39.3857 71.3809 39.789V54.0734C71.3809 54.4767 71.5359 54.8644 71.8304 55.1436L66.7767 60.2153ZM110.508 9.99487H112.849C113.283 9.99487 113.624 9.65366 113.624 9.21939C113.624 8.78512 113.283 8.4439 112.849 8.4439H110.508C110.074 8.4439 109.733 8.78512 109.733 9.21939C109.733 9.65366 110.074 9.99487 110.508 9.99487ZM115.949 15.4388C116.384 15.4388 116.725 15.0976 116.725 14.6633V12.3213C116.725 11.8871 116.384 11.5458 115.949 11.5458C115.515 11.5458 115.174 11.8871 115.174 12.3213V14.6633C115.174 15.0976 115.515 15.4388 115.949 15.4388ZM115.949 6.89293C116.384 6.89293 116.725 6.55172 116.725 6.11745V3.77548C116.725 3.34121 116.384 3 115.949 3C115.515 3 115.174 3.34121 115.174 3.77548V6.11745C115.174 6.55172 115.515 6.89293 115.949 6.89293ZM60.3588 75.8645C62.3896 76.2523 64.5599 76.4384 66.7767 76.4384C68.3269 76.4384 69.8771 76.3298 71.3498 76.1282C89.3323 73.8638 102.881 58.4627 102.881 40.2853C102.881 20.3864 86.6814 4.17874 66.7767 4.17874C46.872 4.17874 30.6568 20.3864 30.6568 40.3008C30.6568 57.8268 43.1515 72.7936 60.3588 75.8645ZM66.7767 5.72971C85.8288 5.72971 101.331 21.2394 101.331 40.3008C101.331 57.6872 88.3557 72.4369 71.1483 74.6083C67.6913 75.0735 63.9398 74.965 60.6224 74.3446C44.1436 71.3978 32.1915 57.0823 32.1915 40.3008C32.207 21.2394 47.7091 5.72971 66.7767 5.72971ZM92.6808 49.8858L95.4091 50.8009C95.4867 50.8319 95.5797 50.8474 95.6572 50.8474C95.9827 50.8474 96.2773 50.6458 96.3858 50.3201C96.5253 49.9168 96.3083 49.467 95.9052 49.343L93.1768 48.4279C92.7738 48.2883 92.3242 48.5054 92.2002 48.9087C92.0607 49.3119 92.2777 49.7617 92.6808 49.8858ZM119.05 9.99487H121.391C121.825 9.99487 122.166 9.65366 122.166 9.21939C122.166 8.78512 121.825 8.4439 121.391 8.4439H119.05C118.616 8.4439 118.275 8.78512 118.275 9.21939C118.275 9.65366 118.616 9.99487 119.05 9.99487ZM50.422 16.664C50.7785 16.4159 50.856 15.9351 50.6235 15.5784C50.3755 15.2216 49.8949 15.1441 49.5383 15.3767C43.0895 19.859 38.6714 26.5282 37.1056 34.19C37.0126 34.6088 37.2917 35.0275 37.7102 35.1051C37.7567 35.1051 37.8187 35.1051 37.8652 35.1051C38.2218 35.1051 38.5473 34.8569 38.6249 34.4847C40.1131 27.2261 44.2986 20.8827 50.422 16.6485V16.664ZM89.4873 56.0276L91.9057 57.5941C92.0297 57.6717 92.1847 57.7182 92.3242 57.7182C92.5723 57.7182 92.8203 57.5941 92.9753 57.3615C93.2078 57.0048 93.1148 56.524 92.7428 56.2913L90.3244 54.7248C89.9679 54.4922 89.4873 54.5852 89.2548 54.9575C89.0223 55.3142 89.1153 55.795 89.4873 56.0276ZM86.821 63.3172C86.976 63.4878 87.1775 63.5654 87.3945 63.5654C87.5806 63.5654 87.7666 63.5033 87.9216 63.3637C88.2317 63.069 88.2627 62.5882 87.9681 62.2625L86.0149 60.1377C85.7203 59.8275 85.2397 59.7965 84.9142 60.0912C84.6042 60.3859 84.5732 60.8667 84.8677 61.1924L86.821 63.3172ZM52.1582 30.5452L57.9405 24.7446L58.4831 25.2719C58.7621 25.5666 59.1652 25.7217 59.5682 25.7217H73.9852C74.3883 25.7217 74.7913 25.5511 75.0704 25.2719L75.5819 24.7291L81.3952 30.5452C81.5502 30.7003 81.7518 30.7779 81.9378 30.7779C82.1238 30.7779 82.3409 30.7003 82.4804 30.5452C82.7904 30.235 82.7904 29.7542 82.4804 29.444L76.6826 23.6279C76.0935 23.0385 75.0859 23.054 74.5123 23.6279L73.9852 24.1552H59.5837L59.0411 23.6279C58.4676 23.0385 57.4444 23.054 56.8709 23.6279L51.0576 29.444C50.7475 29.7542 50.7475 30.235 51.0576 30.5452C51.3676 30.8554 51.8482 30.8554 52.1582 30.5452ZM56.8709 13.2674C56.9639 13.2674 57.0414 13.2674 57.1344 13.2209C58.9946 12.554 60.9324 12.0887 62.9012 11.825C63.3197 11.763 63.6298 11.3752 63.5678 10.9565C63.5058 10.5377 63.1182 10.212 62.6997 10.2896C60.6379 10.5687 58.5916 11.065 56.6228 11.763C56.2198 11.9026 56.0027 12.3523 56.1578 12.7556C56.2663 13.0813 56.5763 13.2674 56.8864 13.2674H56.8709Z"
        fill={color ?? 'url(#paint0_linear_1060_1143)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1060_1143"
          x1="35.2144"
          y1="143.006"
          x2="116.102"
          y2="2.97215"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
