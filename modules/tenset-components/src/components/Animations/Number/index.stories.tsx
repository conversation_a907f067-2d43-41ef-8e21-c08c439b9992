import { Meta, StoryFn } from '@storybook/react'
import { useEffect, useState } from 'react'

import { CryptoCurrencyFormatter, CurrencyFormatter } from '../../Formatters'

import { AnimatedNumber } from '.'

type AnimatedNumberType = typeof AnimatedNumber

export default {
  title: 'Components/Animations/Number',
  component: AnimatedNumber,
} as Meta<AnimatedNumberType>

const Template: StoryFn<AnimatedNumberType> = _arguments => {
  const [value, setValue] = useState(0)

  useEffect(() => {
    const intervalId = setInterval(() => {
      setValue((previousValue: number) => previousValue + 1000)
    }, 2000)

    return () => {
      clearInterval(intervalId)
    }
  }, [])

  return <AnimatedNumber {..._arguments} value={value} />
}

export const Preview = {
  render: Template,
}

export const Currency = {
  render: Template,

  args: {
    formatter: CurrencyFormatter,
  },
}

export const CryptoCurrency = {
  render: Template,

  args: {
    formatter: CryptoCurrencyFormatter,
  },
}
