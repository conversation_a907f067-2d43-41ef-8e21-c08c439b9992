import { useFetcher, useNavigate } from '@remix-run/react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { ConnectWallet } from '~/components/connect-wallet'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { Button, ButtonVariant } from '~/tenset-components'
import { useWallet } from '~/tenset-web3'

export function MySubscriptionButton() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])

  const fetcher = useFetcher({ key: 'root' })

  const { wallet } = useWallet()
  const navigate = useNavigate()

  const localizePathname = useLocalizePathname()

  const GLP_PANEL_URL = localizePathname('/gem-launch-platform/panel')

  const [didConnect, setDidConnect] = useState(false)

  // TODO: TMP wallet & chain change event via root fetcher
  // first we need to check, if walletAddress has been updated in localStorage (see routes/api-wallet.connect.ts),
  // because we are referencing it in the loader (if wallet<PERSON>dd<PERSON> is null, we are redirecting to "/gem-launch-platform")
  const [revalidatable, setRevalidatable] = useState(false)
  useEffect(() => {
    if (
      fetcher.state === 'submitting' &&
      fetcher.formAction.includes('/api/wallet')
    )
      setRevalidatable(true)

    if (fetcher.state !== 'idle' || !revalidatable) return

    setRevalidatable(false)

    if (didConnect) navigate(GLP_PANEL_URL)
  }, [didConnect, fetcher.state])

  return wallet?.isConnected ? (
    <Button variant={ButtonVariant.Secondary} to={GLP_PANEL_URL}>
      {t('hero.action.my-subscription')}
    </Button>
  ) : (
    <ConnectWallet
      buttonConnectLabel={t('hero.action.my-subscription')}
      variant={ButtonVariant.Secondary}
      onConnect={() => {
        setDidConnect(true)
      }}
    />
  )
}
