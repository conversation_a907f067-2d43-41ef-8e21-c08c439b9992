import { useTranslation } from 'react-i18next'

import type { PostDataType } from '~/api/types'
import { getPostImage } from '~/utils'
import { Image, HrefType } from '~/tenset-components'
import Showcase from '~/components/blocks/showcase'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { Namespace } from '~/i18n'

interface BurnsLatestBurnProps {
  latestBurnData: PostDataType
}

export function BurnsLatestBurn({ latestBurnData }: BurnsLatestBurnProps) {
  const { t } = useTranslation([Namespace.BURNS])
  const localizePathname = useLocalizePathname()

  const {
    attributes: { title, slug, cover },
  } = latestBurnData

  const { url: postImage } = getPostImage(cover)

  return (
    <Showcase
      title={t('latest-burn.title')}
      image={<Image src={postImage} alt={title} />}
      link={{
        type: HrefType.Internal,
        url: localizePathname(`/news/${slug}`),
      }}
    />
  )
}
