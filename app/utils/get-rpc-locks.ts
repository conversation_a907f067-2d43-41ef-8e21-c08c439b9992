import { BigNumber } from 'ethers'
import { formatEther } from 'ethers/lib/utils'

import type { GlpLock } from '~/api/types'
import type { GlpMembershipContract } from '~/tenset-web3'
import { cutDecimals } from '~/utils'

interface GetRpcLocks {
  walletAddress: string
  glpMembershipContract: GlpMembershipContract
  membershipBalance: BigNumber
}

export const getRpcLocks = async ({
  walletAddress,
  glpMembershipContract,
  membershipBalance,
}: GetRpcLocks): Promise<GlpLock[]> => {
  return new Promise(async resolve => {
    const tokenIndexCalls = []
    for (
      let tokenId = BigNumber.from(0);
      tokenId.lt(membershipBalance);
      tokenId = tokenId.add(1)
    ) {
      tokenIndexCalls.push(
        glpMembershipContract.tokenOfOwnerByIndex(walletAddress, tokenId)
      )
    }

    const tokenIds = await Promise.all(tokenIndexCalls)

    const attributeCalls = tokenIds.map(tokenId =>
      glpMembershipContract.getAttributes(tokenId)
    )

    const attributesResults = await Promise.all(attributeCalls)

    resolve(
      attributesResults.map((attributes, index) => {
        const { amountToWithdraw, endTs, tokensForExtension } = attributes

        return {
          id: tokenIds[index].toNumber(),
          amount: cutDecimals(
            formatEther(amountToWithdraw.add(tokensForExtension))
          ),
          txHash: '',
          timestamps: {
            unlock: endTs * 1000,
          },
          membershipAttributes: { ...attributes },
        }
      })
    )
  })
}
