# tenset.io (new)

## Development

- Run the following command

```sh
  yarn
```

- Fill the values of `.env` if necessary

## Working with translations

**Using translations in route / component:**

To use translations in your route or component you have to use hook `useTranslation` and pass as
parameter array of namespaces you want to use. First namespace in array is using as default. If you
want to use translations from non default namespace you have to prefix it.

```jsx
import { useTranslation } from 'react-i18next'

export default function Component() {
  const { t } = useTranslation([Namespace.HOMEPAGE, Namespace.COMMON])

  return (
    <h1>{t('test-translations')}</h1>
  <h1>{t('common:welcome-test')}</h1>
)
}
```

**Using translations in loader:**

```jsx
import { detectLocale } from '~/i18n/detect-locale'
import { i18next } from '~/i18n'
import type { LoaderFunctionArgs } from '@remix-run/node'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.HOMEPAGE)

  const title = t('test-translations')

  return json({
    title,
  })
}
```

**Tips & Tricks:**

Don't use the default value for translations. It will be easier to locate potentially missing
translation keys.

| ✅ Do this                | ❌ Don't do this                          |
|--------------------------|------------------------------------------|
| `t('test-translations')` | `t('test-translations', 'Some default')` |

**Synchronization of translations with locize:**

We're using Locize for translations. To push out new translation keys or download already translated
ones use `yarn locize:sync`

You can read more about
locize-cli [here](https://github.com/locize/locize-cli#synchronize-locize-with-your-repository-or-any-other-local-directory).

But simplifying just do `yarn locize:download` before adding new key/namespace and
then `yarn locize:sync` to synchronize keys/namespaces you added.
