import { AssetProps } from './index'

export default function Wallet({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2232_135)">
        <path
          d="M101.791 74.9245V48.6748C101.78 47.2224 100.644 46.037 99.2291 46.037H79.3572C81.8868 43.7196 83.4838 40.3983 83.4838 36.6926C83.4838 29.6977 77.7924 24.0377 70.7718 24.0377C63.7513 24.0377 58.0705 29.6977 58.0705 36.6926C58.0705 40.3983 59.6569 43.7196 62.1971 46.037H55.9269C54.2119 43.3351 53.2151 40.1314 53.2151 36.6926C53.2151 31.1928 55.7554 26.291 59.7426 23.0765C58.2313 21.8484 56.5164 20.8766 54.6514 20.2145C52.7864 19.5417 50.782 19.1786 48.6812 19.1786C38.9704 19.1786 31.1031 27.0172 31.1031 36.6926C31.1031 40.1314 32.0892 43.3351 33.8041 46.037H29.506C27.1909 46.037 25.3151 44.2322 25.1865 41.9575V107.827C25.1865 111.234 27.9626 114 31.3818 114H99.3042C100.676 114 101.78 112.889 101.791 111.533V88.9891H107V74.9245H101.791ZM46.1303 103.299C44.4689 103.299 43.1184 101.954 43.1184 100.298C43.1184 98.6432 44.4689 97.2976 46.1303 97.2976C46.9663 97.2976 47.7166 97.6287 48.2739 98.1733C48.8098 98.718 49.1528 99.4655 49.1528 100.298C49.1528 101.954 47.8023 103.299 46.1303 103.299ZM51.7681 69.6382C50.1068 69.6382 48.7563 68.2926 48.7563 66.6374C48.7563 64.9821 50.1068 63.6365 51.7681 63.6365C52.6042 63.6365 53.3544 63.9675 53.9011 64.5122C54.4477 65.0568 54.78 65.8044 54.78 66.6374C54.78 68.2926 53.4295 69.6382 51.7681 69.6382ZM62.5294 69.4994C62.5294 67.8441 63.8799 66.4985 65.5412 66.4985C66.3666 66.4985 67.1276 66.8296 67.6742 67.3742C68.2101 67.9189 68.5531 68.6664 68.5531 69.4994C68.5531 71.1654 67.2026 72.511 65.5412 72.511C63.8799 72.511 62.5294 71.1654 62.5294 69.4994ZM70.0966 93.5599C68.4352 93.5599 67.0847 92.2143 67.0847 90.559C67.0847 88.9037 68.4352 87.5474 70.0966 87.5474C70.9326 87.5474 71.6829 87.8891 72.2402 88.4231C72.7762 88.9784 73.1191 89.726 73.1191 90.559C73.1191 92.2143 71.7686 93.5599 70.0966 93.5599ZM99.8508 84.0873C98.6718 84.0873 97.7071 83.1262 97.7071 81.9515C97.7071 80.7767 98.6718 79.8263 99.8508 79.8263C100.44 79.8263 100.976 80.0612 101.362 80.4457C101.748 80.8301 101.984 81.3641 101.984 81.9515C101.984 83.1262 101.03 84.0873 99.8508 84.0873Z"
          fill="#15121A"
        />
        <path
          d="M79.3358 28.4375C79.443 28.5977 79.6145 28.6831 79.786 28.6831C79.8825 28.6831 79.9897 28.6511 80.0754 28.5977C80.3219 28.4375 80.397 28.1065 80.2362 27.8608C79.2715 26.3871 78.0389 25.1269 76.5705 24.1338C76.324 23.9736 75.9917 24.027 75.8309 24.2726C75.6702 24.5182 75.7237 24.8493 75.9703 25.0201C77.3208 25.9279 78.4462 27.0813 79.3358 28.4482V28.4375ZM58.167 28.6191C58.2528 28.6725 58.36 28.7045 58.4564 28.7045C58.6279 28.7045 58.7994 28.6191 58.9066 28.4589C59.7962 27.1026 60.9216 25.9492 62.2722 25.0308C62.5187 24.86 62.583 24.5289 62.4115 24.2833C62.24 24.0377 61.9077 23.9736 61.6612 24.1444C60.1928 25.1376 58.9709 26.3978 58.0062 27.8715C57.8455 28.1171 57.9205 28.4482 58.167 28.6084V28.6191ZM81.4152 38.3265C81.4152 38.3265 81.4902 38.3372 81.5331 38.3372C81.7796 38.3372 82.0047 38.1664 82.0583 37.9207C82.262 36.981 82.3691 36.0198 82.3691 35.048C82.3691 34.2577 82.2941 33.4675 82.1655 32.7092C82.1119 32.4209 81.8332 32.2287 81.5438 32.2821C81.2544 32.3355 81.0615 32.6131 81.1151 32.9015C81.2437 33.6063 81.308 34.3325 81.308 35.0587C81.308 35.9451 81.2116 36.8421 81.0186 37.6965C80.9543 37.9848 81.1365 38.2731 81.4259 38.3372L81.4152 38.3265ZM66.6024 23.1726C66.6024 23.1726 66.6774 23.1726 66.7096 23.1726C68.2745 22.8523 69.9465 22.8523 71.5221 23.1726C71.8115 23.226 72.0902 23.0445 72.1545 22.7561C72.2081 22.4678 72.0259 22.1901 71.7365 22.1261C70.0216 21.7843 68.2102 21.7843 66.5059 22.1261C66.2165 22.1795 66.0236 22.4678 66.0879 22.7561C66.1415 23.0124 66.3666 23.1833 66.6131 23.1833L66.6024 23.1726ZM56.7093 32.3034C56.4199 32.2393 56.1413 32.4423 56.0877 32.7413C55.9483 33.4995 55.884 34.2898 55.884 35.0587C55.884 36.0305 55.9912 37.0023 56.2056 37.9528C56.2592 38.1984 56.4842 38.3693 56.7308 38.3693C56.7736 38.3693 56.8058 38.3693 56.8487 38.3586C57.1381 38.2945 57.3203 38.0062 57.256 37.7178C57.063 36.8528 56.9666 35.9557 56.9666 35.0587C56.9666 34.3432 57.0309 33.6277 57.1595 32.9228C57.2131 32.6345 57.0202 32.3568 56.72 32.3034H56.7093ZM82.8729 100.363C82.7764 100.523 82.7764 100.736 82.8729 100.896L86.2814 106.77C86.3778 106.93 86.5493 107.037 86.7422 107.037C86.9352 107.037 87.1067 106.93 87.2031 106.77L90.6116 100.896C90.708 100.736 90.708 100.523 90.6116 100.363C90.5151 100.202 90.3436 100.096 90.1507 100.096H83.3338C83.1409 100.096 82.9694 100.202 82.8729 100.363ZM89.2182 101.163L86.7422 105.435L84.2663 101.163H89.2182ZM98.2002 82.9767C99.6793 82.9767 100.869 81.7806 100.869 80.3068C100.869 78.8331 99.6686 77.6477 98.2002 77.6477C96.7318 77.6477 95.5313 78.8438 95.5313 80.3068C95.5313 81.7699 96.7318 82.9767 98.2002 82.9767ZM98.2002 78.7156C99.0791 78.7156 99.7972 79.4311 99.7972 80.3068C99.7972 81.1825 99.0791 81.9087 98.2002 81.9087C97.3213 81.9087 96.6032 81.1932 96.6032 80.3068C96.6032 79.4205 97.3213 78.7156 98.2002 78.7156ZM78.6391 57.3677H80.3755C80.6756 57.3677 80.9114 57.1328 80.9114 56.8338C80.9114 56.5347 80.6756 56.2998 80.3755 56.2998H78.6391C78.339 56.2998 78.1032 56.5347 78.1032 56.8338C78.1032 57.1328 78.339 57.3677 78.6391 57.3677ZM105.36 72.7459H100.687V47.0302C100.687 45.2788 99.2935 43.8584 97.6 43.8584H94.6846V38.2518C94.6846 36.6072 93.3341 35.2509 91.6727 35.2509H87.2782C87.2782 35.1868 87.2782 35.1228 87.2782 35.0587C87.2782 25.0949 79.1429 17 69.1534 17C64.9946 17 61.1682 18.4203 58.1027 20.7805C54.9408 18.3456 51.0393 17 47.0306 17C37.0411 17 28.9165 25.0949 28.9165 35.0587C28.9165 35.1228 28.9165 35.1868 28.9165 35.2509H27.8447C26.5478 35.2509 25.3259 35.7528 24.4148 36.6713C23.5038 37.5897 23 38.7964 23 40.0886V40.11V82.9126V82.9339C23 82.9339 23 82.9339 23 82.9446V106.183C23 107.977 23.6967 109.664 24.9722 110.924C26.2477 112.185 27.9305 112.889 29.7311 112.889H97.6643C99.3256 112.889 100.676 111.533 100.676 109.888V87.8785H105.349C105.649 87.8785 105.885 87.6435 105.885 87.3445V73.2799C105.885 72.9808 105.649 72.7459 105.349 72.7459H105.36ZM91.662 36.3188C92.7338 36.3188 93.602 37.1945 93.602 38.2518V43.8584H84.963C86.2706 41.541 87.0424 38.9673 87.2246 36.3188H91.6513H91.662ZM69.1534 18.0679C78.5534 18.0679 86.2063 25.6823 86.2063 35.0587C86.2063 38.177 85.3489 41.2206 83.7304 43.8691H78.9929C79.3251 43.506 79.636 43.1215 79.9146 42.7264C80.0861 42.4915 80.0325 42.1497 79.786 41.9789C79.5395 41.808 79.2072 41.8614 79.0357 42.107C78.5748 42.7478 78.0496 43.3351 77.4816 43.8691H60.7823C60.2142 43.3244 59.6783 42.7371 59.2281 42.0963C59.0567 41.8614 58.7244 41.7973 58.4779 41.9682C58.2421 42.139 58.1777 42.4701 58.3492 42.7157C58.6386 43.1109 58.9495 43.4953 59.2817 43.8584H54.5871C52.9686 41.21 52.1111 38.1664 52.1111 35.048C52.1111 25.6823 59.7641 18.0572 69.1641 18.0572L69.1534 18.0679ZM47.0413 18.0679C50.7285 18.0679 54.3191 19.2854 57.256 21.4746C53.4509 24.7852 51.0393 29.6443 51.0393 35.0587C51.0393 38.1557 51.8432 41.1886 53.3438 43.8691H32.4643C30.8459 41.2206 29.9884 38.177 29.9884 35.0587C29.9884 25.6823 37.6413 18.0679 47.0413 18.0679ZM25.1758 37.4188C25.894 36.7033 26.8372 36.3082 27.8554 36.3082H28.9594C29.1416 38.9673 29.9241 41.5303 31.221 43.8477H27.8554C26.8479 43.8477 25.894 43.4526 25.1758 42.7478C24.4684 42.0429 24.0718 41.1032 24.0718 40.11V40.0779C24.0718 39.0741 24.4684 38.1343 25.1758 37.4188ZM99.6043 109.888C99.6043 110.956 98.7254 111.821 97.6643 111.832H29.7419C28.2306 111.832 26.805 111.245 25.7439 110.177C24.6721 109.109 24.0826 107.699 24.0826 106.183V83.4679H43.9652V95.1724C42.2609 95.4287 40.9533 96.8918 40.9533 98.6539C40.9533 100.608 42.5503 102.189 44.5011 102.189C46.4518 102.189 48.0489 100.597 48.0489 98.6539C48.0489 96.8811 46.7412 95.4287 45.037 95.1724V82.9339C45.037 82.6349 44.8012 82.4 44.5011 82.4H24.0718V75.64H54.1048V88.9143C54.1048 89.2134 54.3406 89.4483 54.6407 89.4483H64.9625C65.2197 91.1463 66.6881 92.4492 68.4567 92.4492C70.4181 92.4492 72.0045 90.858 72.0045 88.9143C72.0045 86.9707 70.4074 85.3795 68.4567 85.3795C66.6774 85.3795 65.2197 86.6824 64.9625 88.3804H55.1766V75.64H63.9014C64.2015 75.64 64.4373 75.405 64.4373 75.106V71.3469C66.1415 71.0906 67.4492 69.6275 67.4492 67.8655C67.4492 65.9112 65.8521 64.3306 63.9014 64.3306C61.9506 64.3306 60.3536 65.9218 60.3536 67.8655C60.3536 69.6382 61.6612 71.0906 63.3654 71.3469V74.5721H37.7163V65.5267H46.6233C46.8806 67.2247 48.349 68.5276 50.1175 68.5276C52.079 68.5276 53.6653 66.9364 53.6653 64.9927C53.6653 63.0491 52.0683 61.4579 50.1175 61.4579C48.3383 61.4579 46.8806 62.7608 46.6233 64.4588H37.1804C36.8803 64.4588 36.6445 64.6937 36.6445 64.9927V74.5721H24.0718V43.0895C24.1897 43.2283 24.2862 43.3778 24.4255 43.5167C25.3473 44.4351 26.5585 44.937 27.8554 44.937H32.1535H60.5251C60.5251 44.937 60.5358 44.937 60.5465 44.937C60.5465 44.937 60.5465 44.937 60.5572 44.937H77.6531C77.6531 44.937 77.6745 44.937 77.6852 44.937C77.6959 44.937 77.7066 44.937 77.7174 44.937H84.0091C84.0091 44.937 84.0091 44.937 84.0198 44.937C84.0198 44.937 84.0198 44.937 84.0305 44.937H97.5785C98.6932 44.937 99.5936 45.8875 99.5936 47.0408V72.7566H85.6061C81.4152 72.7566 78.0175 76.1526 78.0175 80.3175C78.0175 84.4824 81.4259 87.8785 85.6061 87.8785H99.5936V109.888H99.6043ZM44.4904 96.1869C45.8516 96.1869 46.9663 97.2976 46.9663 98.6539C46.9663 100.01 45.8516 101.121 44.4904 101.121C43.1291 101.121 42.0144 100.01 42.0144 98.6539C42.0144 97.2976 43.1291 96.1869 44.4904 96.1869ZM65.9807 88.9143C65.9807 87.5474 67.0954 86.4474 68.4567 86.4474C69.8179 86.4474 70.9326 87.5581 70.9326 88.9143C70.9326 90.2706 69.8179 91.3813 68.4567 91.3813C67.0954 91.3813 65.9807 90.2706 65.9807 88.9143ZM61.4147 67.8655C61.4147 66.4985 62.5294 65.3985 63.8906 65.3985C65.2519 65.3985 66.3666 66.5092 66.3666 67.8655C66.3666 69.2217 65.2519 70.3324 63.8906 70.3324C62.5294 70.3324 61.4147 69.2217 61.4147 67.8655ZM47.6416 64.9927C47.6416 63.6258 48.7563 62.5258 50.1175 62.5258C51.4788 62.5258 52.5935 63.6365 52.5935 64.9927C52.5935 66.349 51.4788 67.4596 50.1175 67.4596C48.7563 67.4596 47.6416 66.349 47.6416 64.9927ZM104.824 86.8105H85.6168C82.0261 86.8105 79.1 83.8951 79.1 80.3175C79.1 76.74 82.0261 73.8245 85.6168 73.8245H104.824V86.8212V86.8105ZM76.3561 52.2951C76.056 52.2951 75.8202 52.53 75.8202 52.829V54.5591C75.8202 54.8581 76.056 55.093 76.3561 55.093C76.6562 55.093 76.892 54.8581 76.892 54.5591V52.829C76.892 52.53 76.6562 52.2951 76.3561 52.2951ZM76.3561 58.5852C76.056 58.5852 75.8202 58.8201 75.8202 59.1191V60.8492C75.8202 61.1482 76.056 61.3831 76.3561 61.3831C76.6562 61.3831 76.892 61.1482 76.892 60.8492V59.1191C76.892 58.8201 76.6562 58.5852 76.3561 58.5852ZM33.3218 51.6223C33.1289 51.2912 32.5822 51.2912 32.3893 51.6223L29.5275 56.5668C29.431 56.727 29.431 56.9405 29.5275 57.1007C29.624 57.2609 29.7955 57.3677 29.9884 57.3677H35.7227C35.9157 57.3677 36.0871 57.2609 36.1836 57.1007C36.2801 56.9405 36.2801 56.727 36.1836 56.5668L33.3218 51.6223ZM30.9209 56.2998L32.8609 52.9572L34.8009 56.2998H30.9209ZM74.0731 56.2998H72.3367C72.0366 56.2998 71.8008 56.5347 71.8008 56.8338C71.8008 57.1328 72.0366 57.3677 72.3367 57.3677H74.0731C74.3732 57.3677 74.609 57.1328 74.609 56.8338C74.609 56.5347 74.3732 56.2998 74.0731 56.2998Z"
          fill="url(#paint0_linear_2232_135)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2232_135"
          x1="36.7838"
          y1="117.983"
          x2="89.6775"
          y2="26.0168"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2232_135">
          <rect
            width="84"
            height="97"
            fill="white"
            transform="translate(23 17)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
