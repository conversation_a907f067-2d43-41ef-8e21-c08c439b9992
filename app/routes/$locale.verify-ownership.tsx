import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'

import { detectLocale } from '~/i18n/detect-locale'
import { i18next, Namespace } from '~/i18n'
import { HeroVerifyOwnership } from '~/sections/verify-ownership'
import { withCache } from '~/utils'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.VERIFY_OWNERSHIP)

  const title = t('meta.title')
  const description = t('meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.CONTACT_PAGE, Namespace.COMMON, Namespace.VERIFY_OWNERSHIP],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function VerifyOwnershipPage() {
  return (
    <div className="container">
      <HeroVerifyOwnership />
    </div>
  )
}
