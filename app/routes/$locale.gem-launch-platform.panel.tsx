import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import {
  Outlet,
  useFetcher,
  useLoaderData,
  useRevalidator,
} from '@remix-run/react'
import { BigNumber, ethers } from 'ethers'
import { useEffect, useState } from 'react'

import { isDevelopment } from '~/environment'
import { BSC_NETWORK } from '~/config'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import { commitSession, getSession } from '~/sessions.server'
import { TensetContract, chains } from '~/tenset-web3'
import { withoutCache } from '~/utils'

export async function loader({ request }: LoaderFunctionArgs) {
  const requestUrl = new URL(request.url)

  const locale = detectLocale(request)

  const t = await i18next.getFixedT(
    locale,
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION
  )

  const title = t('meta.title')
  const description = t('meta.description')

  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  const affiliateCode =
    requestUrl.searchParams.get('affiliate_code') ||
    session.get('lastGlpAffiliateCode')

  session.set('lastGlpAffiliateCode', affiliateCode)

  const { rpcUrls } = chains[BSC_NETWORK]

  const tensetContract = new TensetContract(BSC_NETWORK, rpcUrls[0])

  const jsonRpcProvider = new ethers.providers.JsonRpcProvider({
    url: rpcUrls[0],
    skipFetchSetup: true,
  })

  const [{ timestamp: blockTimestamp }, tensetBalance] = await Promise.all([
    jsonRpcProvider.getBlock('latest'),
    walletAddress
      ? tensetContract.balanceOf(walletAddress)
      : Promise.resolve(0),
  ])

  return withoutCache(
    json(
      {
        title,
        description,
        blockTimestamp: blockTimestamp * 1000,
        tensetBalance,
      },
      {
        headers: {
          'Set-Cookie': await commitSession(session),
        },
      }
    )
  )
}

export type GlpOutletLoaderData = typeof loader

export const handle = {
  i18n: [Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION],
}

export interface GlpOutletData {
  blockTimestamp: number
  tensetBalance: BigNumber
  isRevalidating: boolean
}

export const meta: MetaFunction<GlpOutletLoaderData> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function GemLaunchPlatformPanelOutlet() {
  const loaderData = useLoaderData<GlpOutletLoaderData>()

  const fetcher = useFetcher({ key: 'root' })

  const revalidator = useRevalidator()

  const [isRevalidating, setIsRevalidating] = useState(false)

  // TODO: TMP wallet & chain change event via root fetcher
  // first we need to check, if walletAddress has been updated in localStorage (see routes/api-wallet.connect.ts), because we are referencing it in the loader
  // then we are revalidating and waiting for the result
  const [revalidatable, setRevalidatable] = useState(false)
  useEffect(() => {
    if (
      fetcher.state === 'submitting' &&
      fetcher.formAction.includes('/api/wallet')
    )
      setRevalidatable(true)

    if (!['loading', 'idle'].includes(fetcher.state) || !revalidatable) return

    setRevalidatable(false)

    revalidator.revalidate()
  }, [fetcher.state])

  useEffect(() => {
    const isFetcherLoading =
      ['submitting', 'loading'].includes(fetcher.state) &&
      !fetcher.formAction?.includes('/api/wallet/connect')

    setIsRevalidating(
      revalidatable || revalidator.state === 'loading' || isFetcherLoading
    )
  }, [revalidatable, revalidator.state, fetcher.state])

  useEffect(() => {
    if (!isDevelopment) return

    const { blockTimestamp } = loaderData

    console.log({ blockTimestamp }, new Date(blockTimestamp))
  }, [loaderData.blockTimestamp])

  return (
    <div className="container grid place-items-center">
      <Outlet
        context={{
          blockTimestamp: loaderData.blockTimestamp,
          tensetBalance: BigNumber.from(loaderData.tensetBalance || 0),
          isRevalidating,
        }}
      />
    </div>
  )
}
