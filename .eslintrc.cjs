/**
 * @type {import('@types/eslint').Linter.BaseConfig}
 */
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    sourceType: 'module',
    ecmaVersion: 'latest',
    ecmaFeatures: {
      jsx: true,
    },
  },
  settings: {
    react: {
      version: 'detect',
    },
    'import/parser': '@typescript-eslint/parser',
    'import/resolver': {
      node: {
        paths: ['~/'],
        extensions: ['.js', '.ts', '.tsx', '.jsx'],
      },
      alias: {
        map: [['~', './app/']],
        extensions: ['.js', '.ts', '.tsx', '.jsx'],
      },
    },
    // We're using vitest which has a very similar API to jest
    // (so the linting plugins work nicely), but we have to
    // set the jest version explicitly.
    jest: {
      version: 27,
    },
  },
  plugins: ['prettier', 'eslint-plugin-import-helpers'],
  extends: [
    '@remix-run/eslint-config',
    '@remix-run/eslint-config/node',
    'plugin:@typescript-eslint/recommended',
    'prettier',
    'plugin:eslint-comments/recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:react-hooks/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:sonarjs/recommended',
    'plugin:unicorn/recommended',
    'plugin:jsx-a11y/recommended',
  ],
  overrides: [
    {
      files: '**/*.{md,mdx}',
      extends: 'plugin:mdx/recommended',
      settings: {
        'mdx/code-blocks': true,
      },
    },
  ],
  ignorePatterns: [
    'remix.config.js',
    'remix.env.d.ts',
    'README.md',
    '.eslintrc.js',
    'server.js',
  ],
  rules: {
    eqeqeq: 'error',
    '@typescript-eslint/no-non-null-assertion': 'off',
    'prettier/prettier': 'warn',
    'prefer-const': 'warn',
    'sonarjs/no-duplicate-string': 'off',
    'no-console': 'off',
    'eslint-comments/no-unused-disable': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    // empty functions are helpful for defining default values
    '@typescript-eslint/no-empty-function': 'off',
    'unicorn/prefer-node-protocol': 'off',
    'import-helpers/order-imports': 'off',
    'unicorn/no-useless-undefined': 'off',
    'unicorn/no-array-reduce': 'off',
    'unicorn/no-empty-file': 'warn',
    // ignore only until https://github.com/sindresorhus/eslint-plugin-unicorn/pull/1628 is opened
    'unicorn/filename-case': 'off',
    'unicorn/no-null': 'off',
    'import/order': [
      'warn',
      {
        'newlines-between': 'always',
      },
    ],
    // we use typescript instead of propTypes
    'react/prop-types': 'off',
    'unicorn/prevent-abbreviations': [
      'error',
      {
        allowList: {
          // props and ref are popular lingo in react world
          Props: true,
          props: true,
          ref: true,
          env: true,
          db: true,
        },
      },
    ],
  },
}
