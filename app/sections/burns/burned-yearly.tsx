import { useTranslation } from 'react-i18next'

import { H2 } from '~/tenset-components'
import { Namespace } from '~/i18n'
import { TotalBurnedPrediction } from '~/components/burns'

export function BurnsBurnedYearly() {
  const { t } = useTranslation([Namespace.BURNS])

  return (
    <div className="flex flex-col gap-8 lg:gap-12">
      <H2 isBold>{t('burned-yearly.title')}</H2>

      <TotalBurnedPrediction />
    </div>
  )
}
