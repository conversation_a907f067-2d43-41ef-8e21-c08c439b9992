import { useTranslation } from 'react-i18next'

import { Icon, IconName, Text } from '~/tenset-components'
import { Namespace } from '~/i18n'

export function LoadingIndicator() {
  const { t } = useTranslation([Namespace.COMMON])

  return (
    <div className="flex items-center gap-2 overflow-hidden">
      <Icon name={IconName.Loading2} />

      <Text>{t('common:loading')}</Text>
    </div>
  )
}
