import { useTranslation } from 'react-i18next'
import { useState } from 'react'

import { Namespace } from '~/i18n'
import { Button, ButtonVariant } from '~/tenset-components'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { useWallet } from '~/tenset-web3'
import { ConnectWallet } from '~/components/connect-wallet'
import { PresaleParticipateModal } from '~/components/presale-participate/modal'

interface PresaleParticipateButtonProps {
  link?: string
}

export function PresaleParticipateButton({
  link,
}: PresaleParticipateButtonProps) {
  const { t } = useTranslation(Namespace.COMMON)
  const localizePathname = useLocalizePathname()

  const { wallet } = useWallet()

  const { Primary } = ButtonVariant

  const [showPresaleParticipateModal, setShowPresaleParticipateModal] =
    useState(false)

  if (!wallet) return null

  if (!wallet.isConnected)
    return (
      <ConnectWallet
        buttonConnectLabel={t('common:gems.participate-in-presale')}
      />
    )

  const buttonAction = link
    ? undefined
    : () => setShowPresaleParticipateModal(true)

  return (
    <>
      <Button
        to={link && localizePathname(link)}
        onClick={buttonAction}
        variant={Primary}
        className="w-full sm:w-auto"
      >
        {t('common:gems.participate-in-presale')}
      </Button>

      <PresaleParticipateModal
        isOpen={showPresaleParticipateModal}
        setIsOpen={setShowPresaleParticipateModal}
      />
    </>
  )
}
