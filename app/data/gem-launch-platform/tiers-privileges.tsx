import type { TFunction } from 'i18next'

export interface TierPrivileges {
  entry: number | string
  accessTime: string
  poolWeight: number
  claimBack: boolean
  tradableVesting: boolean
  stakingRewards: boolean

  [key: string]: string | number | boolean
}

export const getTierIndex = (entry: number, isRegularNft = false) => {
  if (isRegularNft) return 4
  if (entry >= 5000) return 3
  if (entry >= 2500) return 2
  if (entry >= 1000) return 1
  return -1
}

export const getTierAmount = (index: number) => {
  if (index === 4) return 5000
  if (index === 3) return 5000
  if (index === 2) return 2500
  if (index === 1) return 1000
  return 0
}

export const tiersPrivileges = (t: TFunction): TierPrivileges[] => [
  {
    entry: 1000,
    accessTime: t('gem-launch-platform:tiers-privileges.access-time.yearly'),
    poolWeight: 1,
    claimBack: false,
    tradableVesting: false,
    stakingRewards: false,
  },
  {
    entry: 2500,
    accessTime: t('gem-launch-platform:tiers-privileges.access-time.yearly'),
    poolWeight: 2.5,
    claimBack: true,
    tradableVesting: false,
    stakingRewards: false,
  },
  {
    entry: 5000,
    accessTime: t('gem-launch-platform:tiers-privileges.access-time.yearly'),
    poolWeight: 6,
    claimBack: true,
    tradableVesting: true,
    stakingRewards: false,
  },
  {
    entry: t('gem-launch-platform:tiers-privileges.entry.regular-nft'),
    accessTime: t('gem-launch-platform:tiers-privileges.access-time.lifetime'),
    poolWeight: 7.5,
    claimBack: true,
    tradableVesting: true,
    stakingRewards: false,
  },
  {
    entry: t('gem-launch-platform:tiers-privileges.entry.sumeragi-genesis-nft'),
    accessTime: t('gem-launch-platform:tiers-privileges.access-time.lifetime'),
    poolWeight: 9,
    claimBack: true,
    tradableVesting: true,
    stakingRewards: true,
  },
]
