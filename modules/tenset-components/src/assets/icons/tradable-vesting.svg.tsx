import { AssetProps } from '.'

export default function TradableVesting({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2040_5)">
        <path
          d="M34.0905 75.443H19.7803V102.121H34.0905V75.443Z"
          fill="#15121A"
        />
        <path
          d="M61.7226 48.7654H47.4124V75.443H61.7226V48.7654Z"
          fill="#15121A"
        />
        <path
          d="M89.3547 55.4282H75.0446V82.1058H89.3547V55.4282Z"
          fill="#15121A"
        />
        <path
          d="M116.987 28.7507H102.677V55.4282H116.987V28.7507Z"
          fill="#15121A"
        />
        <path
          d="M52.446 67.4635C52.8941 67.4635 53.263 67.0948 53.263 66.6471C53.263 66.1994 52.8941 65.8307 52.446 65.8307C51.998 65.8307 51.6291 66.1994 51.6291 66.6471C51.6291 67.0948 51.998 67.4635 52.446 67.4635ZM52.446 54.5197C52.8941 54.5197 53.263 54.151 53.263 53.7033C53.263 53.2556 52.8941 52.8869 52.446 52.8869C51.998 52.8869 51.6291 53.2556 51.6291 53.7033C51.6291 54.151 51.998 54.5197 52.446 54.5197ZM52.446 60.9982C52.8941 60.9982 53.263 60.6295 53.263 60.1818C53.263 59.7341 52.8941 59.3654 52.446 59.3654C51.998 59.3654 51.6291 59.7341 51.6291 60.1818C51.6291 60.6295 51.998 60.9982 52.446 60.9982ZM49.2309 51.2936C49.6789 51.2936 50.0478 50.9249 50.0478 50.4772C50.0478 50.0295 49.6789 49.6608 49.2309 49.6608C48.7828 49.6608 48.4139 50.0295 48.4139 50.4772C48.4139 50.9249 48.7828 51.2936 49.2309 51.2936ZM49.2309 70.7027C49.6789 70.7027 50.0478 70.334 50.0478 69.8863C50.0478 69.4386 49.6789 69.0699 49.2309 69.0699C48.7828 69.0699 48.4139 69.4386 48.4139 69.8863C48.4139 70.334 48.7828 70.7027 49.2309 70.7027ZM49.2309 57.7589C49.6789 57.7589 50.0478 57.3902 50.0478 56.9425C50.0478 56.4948 49.6789 56.1261 49.2309 56.1261C48.7828 56.1261 48.4139 56.4948 48.4139 56.9425C48.4139 57.3902 48.7828 57.7589 49.2309 57.7589ZM49.2309 64.2242C49.6789 64.2242 50.0478 63.8555 50.0478 63.4078C50.0478 62.9601 49.6789 62.5914 49.2309 62.5914C48.7828 62.5914 48.4139 62.9601 48.4139 63.4078C48.4139 63.8555 48.7828 64.2242 49.2309 64.2242ZM55.6612 70.7027C56.1092 70.7027 56.4782 70.334 56.4782 69.8863C56.4782 69.4386 56.1092 69.0699 55.6612 69.0699C55.2132 69.0699 54.8442 69.4386 54.8442 69.8863C54.8442 70.334 55.2132 70.7027 55.6612 70.7027ZM114.865 26.1567H108.369V10.6584C108.369 10.2897 108.079 10 107.71 10C107.341 10 107.051 10.2897 107.051 10.6584V26.1567H100.555C100.186 26.1567 99.8963 26.4463 99.8963 26.815V53.4926C99.8963 53.8613 100.186 54.151 100.555 54.151H107.051V69.6493C107.051 70.018 107.341 70.3077 107.71 70.3077C108.079 70.3077 108.369 70.018 108.369 69.6493V54.151H114.865C115.234 54.151 115.524 53.8613 115.524 53.4926V26.815C115.524 26.4463 115.234 26.1567 114.865 26.1567ZM114.206 52.8342H101.201V27.4734H114.206V52.8342ZM55.6612 51.2936C56.1092 51.2936 56.4782 50.9249 56.4782 50.4772C56.4782 50.0295 56.1092 49.6608 55.6612 49.6608C55.2132 49.6608 54.8442 50.0295 54.8442 50.4772C54.8442 50.9249 55.2132 51.2936 55.6612 51.2936ZM31.9822 72.849H25.486V57.3507C25.486 56.982 25.1961 56.6923 24.8271 56.6923C24.4582 56.6923 24.1683 56.982 24.1683 57.3507V72.849H17.6588C17.2899 72.849 17 73.1387 17 73.5074V100.185C17 100.554 17.2899 100.843 17.6588 100.843H24.1551V116.342C24.1551 116.71 24.445 117 24.8139 117C25.1829 117 25.4728 116.71 25.4728 116.342V100.843H31.969C32.338 100.843 32.6279 100.554 32.6279 100.185V73.5074C32.6279 73.1387 32.338 72.849 31.969 72.849H31.9822ZM31.3234 99.5266H18.3177V74.1658H31.3234V99.5266ZM55.6612 64.2242C56.1092 64.2242 56.4782 63.8555 56.4782 63.4078C56.4782 62.9601 56.1092 62.5914 55.6612 62.5914C55.2132 62.5914 54.8442 62.9601 54.8442 63.4078C54.8442 63.8555 55.2132 64.2242 55.6612 64.2242ZM55.6612 57.7589C56.1092 57.7589 56.4782 57.3902 56.4782 56.9425C56.4782 56.4948 56.1092 56.1261 55.6612 56.1261C55.2132 56.1261 54.8442 56.4948 54.8442 56.9425C54.8442 57.3902 55.2132 57.7589 55.6612 57.7589ZM110.925 50.6747C111.373 50.6747 111.742 50.3061 111.742 49.8584C111.742 49.4107 111.373 49.042 110.925 49.042C110.477 49.042 110.108 49.4107 110.108 49.8584C110.108 50.3061 110.477 50.6747 110.925 50.6747ZM107.71 40.9702C108.158 40.9702 108.527 40.6015 108.527 40.1538C108.527 39.7061 108.158 39.3374 107.71 39.3374C107.262 39.3374 106.893 39.7061 106.893 40.1538C106.893 40.6015 107.262 40.9702 107.71 40.9702ZM107.71 47.4487C108.158 47.4487 108.527 47.08 108.527 46.6323C108.527 46.1846 108.158 45.8159 107.71 45.8159C107.262 45.8159 106.893 46.1846 106.893 46.6323C106.893 47.08 107.262 47.4487 107.71 47.4487ZM107.71 34.5049C108.158 34.5049 108.527 34.1362 108.527 33.6885C108.527 33.2408 108.158 32.8721 107.71 32.8721C107.262 32.8721 106.893 33.2408 106.893 33.6885C106.893 34.1362 107.262 34.5049 107.71 34.5049ZM104.495 37.7442C104.943 37.7442 105.312 37.3755 105.312 36.9278C105.312 36.4801 104.943 36.1114 104.495 36.1114C104.047 36.1114 103.678 36.4801 103.678 36.9278C103.678 37.3755 104.047 37.7442 104.495 37.7442ZM104.495 31.2657C104.943 31.2657 105.312 30.897 105.312 30.4493C105.312 30.0016 104.943 29.6329 104.495 29.6329C104.047 29.6329 103.678 30.0016 103.678 30.4493C103.678 30.897 104.047 31.2657 104.495 31.2657ZM59.6011 46.1846H53.1049V30.6863C53.1049 30.3176 52.815 30.0279 52.446 30.0279C52.0771 30.0279 51.7872 30.3176 51.7872 30.6863V46.1846H45.2909C44.922 46.1846 44.6321 46.4743 44.6321 46.843V73.5206C44.6321 73.8892 44.922 74.1789 45.2909 74.1789H51.7872V89.6772C51.7872 90.0459 52.0771 90.3356 52.446 90.3356C52.815 90.3356 53.1049 90.0459 53.1049 89.6772V74.1789H59.6011C59.9701 74.1789 60.26 73.8892 60.26 73.5206V46.843C60.26 46.4743 59.9701 46.1846 59.6011 46.1846ZM58.9423 72.849H45.9498V47.4882H58.9423V72.849ZM104.495 44.2094C104.943 44.2094 105.312 43.8408 105.312 43.3931C105.312 42.9454 104.943 42.5767 104.495 42.5767C104.047 42.5767 103.678 42.9454 103.678 43.3931C103.678 43.8408 104.047 44.2094 104.495 44.2094ZM110.925 44.2094C111.373 44.2094 111.742 43.8408 111.742 43.3931C111.742 42.9454 111.373 42.5767 110.925 42.5767C110.477 42.5767 110.108 42.9454 110.108 43.3931C110.108 43.8408 110.477 44.2094 110.925 44.2094ZM104.495 50.6747C104.943 50.6747 105.312 50.3061 105.312 49.8584C105.312 49.4107 104.943 49.042 104.495 49.042C104.047 49.042 103.678 49.4107 103.678 49.8584C103.678 50.3061 104.047 50.6747 104.495 50.6747ZM87.2332 52.8342H80.737V37.336C80.737 36.9673 80.4471 36.6776 80.0781 36.6776C79.7092 36.6776 79.4193 36.9673 79.4193 37.336V52.8342H72.923C72.5541 52.8342 72.2642 53.1239 72.2642 53.4926V80.1702C72.2642 80.5389 72.5541 80.8286 72.923 80.8286H79.4193V96.3269C79.4193 96.6955 79.7092 96.9852 80.0781 96.9852C80.4471 96.9852 80.737 96.6955 80.737 96.3269V80.8286H87.2332C87.6022 80.8286 87.8921 80.5389 87.8921 80.1702V53.4926C87.8921 53.1239 87.6022 52.8342 87.2332 52.8342ZM86.5744 79.5118H73.5687V54.151H86.5744V79.5118ZM110.925 31.2657C111.373 31.2657 111.742 30.897 111.742 30.4493C111.742 30.0016 111.373 29.6329 110.925 29.6329C110.477 29.6329 110.108 30.0016 110.108 30.4493C110.108 30.897 110.477 31.2657 110.925 31.2657ZM110.925 37.7442C111.373 37.7442 111.742 37.3755 111.742 36.9278C111.742 36.4801 111.373 36.1114 110.925 36.1114C110.477 36.1114 110.108 36.4801 110.108 36.9278C110.108 37.3755 110.477 37.7442 110.925 37.7442Z"
          fill="url(#paint0_linear_2040_5)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2040_5"
          x1="32.6806"
          y1="121.635"
          x2="99.7852"
          y2="5.32373"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2040_5">
          <rect
            width="100"
            height="107"
            fill="white"
            transform="translate(17 10)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
