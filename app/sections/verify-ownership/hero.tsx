import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'

import { ConnectWallet } from '~/components/connect-wallet'
import { <PERSON><PERSON>, TextS, Hero } from '~/tenset-components'
import { useWallet } from '~/tenset-web3'
import { Namespace } from '~/i18n'

interface UrlParameters {
  username: string
  token: string
  address: string
  callback: string
  valid: boolean
}

function VerifyOwnership(_props: unknown) {
  const { t } = useTranslation([Namespace.VERIFY_OWNERSHIP])
  const { wallet } = useWallet()
  const [error, setError] = useState('')
  const [signature, setSignature] = useState('')
  const [urlParameters, setUrlParameters] = useState<
    UrlParameters | undefined
  >()
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  useEffect(() => {
    const parameters = new URLSearchParams(window.location.search)
    const username = decodeURIComponent(parameters.get('username') || '')
    const token = parameters.get('token') || ''
    const callback = decodeURIComponent(parameters.get('callback') || '')
    const address = parameters.get('address')?.toLowerCase() || ''

    setUrlParameters({
      username,
      token,
      address,
      callback,
      valid: Boolean(address && username && token && callback),
    })
  }, [])

  const handleSignToken = async () => {
    setError('')
    setIsLoading(true)
    let newSignature
    try {
      newSignature = await wallet?.signMessage(urlParameters!.token!)
    } catch (error_) {
      setIsLoading(false)
      return setError(
        'Could not sign message: ' + (error_ as Error).message.split('(')[0]
      )
    }
    if (newSignature) {
      setSignature(newSignature)
      handleUploadSignature(newSignature)
    }
  }

  const handleUploadSignature = async (signature: string) => {
    setError('')
    setIsLoading(true)
    try {
      const payload = JSON.stringify({
        signature,
        token: urlParameters?.token,
        address: urlParameters?.address,
      })

      const request = await fetch(urlParameters!.callback, {
        method: 'POST',
        headers: new Headers({
          'Content-Type': 'application/json',
        }),
        body: payload,
      })

      if (request.ok) {
        setIsSubmitted(true)
      } else {
        const response = await request.json()

        if (request.status === 403) {
          setError(
            response.message === 'community.not_nft_owner'
              ? 'Not an owner of Genesis NFT'
              : 'Your link has expired. Please request a new link from Tenset BOT.'
          )
        } else if (request.status > 201) {
          setError('Something went wrong. Please try again later.')
        }
      }
    } catch (error_) {
      console.error(error_)
      setError('Something went wrong. Please try again later.')
    }
    setIsLoading(false)
  }

  const isValidAddress =
    !wallet?.account || urlParameters?.address === wallet?.account

  if (isSubmitted) {
    return (
      <div className="flex w-full flex-col gap-4 lg:gap-8">
        <div className="mt-2 flex flex-col items-center md:mt-0 md:flex-none md:items-start">
          {t('success', {
            username: urlParameters?.username,
          })}
        </div>
      </div>
    )
  }

  return (
    <div className="flex w-full flex-col gap-4 lg:gap-8">
      <div className="mt-2 flex flex-col items-center md:mt-0 md:flex-none md:items-start">
        <ConnectWallet />
        {urlParameters && !urlParameters?.valid && (
          <TextS className="mt-5" isBold>
            {t('invalid-url')}
          </TextS>
        )}
        {wallet?.isConnected && urlParameters?.valid && !isValidAddress && (
          <TextS className="mt-5" isBold>
            {t('invalid-address')} {urlParameters?.address}
          </TextS>
        )}
        {error && (
          <TextS className="mt-5 text-red-500" isBold>
            {error}
          </TextS>
        )}
        {wallet?.isConnected && isValidAddress && (
          <Button
            onClick={
              signature
                ? () => handleUploadSignature(signature)
                : handleSignToken
            }
            className="mt-5 w-full md:w-auto"
            disabled={isLoading || isSubmitted}
            loading={isLoading}
          >
            <TextS isBold>{signature ? t('log-in') : t('sign-token')}</TextS>
          </Button>
        )}
      </div>
    </div>
  )
}

export function HeroVerifyOwnership(_props: unknown) {
  const { t } = useTranslation([Namespace.VERIFY_OWNERSHIP])

  return (
    <section className="grid place-items-center">
      <Hero
        title={t('hero.title')}
        description={t('hero.description')}
        leftContent={<VerifyOwnership />}
      />
    </section>
  )
}
