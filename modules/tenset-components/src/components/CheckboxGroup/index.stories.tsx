import { Meta, StoryFn } from '@storybook/react'
import { useState } from 'react'

import { DateFormatter, NumberFormatter } from '../Formatters'
import { Image } from '../Image'
import { Text, TextS, TextXs } from '../Typo/Text'

import { CheckboxGroup } from './index'

type CheckboxGroupType = typeof CheckboxGroup

export default {
  title: 'Components/CheckboxGroup',
  component: CheckboxGroup,
} as Meta<CheckboxGroupType>

const Template: StoryFn<CheckboxGroupType> = _arguments => {
  const [checked, setChecked] = useState<string[]>([])

  return (
    <div className="grid gap-4 grid-cols-2">
      <CheckboxGroup
        {..._arguments}
        checked={checked}
        onCheckedChange={setChecked}
      />

      <div>
        <Text>Checked:</Text>

        <ul className="flex gap-1">
          {checked.map(id => (
            <li key={id}>{id}</li>
          ))}
        </ul>
      </div>
    </div>
  )
}

const previewOptions = [
  { id: 'preview1', label: 'Option 1' },
  { id: 'preview2', label: 'Option 2' },
  { id: 'preview3', label: 'Option 3' },
  { id: 'preview4', label: 'Option 4' },
]

export const Preview = {
  render: Template,

  args: {
    options: previewOptions,
  },
}

const someDisabledOptions = [
  { id: 'disabled1', label: 'Option 1', disabled: true },
  { id: 'disabled2', label: 'Option 2', disabled: true },
  { id: 'disabled3', label: 'Option 3' },
  { id: 'disabled4', label: 'Option 4' },
]

export const SomeDisabled = {
  render: Template,

  args: {
    options: someDisabledOptions,
  },
}

const glpTransferLocksOptions = Array.from({ length: 20 }).map((_, index) => (
  <span className="flex gap-1 items-center" key={index}>
    <span>
      <TextS isBold tag="span">
        Tier 4
      </TextS>{' '}
      (
      <NumberFormatter value={5000} />)
    </span>{' '}
    <TextXs tag="span" className="text-neutral-100">
      Release date: <DateFormatter date={new Date()} />
    </TextXs>
  </span>
))

export const GlpTransferLocks = {
  render: Template,

  args: {
    title: (
      <Text isBold tag="span">
        Your Locks
      </Text>
    ),
    options: glpTransferLocksOptions.map((label, index) => ({
      id: `${index + 1}`,
      label,
    })),
  },
}

const glpTransferNftsOptions = Array.from({ length: 10 }).map((_, index) => (
  <div className="flex gap-2 items-center" key={index}>
    <span>
      <TextS isBold tag="span">
        Tier 4
      </TextS>{' '}
      <span>TGLP Genesis #{index + 1}</span>
    </span>

    <Image
      src={`https://cdn-static.tenset.io/TGLP_genesis/${index + 1}.png`}
      alt={`TGLP Genesis #${index + 1}`}
      ratio={1}
      width="24px"
    />
  </div>
))

export const GlpTransferNfts = {
  render: Template,

  args: {
    title: (
      <Text isBold tag="span">
        Your NFTs
      </Text>
    ),
    options: glpTransferNftsOptions.map((label, index) => ({
      id: `${index + 1}`,
      label,
    })),
  },
}
