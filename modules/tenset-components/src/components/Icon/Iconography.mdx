import { Meta, IconGallery, IconItem } from '@storybook/blocks'

import { IconNames } from './index'

<Meta title="Documentation/Iconography" />

# Tenset icons library

<IconGallery>
  {Object.entries(IconNames).map(([name, Icon]) =>
  (<IconItem key={name} name={name}>

        <div className="flex h-full w-full items-center justify-center bg-neutral-900 p-2.5">
          <Icon />
        </div>

      </IconItem>
    )

)}

</IconGallery>
