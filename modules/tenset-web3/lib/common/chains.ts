import { Utils } from '../types'

type Chains = {
  [key in Utils.Network]: {
    name: string
    scannerUrl: string
    currency: string
    rpcUrls: string[]
  }
}

const {
  ETHEREUM,
  ROPSTEN,
  RINKEBY,
  GOERLI,
  SMART_CHAIN,
  SMART_CHAIN_TESTNET,
  POLYGON,
  ARBITRUM_ONE,
  BASE,
  PHALCON_BSC,
  PHALCON_ETH,
  ANVIL_BSC,
  ANVIL_ETH,
} = Utils.Network

export const chains: Chains = {
  [ETHEREUM]: {
    name: 'Ethereum',
    scannerUrl: 'https://etherscan.io',
    currency: 'ETH',
    rpcUrls: ['https://cloudflare-eth.com'],
  },
  [ROPSTEN]: {
    name: 'Rops<PERSON>',
    scannerUrl: 'https://ropsten.etherscan.io',
    currency: 'ETH',
    rpcUrls: ['https://ropsten.infura.io/v3/'],
  },
  [RINKEBY]: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    scannerUrl: 'https://rinkeby.etherscan.io',
    currency: 'ETH',
    rpcUrls: ['https://rinkeby.infura.io/v3/'],
  },
  [GOERLI]: {
    name: 'Goerli',
    scannerUrl: 'https://goerli.etherscan.io',
    currency: 'ETH',
    rpcUrls: ['https://rpc.goerli.mudit.blog'],
  },
  [SMART_CHAIN]: {
    name: 'Smart Chain',
    scannerUrl: 'https://bscscan.com',
    currency: 'BNB',
    rpcUrls: ['https://bsc-dataseed.binance.org/'],
  },
  [SMART_CHAIN_TESTNET]: {
    name: 'Smart Chain Testnet',
    scannerUrl: 'https://testnet.bscscan.com',
    currency: 'tBNB',
    rpcUrls: ['https://data-seed-prebsc-1-s1.binance.org:8545/'],
  },
  [POLYGON]: {
    name: 'Polygon',
    scannerUrl: 'https://polygonscan.com',
    currency: 'MATIC',
    rpcUrls: ['https://rpc-mainnet.maticvigil.com/'],
  },
  [ARBITRUM_ONE]: {
    name: 'Arbitrum One',
    scannerUrl: 'https://arbiscan.io',
    currency: 'ETH',
    rpcUrls: ['https://arb1.arbitrum.io/rpc/'],
  },
  [BASE]: {
    name: 'Base',
    scannerUrl: 'https://basescan.org',
    currency: 'ETH',
    rpcUrls: ['https://mainnet.base.org'],
  },
  [PHALCON_BSC]: {
    name: 'Phalcon BSC',
    scannerUrl: '',
    currency: 'tBNB',
    rpcUrls: [
      'https://rpc.phalcon.blocksec.com/rpc_32c6d2d66a3e4a66a06ee13415ce51f9',
    ],
  },
  [PHALCON_ETH]: {
    name: 'Phalcon ETH',
    scannerUrl: '',
    currency: 'tETH',
    rpcUrls: [
      'https://rpc.phalcon.blocksec.com/rpc_b172223f2e6d49c9a4ebeeb2d8c0e9c7',
    ],
  },
  [ANVIL_BSC]: {
    name: 'Anvil BSC',
    scannerUrl: '',
    currency: 'tBNB',
    rpcUrls: ['http://127.0.0.1:8545'],
  },
  [ANVIL_ETH]: {
    name: 'Anvil ETH',
    scannerUrl: '',
    currency: 'tETH',
    rpcUrls: ['http://127.0.0.1:8546'],
  },
}
