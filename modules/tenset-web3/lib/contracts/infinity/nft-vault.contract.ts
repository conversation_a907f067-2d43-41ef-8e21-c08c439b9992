import { ContractTransaction } from 'ethers'

import { infinityNftVaultAbi } from '../../assets'
import { config } from '../../config'
import { Utils } from '../../types'
import { Contract } from '../base'

export class InfinityNftVaultContract extends Contract {
  constructor(chain: Utils.Network) {
    super(config.infinity.nftVault[chain]!, chain, infinityNftVaultAbi)
  }

  public async deposit(tokenId: number, collection: string) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.deposit(
        tokenId,
        collection
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }

  public async withdraw(tokenId: number, collection: string) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.withdraw(
        tokenId,
        collection
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
