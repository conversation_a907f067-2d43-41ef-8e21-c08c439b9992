import { Meta } from '@storybook/react'

import {
  currentLocale,
  navigationData,
  SUPPORTED_LOCALES,
  SupportedLocale,
} from './story-data'
import { Navigation } from './navigation'

type NavigationType = typeof Navigation

export default {
  title: 'Components/Navigation',
  component: Navigation,
  args: {
    data: navigationData,
    localeData: {
      supportedLocales: SUPPORTED_LOCALES,
      currentLocale,
      changePathnameLocale: (locale: SupportedLocale) => `/${locale}`,
      routeWithLocale: (route: string) => `/${route}`,
    },
  },
} as Meta<NavigationType>

export const Preview = {}
