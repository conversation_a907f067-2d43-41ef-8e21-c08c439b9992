import { type TFunction } from 'i18next'
import { Trans } from 'react-i18next'

import type { GlpLock, InfinityNftLock } from '~/api/types'
import type { GlpNftWithInfinityLock } from '~/routes/$locale.gem-launch-platform.panel.transfer'
import type { CheckboxProps } from '~/tenset-components'
import {
  Button,
  ButtonVariant,
  DateFormatter,
  Image,
  NumberFormatter,
  TextS,
  TextXs,
  Video,
} from '~/tenset-components'

type FormatLock = GlpLock & { t: TFunction }
type FormatNft = GlpNftWithInfinityLock & {
  t: TFunction
  isNotWithdrawnFromInfinity: boolean
}

export function formatLock({
  t,
  amount,
  membershipAttributes,
  timestamps,
  id: _id,
}: FormatLock): CheckboxProps {
  const { tierId } = membershipAttributes!
  const { unlock } = timestamps

  const id = `lock-${_id}`

  return {
    id,
    label: (
      <span key={id}>
        <TextS isBold tag="span">
          {t('tier')} {tierId}
        </TextS>{' '}
        (<NumberFormatter value={Number.parseFloat(amount)} />){' '}
        <TextXs tag="span" className="text-neutral-100">
          {t('release-date')}: <DateFormatter date={new Date(unlock)} />
        </TextXs>
      </span>
    ),
  }
}

export function formatNft({
  t,
  id: _id,
  collection,
  contractAddress,
  tokenName,
  image,
  isImageVideo,
  chainId,
  infinityLock,
  isNotWithdrawnFromInfinity,
}: FormatNft): CheckboxProps {
  const id = `${chainId}-${contractAddress}-${_id}`

  return {
    id,
    label: (
      <div className="flex flex-wrap gap-2 items-center" key={id}>
        <span>
          <TextS isBold tag="span">
            {t('tier')} 4
          </TextS>{' '}
          <span>
            ({collection} #{_id})
          </span>
        </span>

        {isImageVideo ? (
          <div className="w-[24px] aspect-square rounded-md overflow-hidden">
            <Video
              src={image}
              autoPlay
              muted
              loop
              playsInline
              controls={false}
            />
          </div>
        ) : (
          <Image src={image} alt={tokenName} ratio={1} width="24px" />
        )}
      </div>
    ),
    children: isNotWithdrawnFromInfinity
      ? NftInfinityLockInformation(t, infinityLock)
      : null,
    disabled: isNotWithdrawnFromInfinity,
  }
}

function NftInfinityLockInformation(
  t: TFunction,
  infinityLock?: InfinityNftLock
) {
  if (!infinityLock) return null

  const INFINITY_LOCKS_URL = 'https://infinity.tenset.io/locks'

  const InfinityLocksPageButton = () => (
    <Button to={INFINITY_LOCKS_URL} variant={ButtonVariant.Link}>
      {t('transfer.subscriptions.infinity-locks-page')}
    </Button>
  )

  return (
    <TextS className="text-left">
      <Trans
        t={t}
        i18nKey={'transfer.subscriptions.nft-locked-in-infinity'}
        components={{ link: <InfinityLocksPageButton /> }}
      />
    </TextS>
  )
}
