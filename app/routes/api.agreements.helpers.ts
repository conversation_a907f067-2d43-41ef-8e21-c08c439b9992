import type { Agreement } from '@prisma/client'
import { json } from '@remix-run/node'
import { cors } from 'remix-utils/cors'

import { agreementConfig } from '../config/application'

import { database } from '~/database'
import { getErrorMessage } from '~/utils'

export async function getAgreement(request: Request): Promise<Agreement> {
  let agreement: Agreement | null = null

  try {
    const { id } = agreementConfig

    agreement = await database!.agreement.findUnique({
      where: {
        id,
      },
    })
  } catch (error) {
    throw await cors(
      request,
      json({ error: getErrorMessage(error) }, { status: 409 })
    )
  }

  if (!agreement)
    throw await cors(
      request,
      json({ error: 'Agreement id not found' }, { status: 404 })
    )

  return agreement
}
