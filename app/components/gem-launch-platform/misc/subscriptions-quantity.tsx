import { useLoaderData } from '@remix-run/react'
import { BigNumber } from 'ethers'
import { formatEther } from 'ethers/lib/utils'
import { useTranslation } from 'react-i18next'

import { GLP_MAX_CAPACITY, MINTED_TIER_5_NFT_AMOUNT } from '~/data'
import { Namespace } from '~/i18n'
import type { GlpLoaderData } from '~/routes/$locale.gem-launch-platform._index'
import {
  NumberFormatter,
  ProgressBar,
  TextS,
  TextSNumeric,
} from '~/tenset-components'

export function SubscriptionsQuantity() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])

  const { rpcLockedAmount, lifetimeMembershipTotalSupply } =
    useLoaderData<GlpLoaderData>()

  const lockedTensets = Number.parseFloat(formatEther(rpcLockedAmount))

  const mintedNfts =
    MINTED_TIER_5_NFT_AMOUNT +
    BigNumber.from(lifetimeMembershipTotalSupply).toNumber()

  const lockedTensetsCapacity = GLP_MAX_CAPACITY * 5000

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2 justify-between">
        <TextS isBold>{t('hero.subscriptions-quantity.minted-nfts')}</TextS>

        <TextSNumeric isBold>
          <NumberFormatter value={mintedNfts} />
        </TextSNumeric>
      </div>

      <ProgressBar
        label={t('hero.subscriptions-quantity.tenset-locked')}
        numerator={lockedTensets}
        denominator={lockedTensetsCapacity}
        ofLabel={t('common:of')}
      />
    </div>
  )
}
