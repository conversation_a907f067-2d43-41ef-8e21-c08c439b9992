/* eslint unicorn/prefer-module: 0 */
/* eslint @typescript-eslint/no-var-requires: 0 */
const {
  resolveConfigRelative,
} = require('./modules/tenset-components/src/tailwind-config.js')

const baseConfig = resolveConfigRelative('./modules/tenset-components')

/** @type {import('tailwindcss').Config} */
module.exports = {
  ...baseConfig,
  content: [...baseConfig.content, './app/**/*.{ts,tsx,jsx,js}'],
  plugins: [require('@tailwindcss/typography')],
}
