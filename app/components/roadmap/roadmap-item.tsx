import type React from 'react'

import { H3, Icon, IconName, Tag, TagColor, Text } from '~/tenset-components'

interface RoadmapItemProps {
  title: string
  description: string
  statuses: {
    pending: string[]
    ongoing: string[]
    done: string[]
  }
}

type Status = 'pending' | 'ongoing' | 'done'

const statusConfig: Record<Status, { color: TagColor; icon: IconName }> = {
  pending: { color: TagColor.NEUTRAL, icon: IconName.Clock },
  ongoing: { color: TagColor.BLUE, icon: IconName.Gear },
  done: { color: TagColor.GREEN, icon: IconName.Done },
}

const RoadmapItem: React.FC<RoadmapItemProps> = ({
  title,
  description,
  statuses,
}) => {
  return (
    <div className="my-10">
      <H3 isBold className="text-2xl font-bold mb-2">
        {title}
      </H3>
      <Text className="mb-4 max-w-[700px]">{description}</Text>
      <div className="grid gap-6 md:grid-cols-3">
        {Object.keys(statuses).map(status => (
          <div key={status} className="p-6 bg-neutral-800 rounded-lg space-y-6">
            <Tag color={statusConfig[status as Status].color}>
              <span className="capitalize">{status}</span>
            </Tag>
            <ul className="space-y-2">
              {statuses[status as Status].map(item => (
                <li key={item} className="flex items-center gap-2">
                  <Icon
                    name={statusConfig[status as Status].icon}
                    className="flex-none"
                  />
                  {item}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  )
}

export default RoadmapItem
