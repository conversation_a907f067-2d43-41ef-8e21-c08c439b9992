import { AssetProps } from './index'

export default function Buyback({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M92.7586 77.3378C103.481 72.1655 101.745 58.4928 99.3282 56.5148C99.0478 67.3005 93.5731 68.597 88.1385 70.3211V66.6456L90.3284 64.4403C91.1562 63.6117 91.1562 62.2752 90.3284 61.4465L79.192 50.2999C78.3641 49.4713 77.0288 49.4713 76.2009 50.2999L65.0645 61.4465C64.2367 62.2752 64.2367 63.6117 65.0645 64.4403L67.2544 66.6456V84.7421L65.0645 86.9474C64.2367 87.7761 64.2367 89.1126 65.0645 89.9412L76.2009 101.088C77.0288 101.916 78.3641 101.916 79.192 101.088L83.318 96.958L83.4516 96.2095C83.4249 96.4634 83.3982 96.7174 83.3314 96.9713C90.2082 98.2945 89.3002 92.4539 91.3565 90.6629C93.3862 88.9121 96.0968 88.6849 98.8208 85.7846C102.333 82.0423 102.506 78.2867 101.037 73.3149C100.183 76.2285 97.1384 78.9684 92.7452 77.3645L92.7586 77.3378ZM77.7098 88.9655L74.1312 85.3837C73.7306 84.9827 73.517 84.4481 73.517 83.8868V67.4609C73.517 66.8996 73.744 66.365 74.1312 65.964L77.7098 62.3821L81.2884 65.964C81.689 66.365 81.9026 66.8996 81.9026 67.4609V73.2614C80.5273 74.3974 79.3522 75.9077 78.511 78.113C76.962 82.1626 77.8967 84.9293 79.3522 87.3083L77.7232 88.9522L77.7098 88.9655Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M102.026 45.7825L91.8906 35.6383C91.0628 34.8097 89.7275 34.8097 88.8996 35.6383L87.591 36.9481C87.1904 37.3491 86.6563 37.5629 86.0955 37.5629H69.3108C68.75 37.5629 68.2158 37.3357 67.8153 36.9481L66.5067 35.6383C65.6788 34.8097 64.3435 34.8097 63.5156 35.6383L53.3807 45.7825C52.5528 46.6112 52.5528 47.9477 53.3807 48.7764L57.1863 52.5854C58.0142 53.4141 59.3495 53.4141 60.1774 52.5854L68.3093 44.446C68.7099 44.0451 69.244 43.8312 69.8048 43.8312H85.6014C86.1622 43.8312 86.6963 44.0584 87.0969 44.446L92.9989 50.3535C92.8654 50.5005 92.7586 50.6475 92.6117 50.7812C90.1548 52.9196 90.0212 57.8781 95.2022 60.6046C93.5865 56.3411 94.1607 53.9888 95.1087 52.4651L95.2422 52.5988C96.0701 53.4274 97.4054 53.4274 98.2333 52.5988L102.039 48.7897C102.867 47.9611 102.867 46.6246 102.039 45.7959L102.026 45.7825Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M108.195 31.4683C99.3817 21.6181 86.7631 15.978 73.5704 15.978C55.7442 15.978 39.2666 26.3895 31.602 42.508C30.2 45.4617 27.1555 47.3729 23.8573 47.3729H3.66765C3.29377 47.3729 3 47.6803 3 48.0412C3 48.4021 3.29377 48.7095 3.66765 48.7095H23.844C27.6496 48.7095 31.1614 46.4908 32.7905 43.0693C40.2414 27.4186 56.2516 17.3012 73.557 17.3012C86.3759 17.3012 98.6339 22.7809 107.193 32.3504C115.873 42.0402 119.852 54.5768 118.41 67.6212C116.06 88.8853 98.8876 105.645 77.5629 107.476C58.5617 109.107 40.9758 99.0697 32.7905 81.8953C31.1747 78.5006 27.6629 76.2953 23.844 76.2953H18.0621C17.6883 76.2953 17.3945 76.5893 17.3945 76.9636C17.3945 77.3378 17.6883 77.6318 18.0621 77.6318H23.844C27.1421 77.6318 30.1733 79.5297 31.5753 82.47C39.4002 98.8959 55.5706 108.987 73.517 108.987C74.8923 108.987 76.281 108.933 77.6697 108.813C99.622 106.915 117.315 89.6605 119.732 67.7683C121.214 54.3362 117.114 41.4388 108.181 31.4549L108.195 31.4683ZM33.7252 62.489C33.7252 62.1148 33.4314 61.8207 33.0575 61.8207H10.2106C9.83672 61.8207 9.54295 62.1148 9.54295 62.489C9.54295 62.8632 9.83672 63.1573 10.2106 63.1573H33.0575C33.4314 63.1573 33.7252 62.8632 33.7252 62.489ZM64.3702 32.5643L63.0616 31.2545C61.98 30.1719 60.2174 30.1719 59.1358 31.2545L49.0009 41.3987C47.9194 42.4813 47.9194 44.2455 49.0009 45.3281L52.8065 49.1372C53.8881 50.2197 55.6507 50.2197 56.7323 49.1372L64.8643 40.9977C65.1313 40.7304 65.5052 40.57 65.8791 40.57H81.6756C82.0495 40.57 82.4234 40.7304 82.6905 40.9977L88.1251 46.4374C86.8165 47.6403 86.1622 49.4713 86.3892 51.3558C86.6696 53.7749 88.2854 55.8732 90.9426 57.2632C91.0361 57.3167 91.1429 57.3434 91.2497 57.3434C91.4099 57.3434 91.5702 57.29 91.6904 57.1697C91.904 56.9826 91.9708 56.6885 91.8773 56.4346C90.5687 52.9863 90.769 50.9415 91.3832 49.5648C91.8105 49.8188 92.2912 49.9658 92.7853 49.9658C93.5331 49.9658 94.2274 49.6718 94.7482 49.1505L98.5538 45.3414C99.6354 44.2589 99.6354 42.4946 98.5538 41.4121L88.4189 31.2678C87.3373 30.1853 85.5747 30.1853 84.4931 31.2678L83.1845 32.5776C82.9175 32.8449 82.5436 33.0053 82.1563 33.0053H65.3717C64.9844 33.0053 64.6239 32.8583 64.3568 32.5776L64.3702 32.5643ZM91.0895 47.52C90.0746 48.8298 88.9396 50.9815 90.0613 55.098C88.726 54.0288 87.9115 52.6789 87.7379 51.2087C87.5643 49.7252 88.0984 48.255 89.1266 47.3596C89.2735 47.2259 89.407 47.0923 89.5272 46.9452C89.5272 46.9452 89.5539 46.9319 89.5672 46.9185C89.5806 46.9052 89.5806 46.8918 89.5939 46.8784C90.9426 45.4083 91.45 43.4302 91.45 41.9867C91.45 41.7061 91.4366 41.4388 91.3966 41.1848C91.5034 41.3452 91.6102 41.5189 91.6904 41.7194C92.4648 43.5104 91.9975 46.3572 91.0895 47.52ZM84.1459 33.5132L85.4545 32.2034C86.0154 31.6421 86.9367 31.6421 87.4975 32.2034L97.6324 42.3476C97.8995 42.6149 98.0597 42.9891 98.0597 43.3634C98.0597 43.7376 97.9128 44.1118 97.6324 44.3791L93.8268 48.1882C93.3728 48.6427 92.6518 48.6961 92.0909 48.3887C92.1043 48.3753 92.1177 48.3486 92.131 48.3219C93.4529 46.6245 93.8135 43.2698 92.9055 41.1715C92.358 39.8884 91.3565 39.1266 90.1014 39.0063C89.7942 38.9796 89.5405 39.14 89.4337 39.4073C89.3135 39.6746 89.3936 39.982 89.6073 40.1691C89.8343 40.3696 90.1147 40.9576 90.1014 41.96C90.1014 42.9357 89.7942 44.2989 89.0198 45.4216L83.6385 40.0354C83.1178 39.5142 82.41 39.2201 81.6756 39.2201H65.8791C65.1447 39.2201 64.437 39.5142 63.9162 40.0354L55.7842 48.1749C55.2368 48.7228 54.2887 48.7228 53.7412 48.1749L49.9357 44.3658C49.6686 44.0985 49.5084 43.7242 49.5084 43.35C49.5084 42.9758 49.6552 42.6016 49.9357 42.3343L60.0705 32.1901C60.6314 31.6287 61.5527 31.6287 62.1136 32.1901L63.4221 33.4998C63.9429 34.0211 64.6506 34.3151 65.385 34.3151H82.1697C82.9175 34.3151 83.6118 34.0211 84.1326 33.4998L84.1459 33.5132ZM114.444 65.1086H114.471C114.831 65.1086 115.125 64.8279 115.138 64.4671C115.165 63.8122 115.192 63.1573 115.192 62.489C115.192 58.8938 114.724 55.3252 113.816 51.877C113.723 51.5161 113.362 51.3023 113.002 51.3959C112.641 51.4894 112.427 51.8503 112.521 52.2111C113.402 55.5524 113.843 59.0007 113.843 62.489C113.843 63.1305 113.83 63.7721 113.803 64.4002C113.79 64.7744 114.07 65.0819 114.444 65.0952V65.1086ZM60.6848 82.5368C59.6032 83.6194 59.6032 85.3836 60.6848 86.4662L71.8212 97.6128C72.3686 98.1608 73.0763 98.4281 73.784 98.4281C74.4917 98.4281 75.2128 98.1608 75.7469 97.6128L79.6193 93.7369C80.2202 93.8305 80.7676 93.8973 81.2617 93.8973C84.9738 93.8973 85.9619 91.3312 86.7231 89.3798C87.0702 88.4977 87.3907 87.6557 87.8848 87.2281C88.6459 86.5598 89.5272 86.1321 90.542 85.6376C92.0242 84.9159 93.72 84.0872 95.4025 82.2963C98.7808 78.6877 99.4618 74.8919 97.766 69.1716C97.6858 68.8909 97.4188 68.6905 97.125 68.6905C96.8312 68.6905 96.5642 68.8909 96.4841 69.1716C96.0434 70.6953 94.9752 72.005 93.6532 72.6733C92.7452 73.1277 91.7304 73.3015 90.6488 73.1678C95.9767 69.8533 97.3921 64.5339 97.7526 61.5C98.2199 57.5572 97.352 53.3205 95.8164 52.0641C95.6161 51.9038 95.3491 51.8637 95.1221 51.9706C94.8951 52.0775 94.7348 52.3047 94.7348 52.5586C94.4811 62.3019 90.0613 63.8389 84.8803 65.4695V62.9835L86.8833 60.9787C87.4041 60.4575 87.6978 59.7491 87.6978 59.014C87.6978 58.2789 87.4041 57.5706 86.8833 57.0493L75.7469 45.9028C74.6653 44.8202 72.9027 44.8202 71.8212 45.9028L60.6848 57.0493C59.6032 58.1319 59.6032 59.8961 60.6848 60.9787L62.6877 62.9835V80.5187L60.6848 82.5235V82.5368ZM85.0406 66.8327C89.9411 65.2957 94.9885 63.7186 95.9232 54.8575C96.4306 56.4212 96.7244 58.7869 96.4307 61.3396C96.0701 64.3735 94.5612 69.9067 88.5391 72.807C88.2987 72.9272 88.1518 73.1678 88.1652 73.4351C88.1652 73.7024 88.3521 73.943 88.6058 74.0366C90.6221 74.7717 92.585 74.7182 94.2675 73.8762C95.3491 73.3282 96.2971 72.4328 96.9648 71.3635C97.8861 75.6137 97.1117 78.5273 94.4411 81.3875C92.9188 83.0046 91.4233 83.7397 89.9678 84.4481C88.9129 84.9559 87.9248 85.4505 87.0168 86.2257C86.2691 86.8672 85.8818 87.8562 85.4812 88.8987C84.6667 90.997 83.8922 92.9751 80.1935 92.4806C80.554 89.5135 78.965 87.3082 77.416 85.1564C75.3997 82.3363 73.3033 79.4227 75.1994 74.4375C77.149 69.3053 80.9813 68.1024 85.0406 66.8327ZM74.5852 83.2987L73.784 84.1006L70.6861 80.9998C70.4191 80.7325 70.2589 80.3583 70.2589 79.9841V63.5582C70.2589 63.1707 70.4057 62.8098 70.6861 62.5291L73.784 59.4284L76.8819 62.5291C77.149 62.7964 77.3092 63.1707 77.3092 63.5582V69.0513C75.9606 70.2141 74.7989 71.7645 73.9576 73.9831C72.4087 78.0461 73.2232 80.9197 74.5852 83.312V83.2987ZM64.023 62.7162C64.023 62.5425 63.9563 62.3687 63.8227 62.2484L61.6195 60.0432C61.0587 59.4818 61.0587 58.5596 61.6195 57.9983L72.7559 46.8517C73.3167 46.2904 74.238 46.2904 74.7989 46.8517L85.9352 57.9983C86.4961 58.5596 86.4961 59.4818 85.9352 60.0432L83.7587 62.2217C83.6252 62.342 83.5317 62.5291 83.5317 62.7162V65.8972C81.8359 66.4451 80.1534 67.0599 78.6312 68.0356V63.5449C78.6312 62.7964 78.3374 62.1014 77.8166 61.5802L74.2514 58.0116C73.9843 57.7443 73.5704 57.7443 73.3033 58.0116L69.7247 61.5802C69.204 62.1014 68.9102 62.8098 68.9102 63.5449V79.9707C68.9102 80.7192 69.204 81.4142 69.7247 81.9354L73.3033 85.5173C73.4235 85.6376 73.5971 85.7178 73.7707 85.7178C73.9443 85.7178 74.1179 85.651 74.238 85.5173L75.2929 84.4614C75.6267 84.9693 75.9739 85.4638 76.3211 85.9316C77.8968 88.1369 79.2588 90.0481 78.778 92.6677L74.7855 96.6639C74.2247 97.2252 73.3033 97.2252 72.7425 96.6639L61.6061 85.5173C61.0453 84.956 61.0453 84.0338 61.6061 83.4724L63.8094 81.2672C63.9296 81.1469 64.0097 80.9731 64.0097 80.7994V62.7029L64.023 62.7162ZM107.714 41.1581C107.834 41.3586 108.061 41.4655 108.275 41.4655C108.395 41.4655 108.515 41.4388 108.635 41.3586C108.942 41.1581 109.036 40.7438 108.849 40.4364C102.439 30.2387 91.9708 23.3556 80.1267 21.5246C79.7528 21.4711 79.419 21.7251 79.3656 22.0859C79.3122 22.4468 79.5659 22.7943 79.9264 22.8477C91.3832 24.6119 101.518 31.2812 107.727 41.1447L107.714 41.1581Z"
        fill="url(#paint0_linear_819_772)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_819_772"
          x1="40.8156"
          y1="97.3455"
          x2="87.3747"
          y2="16.7635"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
