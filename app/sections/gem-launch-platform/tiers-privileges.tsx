import { useTranslation } from 'react-i18next'

import { H2 } from '~/tenset-components'
import { Namespace } from '~/i18n'
import { tiersPrivileges } from '~/data/gem-launch-platform'
import { TierPrivilegesBox } from '~/components/gem-launch-platform'

export function GlpTiersPrivileges() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])

  return (
    <div className="flex flex-col gap-12">
      <H2 isBold>{t('tiers-privileges.title')}</H2>

      <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 max-w-xs sm:max-w-none self-center">
        {tiersPrivileges(t).map((tier, index) => (
          <TierPrivilegesBox
            key={index}
            tierPrivileges={tier}
            tier={index + 1}
          />
        ))}
      </div>
    </div>
  )
}
