export type PostsPaginationType = {
  data: Array<PostDataType>
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export type PostDataType = {
  id: number
  attributes: PostAttributesType
}

export type PostAttributesType = {
  slug: string
  title: string
  body: string
  date: string
  cover: PostImageType
  description: string
  PublicationDate: string
}

export type PostImageType = {
  data: {
    id: number
    attributes: {
      name: string
      alternativeText: string
      caption: string
      width: number
      height: number
      formats: {
        thumbnail: PostImageFormatType
        large: PostImageFormatType
        medium: PostImageFormatType
        small: PostImageFormatType
      }
      hash: string
      ext: string
      mime: string
      size: number
      url: string
      previewUrl: null
      provider: string
      provider_metadata: null
      createdAt: string
      updatedAt: string
    }
  }
}

export enum PostImageSize {
  THUMBNAIL = 'thumbnail',
  LARGE = 'large',
  MEDIUM = 'medium',
  SMALL = 'small',
}

export type PostImageFormatType = {
  name: string
  hash: string
  ext: string
  mime: string
  width: number
  height: number
  size: number
  path: null
  url: string
}
