import { AssetProps } from './index'

export default function ScDevelopment({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M51.7294 83.772H46.3758V47.2914H54.8709C56.4809 47.2914 57.7899 45.9825 57.7899 44.3725V35.2229H110.92V31.4924C110.92 30.3928 110.03 29.4896 108.918 29.4896H57.5804C57.1485 28.4294 56.1013 27.6702 54.884 27.6702H46.3889V16.2169C46.3889 15.2744 45.6297 14.5152 44.6873 14.5152H42.3573C41.4149 14.5152 40.6557 15.2744 40.6557 16.2169V29.4896H20.2883C19.1888 29.4896 18.2856 30.3798 18.2856 31.4924V35.2229H20.0527V43.2598C20.0527 44.8175 21.3093 46.0741 22.867 46.0741H31.0218C32.5794 46.0741 33.836 44.8175 33.836 43.2598V35.2229H40.6295V83.772H25.3016V95.9976H20.8774V119.232H60.3816V95.9976H51.7163V83.772H51.7294Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M113.172 56.8205H106.326V51.6109H96.692V56.8205H79.9897L71.1805 65.6298V115.763C71.1805 117.674 72.7251 119.218 74.6361 119.218H113.172C115.083 119.218 116.627 117.674 116.627 115.763V60.2762C116.627 58.3651 115.083 56.8205 113.172 56.8205Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M119.271 114.794H112.295C112.989 114.061 113.42 113.079 113.42 111.993V56.5064C113.42 54.242 111.575 52.3963 109.31 52.3963H103.119V47.8412C103.119 47.4747 102.831 47.1867 102.465 47.1867H98.3021V32.1075H107.072C107.439 32.1075 107.727 31.8195 107.727 31.453V27.7226C107.727 26.2565 106.535 25.0654 105.069 25.0654H101.182L43.1689 13.9393V12.447C43.1689 11.1512 42.1086 10.0909 40.8128 10.0909H38.4828C37.187 10.0909 36.1267 11.1512 36.1267 12.447V14.1225L18.8616 25.0654H16.427C14.9609 25.0654 13.7698 26.2565 13.7698 27.7226V31.453C13.7698 31.8195 14.0578 32.1075 14.4243 32.1075H15.5369V39.49C15.5369 41.4011 17.0945 42.9588 19.0056 42.9588H27.1604C29.0715 42.9588 30.6291 41.4011 30.6291 39.49V32.1075H36.1136V79.3477H21.4533C21.0868 79.3477 20.7989 79.6357 20.7989 80.0022V91.5733H17.0291C16.6626 91.5733 16.3746 91.8613 16.3746 92.2278V114.807H10.6545C10.288 114.807 10 115.095 10 115.462C10 115.828 10.288 116.116 10.6545 116.116H119.271C119.638 116.116 119.926 115.828 119.926 115.462C119.926 115.095 119.638 114.807 119.271 114.807V114.794ZM101.797 48.4825V56.8075H99.467H93.4851V48.4825H101.81H101.797ZM99.5194 63.7188C99.1529 63.7188 98.8649 64.0068 98.8649 64.3733V66.1011L91.6264 63.6403V60.1977C91.6264 60.1977 91.6264 60.1977 91.6264 60.1846L93.5244 58.1034H98.9827L101.09 63.7188H99.5194ZM106.889 65.0277V71.7427H100.174V65.0277H106.889ZM105.069 26.3612C105.815 26.3612 106.418 26.9634 106.418 27.7095V30.7855H54.583V26.8194C54.583 26.6623 54.5568 26.5183 54.5437 26.3743H105.069V26.3612ZM43.182 15.2613L94.2312 25.0523H54.1118C53.4965 23.9789 52.3447 23.2328 51.0226 23.2328H43.182V15.2613ZM51.0226 24.5418C52.2792 24.5418 53.2871 25.5628 53.2871 26.8063V29.254H45.7606C45.3941 29.254 45.1061 29.542 45.1061 29.9085V34.5684C45.1061 34.9349 45.3941 35.2228 45.7606 35.2228H53.2871V40.5765C53.2871 41.8331 52.2661 42.841 51.0226 42.841H40.8259V24.5418H51.0226ZM53.2871 30.563V33.9139H46.4151V30.563H53.2871ZM43.182 80.6436H47.2267V91.5602H43.182V80.6436ZM40.1714 44.163H41.873V106.77H37.4488V32.0945H39.5038V43.5085C39.5038 43.875 39.7918 44.163 40.1583 44.163H40.1714ZM37.4619 12.4339C37.4619 11.858 37.9331 11.3868 38.509 11.3868H40.839C41.4149 11.3868 41.8861 11.858 41.8861 12.4339V23.2328H40.1845C39.818 23.2328 39.53 23.5208 39.53 23.8873V25.0523H37.4749V12.4339H37.4619ZM36.1529 15.6671V25.0523H21.3094L36.1267 15.6671H36.1529ZM29.3594 39.4769C29.3594 40.6681 28.3908 41.6367 27.1997 41.6367H19.0449C17.8537 41.6367 16.8851 40.6681 16.8851 39.4769V32.0945H29.3725V39.4769H29.3594ZM30.0139 30.7855H15.0787V27.7095C15.0787 26.9634 15.6809 26.3612 16.427 26.3612H39.5038V30.7855H30.0008H30.0139ZM36.8074 108.079H45.0669C45.4465 108.079 45.7475 108.38 45.7475 108.76V109.807H33.8361V108.76C33.8361 108.38 34.1371 108.079 34.5167 108.079H36.7943H36.8074ZM22.134 80.6436H36.1529V91.5602H22.1078V80.6436H22.134ZM28.4432 111.784V114.781H17.6966V92.8561H36.1398V106.757H34.5167C33.4172 106.757 32.5271 107.647 32.5271 108.747V109.794H30.4197C29.3202 109.794 28.4301 110.684 28.4301 111.784H28.4432ZM49.8577 114.781H29.7521V111.784C29.7521 111.404 30.0532 111.103 30.4328 111.103H49.1639C49.5435 111.103 49.8446 111.404 49.8446 111.784V114.781H49.8577ZM55.905 114.781H51.1666V111.784C51.1666 110.684 50.2765 109.794 49.177 109.794H47.0696V108.747C47.0696 107.647 46.1795 106.757 45.08 106.757H43.1951V92.8561H55.905V114.781ZM57.214 114.781V92.2016C57.214 91.8351 56.926 91.5471 56.5595 91.5471H48.5487V79.976C48.5487 79.6095 48.2607 79.3215 47.8942 79.3215H43.1951V44.1499H51.0357C53.0122 44.1499 54.6092 42.5399 54.6092 40.5765V32.0814H97.0062V47.1605H92.8437C92.4772 47.1605 92.1892 47.4485 92.1892 47.815V52.3701H76.1414C76.0498 52.3701 75.9713 52.3832 75.8928 52.4225C75.8142 52.4618 75.7357 52.501 75.6833 52.5665L66.8741 61.3758C66.8741 61.3758 66.7693 61.5067 66.7301 61.5852C66.6908 61.6637 66.6777 61.7554 66.6777 61.8339V111.967C66.6777 113.053 67.1097 114.035 67.8034 114.768H57.214V114.781ZM75.487 54.6084V58.3782C75.487 59.9228 74.2304 61.1794 72.6858 61.1794H68.916L75.487 54.6084ZM112.125 111.98C112.125 113.524 110.868 114.781 109.323 114.781H70.7878C69.2433 114.781 67.9867 113.524 67.9867 111.98V62.5014H72.6858C74.9503 62.5014 76.7959 60.6558 76.7959 58.3913V53.6922H92.1892V57.462C92.1892 57.462 92.2023 57.5667 92.2285 57.6321L90.4614 59.5563H85.1601C84.7936 59.5563 84.5057 59.8443 84.5057 60.2108V66.0618C84.5057 66.4283 84.7936 66.7163 85.1601 66.7163H91.0112C91.3777 66.7163 91.6656 66.4283 91.6656 66.0618V65.0408L98.9042 67.5017V72.4102C98.9042 72.7767 99.1921 73.0647 99.5586 73.0647H107.583C107.949 73.0647 108.237 72.7767 108.237 72.4102V64.3863C108.237 64.0198 107.949 63.7319 107.583 63.7319H102.53L100.423 58.1165H102.491C102.857 58.1165 103.145 57.8285 103.145 57.462V53.6922H109.337C110.881 53.6922 112.138 54.9488 112.138 56.4933V111.98H112.125ZM90.3436 60.8522V65.3943H85.8015V60.8522H90.3436ZM27.9981 103.223H21.4795C21.113 103.223 20.8251 103.511 20.8251 103.877C20.8251 104.244 21.113 104.532 21.4795 104.532H27.9981C28.3646 104.532 28.6526 104.244 28.6526 103.877C28.6526 103.511 28.3646 103.223 27.9981 103.223ZM103.708 96.3772C104.52 95.6311 105.03 94.5839 105.03 93.4059V92.2278C105.03 90.0026 103.224 88.1962 100.998 88.1832V86.6255C100.998 86.259 100.711 85.971 100.344 85.971C99.9775 85.971 99.6895 86.259 99.6895 86.6255V88.1832H97.3465V86.6255C97.3465 86.259 97.0585 85.971 96.692 85.971C96.3255 85.971 96.0376 86.259 96.0376 86.6255V88.1832H94.493C94.1265 88.1832 93.8385 88.4711 93.8385 88.8376V105.88C93.8385 106.247 94.1265 106.535 94.493 106.535H96.8098V108.55C96.8098 108.917 97.0978 109.205 97.4643 109.205C97.8308 109.205 98.1188 108.917 98.1188 108.55V106.535H100.239V108.55C100.239 108.917 100.527 109.205 100.894 109.205C101.26 109.205 101.548 108.917 101.548 108.55V106.535H102.347C104.847 106.535 106.876 104.506 106.876 102.006V100.671C106.876 98.6548 105.541 96.9662 103.721 96.3772H103.708ZM95.1475 89.4921H100.985C102.491 89.4921 103.721 90.7225 103.721 92.2278V93.4059C103.721 94.9112 102.491 96.1416 100.985 96.1416H95.1475V89.4921ZM105.567 102.006C105.567 103.786 104.127 105.226 102.347 105.226H95.1475V97.4505H102.347C104.127 97.4505 105.567 98.8904 105.567 100.671V102.006ZM28.0112 100.239H21.4926C21.1261 100.239 20.8381 100.527 20.8381 100.893C20.8381 101.26 21.1261 101.548 21.4926 101.548H28.0112C28.3777 101.548 28.6657 101.26 28.6657 100.893C28.6657 100.527 28.3777 100.239 28.0112 100.239ZM85.0685 101.207C84.702 101.207 84.414 101.495 84.414 101.862V105.37C84.414 105.736 84.702 106.024 85.0685 106.024C85.435 106.024 85.723 105.736 85.723 105.37V101.862C85.723 101.495 85.435 101.207 85.0685 101.207ZM28.0112 97.3066H21.4926C21.1261 97.3066 20.8381 97.5945 20.8381 97.961C20.8381 98.3275 21.1261 98.6155 21.4926 98.6155H28.0112C28.3777 98.6155 28.6657 98.3275 28.6657 97.961C28.6657 97.5945 28.3777 97.3066 28.0112 97.3066ZM75.3953 92.1623C75.0288 92.1623 74.7409 92.4503 74.7409 92.8168V105.37C74.7409 105.736 75.0288 106.024 75.3953 106.024C75.7619 106.024 76.0498 105.736 76.0498 105.37V92.8168C76.0498 92.4503 75.7619 92.1623 75.3953 92.1623ZM80.2385 96.1547C79.872 96.1547 79.584 96.4426 79.584 96.8092V105.37C79.584 105.736 79.872 106.024 80.2385 106.024C80.605 106.024 80.893 105.736 80.893 105.37V96.8092C80.893 96.4426 80.605 96.1547 80.2385 96.1547Z"
        fill="url(#paint0_linear_826_797)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_826_797"
          x1="33.3648"
          y1="129.324"
          x2="96.6135"
          y2="19.7772"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
