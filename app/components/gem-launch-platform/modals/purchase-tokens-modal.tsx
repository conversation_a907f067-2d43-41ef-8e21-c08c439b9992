import { useTranslation } from 'react-i18next'

import TokensAvailableOnExchangesBlock from '~/components/blocks/available-on-exchanges'
import { Namespace } from '~/i18n'
import {
  H3,
  Modal,
  ModalContent,
  ModalMain,
  ModalTrigger,
} from '~/tenset-components'

export function PurchaseTokensModal() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  return (
    <>
      <Modal>
        <ModalTrigger>{t('purchase-tokens.action')}</ModalTrigger>

        <ModalContent>
          <ModalMain>
            <TokensAvailableOnExchangesBlock
              HeaderLevel={H3}
              description={t('purchase-tokens.header')}
            />
          </ModalMain>
        </ModalContent>
      </Modal>
    </>
  )
}
