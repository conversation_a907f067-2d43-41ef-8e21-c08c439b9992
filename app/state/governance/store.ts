import type { Observable } from 'rxjs'
import { BehaviorSubject } from 'rxjs'
import { filter, take } from 'rxjs/operators'

import { Query } from './query'
import type { VotingDTO } from './voting.dto'
import { Dispatcher } from './dispatcher'
import type { Repository } from './repository'
import { VoteObserver } from './vote.observer'
import { FirebaseClient, VotingRepository } from './firebase'

import { getEnvironment } from '~/environment'

class Store {
  get ready() {
    return this._ready
  }

  protected _ready = false

  protected repository: Repository<VotingDTO> | undefined

  protected _ready$ = new BehaviorSubject(this.ready)

  readonly ready$: Observable<boolean> = this._ready$.asObservable()

  readonly dispatcher = new Dispatcher()

  constructor() {
    this.register()
  }

  async select() {
    await this.waitUntilIsReady()

    if (!this.repository) return []

    return this.repository.get(new Query())
  }

  protected async register() {
    if (this.ready || typeof window === 'undefined') return

    const firebaseClient = new FirebaseClient({
      apiKey: getEnvironment().FIREBASE_API_KEY!,
      authDomain: getEnvironment().FIREBASE_AUTH_DOMAIN!,
      databaseURL: getEnvironment().FIREBASE_DATABASE_URL!,
      projectId: getEnvironment().FIREBASE_PROJECT_ID!,
      storageBucket: getEnvironment().FIREBASE_STORAGE_BUCKET!,
      messagingSenderId: getEnvironment().FIREBASE_MESSAGING_SENDER_ID!,
      appId: getEnvironment().FIREBASE_APP_ID!,
      measurementId: getEnvironment().FIREBASE_MEASUREMENT_ID!,
      apiHost: getEnvironment().FIREBASE_API_HOST!,
    })

    await firebaseClient.connect()

    this.repository = new VotingRepository(firebaseClient)

    const observer = new VoteObserver(
      firebaseClient,
      this.repository as VotingRepository,
      this.dispatcher
    )

    setTimeout(() => observer.observe(), 600)

    this._ready$.next((this._ready = true))
  }

  protected waitUntilIsReady() {
    return new Promise(resolve => {
      if (this.ready) return resolve(true)

      this.ready$
        .pipe(
          filter(ready => ready),
          take(1)
        )
        .subscribe(() => resolve(true))
    })
  }
}

export const store = new Store()
