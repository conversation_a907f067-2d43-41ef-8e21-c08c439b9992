import { AssetProps } from './index'

export default function MarketingUP({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M112.606 28.378H106.106C100.614 28.378 96.155 32.828 96.155 38.3495V41.249H84.9945C81.2785 24.4449 66.3012 11.8639 48.3764 11.8639C27.6551 11.8639 10.864 28.668 10.864 49.4052C10.864 76.0169 33.3739 83.8075 33.3739 110.016C33.3739 115.903 38.148 120.693 44.0558 120.693H52.7222C58.6174 120.693 63.3914 115.903 63.3914 110.016C63.3914 84.0975 85.3975 76.1934 85.8762 50.275H96.155V53.2627C96.155 58.7716 100.614 63.2468 106.106 63.2468H112.606C118.111 63.2468 122.557 58.7716 122.557 53.2627V38.3495C122.557 32.828 118.111 28.378 112.606 28.378ZM46.1972 35.7905C51.1098 34.845 55.5564 37.3032 57.597 41.249H38.9668C40.3902 38.4756 43.0606 36.4082 46.1972 35.7905ZM51.1476 76.1178H45.454C43.174 76.1178 41.3601 74.2142 41.4861 71.9451L41.5491 70.445H45.4414V50.275H51.2862V65.5159H54.8006L55.1155 71.9451C55.2289 74.2142 53.415 76.1178 51.1476 76.1178ZM116.775 52.9854C116.775 55.431 114.81 57.3975 112.379 57.3975H106.358C103.914 57.3975 101.937 55.431 101.937 52.9854V38.6143C101.937 36.1939 103.914 34.2147 106.358 34.2147H112.379C114.81 34.2147 116.775 36.1939 116.775 38.6143V52.9854Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M110.095 25.4682H103.613C97.7968 25.4682 93.0607 30.2075 93.0607 36.0406V38.3034H83.0484C79.0535 21.2946 63.5639 9 46.0393 9C25.0599 9 8 26.0716 8 47.0655C8 60.5669 13.7913 69.3667 19.3942 77.8774C25.085 86.5138 30.4492 94.6725 30.4492 107.508C30.4492 113.73 35.5119 118.784 41.7303 118.784H50.3733C56.5918 118.784 61.6419 113.73 61.6419 107.508C61.6419 94.6725 67.0061 86.5138 72.6969 77.8774C78.0862 69.6936 83.6388 61.2206 84.0408 48.5614H93.0607V50.9123C93.0607 56.7453 97.7968 61.4846 103.613 61.4846H110.095C115.912 61.4846 120.648 56.7453 120.648 50.9123V36.028C120.648 30.195 115.912 25.4557 110.095 25.4557V25.4682ZM60.3731 107.52C60.3731 113.039 55.8883 117.539 50.3608 117.539H41.7178C36.1903 117.539 31.6929 113.052 31.6929 107.52C31.6929 107.307 31.6803 107.093 31.6803 106.879H60.3982C60.3982 107.093 60.3856 107.294 60.3856 107.52H60.3731ZM60.4233 105.622H31.6552C31.6175 104.604 31.5421 103.611 31.4291 102.655H60.6494C60.5364 103.611 60.4736 104.604 60.4233 105.622ZM71.6291 77.1986C66.9935 84.2385 62.2449 91.492 60.8128 101.398H31.2532C29.8211 91.492 25.0724 84.2385 20.4369 77.1986C14.9345 68.8388 9.25625 60.2024 9.25625 47.0781C9.25625 26.7756 25.7634 10.2697 46.0393 10.2697C62.8981 10.2697 77.7847 22.0111 81.767 38.3159H55.6119C55.5616 38.2405 55.5114 38.1651 55.4611 38.0771C55.3858 37.9514 55.3104 37.8257 55.235 37.7126C55.1471 37.5868 55.0591 37.4486 54.9712 37.3228C54.8833 37.2097 54.8079 37.0965 54.7199 36.9834C54.6194 36.8577 54.5315 36.7445 54.431 36.6188C54.3431 36.5057 54.2551 36.4051 54.1546 36.3045C54.0541 36.1914 53.9411 36.0783 53.8406 35.9777C53.7401 35.8772 53.6396 35.7766 53.5391 35.6886C53.426 35.588 53.3129 35.4874 53.1999 35.3869C53.0994 35.2989 52.9863 35.1983 52.8858 35.1229C52.7728 35.0349 52.6471 34.9343 52.5215 34.8463C52.4084 34.7583 52.2954 34.6829 52.1823 34.5949C52.0567 34.5069 51.9311 34.4315 51.8054 34.356C51.6798 34.2806 51.5668 34.2052 51.4411 34.1298C51.3155 34.0543 51.1899 33.9915 51.0643 33.9286C50.9386 33.8658 50.8004 33.7903 50.6748 33.7275C50.5492 33.6646 50.4236 33.6143 50.2979 33.564C50.1598 33.5012 50.0216 33.4384 49.8708 33.3881C49.7452 33.3378 49.6196 33.3001 49.5065 33.2623C49.3558 33.2121 49.205 33.1618 49.0417 33.1115C48.9286 33.0738 48.803 33.0486 48.6899 33.0235C48.5266 32.9858 48.3633 32.9355 48.2 32.9104C48.0869 32.8852 47.9739 32.8726 47.8608 32.8475C47.6849 32.8223 47.5091 32.7846 47.3332 32.7595C47.2201 32.7469 47.1196 32.7469 47.0066 32.7343C46.8181 32.7218 46.6297 32.6967 46.4538 32.6841C46.3533 32.6841 46.2403 32.6841 46.1398 32.6841C45.9513 32.6841 45.7503 32.6841 45.5619 32.6841C45.4614 32.6841 45.3483 32.6966 45.2478 32.7092C45.0594 32.7092 44.8584 32.7344 44.6699 32.7469C44.3684 32.7846 44.0669 32.8224 43.7654 32.8852C40.9138 33.4384 38.3761 35.1732 36.7556 37.5742C36.5294 37.9137 36.3159 38.2783 36.1149 38.6554C35.6249 39.5857 35.2732 40.6039 35.0722 41.6725C34.2808 45.9592 35.9264 50.158 39.3811 52.7099L38.6274 68.0216L38.552 69.5176C38.4892 70.7873 38.9289 71.9941 39.8083 72.9118C40.6876 73.8295 41.8685 74.3449 43.1373 74.3449H48.8156C50.0844 74.3449 51.2653 73.8421 52.1446 72.9118C53.024 71.9941 53.4637 70.7873 53.4009 69.5176L53.1245 63.76H57.848C58.1998 63.76 58.4761 63.4835 58.4761 63.1315V48.574H82.8097C82.3951 60.8686 76.943 69.1656 71.6542 77.1986H71.6291ZM54.0918 38.3159H37.7354C37.7354 38.3159 37.7354 38.3034 37.748 38.2908C37.7857 38.2405 37.8234 38.2028 37.8485 38.1525C38.0118 37.9137 38.1877 37.6874 38.3636 37.4611C38.4389 37.3731 38.5269 37.2851 38.6023 37.1971C38.753 37.0337 38.9038 36.8577 39.0671 36.7068C39.1676 36.6063 39.2681 36.5183 39.3686 36.4303C39.5193 36.292 39.6826 36.1537 39.8459 36.028C39.959 35.94 40.0721 35.852 40.1851 35.7766C40.3484 35.6509 40.5243 35.5377 40.7002 35.4372C40.8258 35.3617 40.9389 35.2863 41.0645 35.2234C41.2529 35.1229 41.4288 35.0223 41.6173 34.9343C41.7429 34.8714 41.8559 34.8086 41.9816 34.7583C42.1951 34.6703 42.4087 34.5823 42.6223 34.5069C42.7353 34.4692 42.8358 34.4189 42.9489 34.3937C43.2755 34.2932 43.6147 34.2052 43.9539 34.1298C44.4815 34.0292 45.0091 33.9789 45.5242 33.9537C45.7001 33.9537 45.8634 33.9538 46.0393 33.9663C46.3784 33.9663 46.7302 33.9663 47.0694 34.0166C47.2704 34.0418 47.4714 34.092 47.6598 34.1172C47.9613 34.1675 48.2628 34.2303 48.5518 34.3057C48.7528 34.356 48.9538 34.4315 49.1548 34.5069C49.4311 34.6074 49.7075 34.6954 49.9588 34.8212C50.1598 34.9092 50.3482 35.0097 50.5366 35.1103C50.7879 35.2486 51.0391 35.3869 51.2778 35.5377C51.4663 35.6509 51.6421 35.7891 51.818 35.9148C52.0441 36.0783 52.2577 36.2669 52.4713 36.4554C52.6346 36.6063 52.7979 36.7571 52.9486 36.908C53.1496 37.1091 53.3381 37.3354 53.5139 37.5617C53.6521 37.7377 53.7903 37.9011 53.9285 38.0897C53.9788 38.1651 54.029 38.2531 54.0793 38.3285L54.0918 38.3159ZM48.9286 63.7726H51.8306L52.1195 69.593C52.1698 70.5107 51.8431 71.3907 51.2024 72.057C50.5618 72.7232 49.7075 73.1004 48.7779 73.1004H43.0996C42.17 73.1004 41.3158 72.7358 40.6751 72.057C40.0469 71.3907 39.7203 70.5107 39.758 69.593L39.8083 68.7005H43.0871C43.4388 68.7005 43.7152 68.4239 43.7152 68.0719V48.5866H48.3005V63.144C48.3005 63.496 48.5769 63.7726 48.9286 63.7726ZM119.379 50.9374C119.379 56.079 115.208 60.2527 110.083 60.2527H103.601C98.4751 60.2527 94.3044 56.0664 94.3044 50.9374V47.958C94.3044 47.6061 94.028 47.3295 93.6763 47.3295H57.8103C57.4586 47.3295 57.1822 47.6061 57.1822 47.958V62.5154H49.5568V47.958C49.5568 47.6061 49.2804 47.3295 48.9286 47.3295H43.0871C42.7353 47.3295 42.4589 47.6061 42.4589 47.958V67.4434H39.8711L40.5997 52.4585C40.5997 52.2448 40.5118 52.0311 40.3359 51.9054C37.0948 49.6551 35.537 45.8335 36.2656 41.9239C36.3536 41.4839 36.4666 41.0439 36.6048 40.629C36.6551 40.4908 36.7053 40.365 36.7681 40.2393C36.8561 40.0256 36.944 39.8119 37.0445 39.5982H93.6763C94.028 39.5982 94.3044 39.3217 94.3044 38.9697V36.0783C94.3044 30.9367 98.4751 26.763 103.601 26.763H110.083C115.208 26.763 119.379 30.9492 119.379 36.0783V50.9625V50.9374ZM35.1601 17.1461C35.0345 16.8192 34.6702 16.6558 34.3436 16.7941C27.497 19.4718 21.6429 24.4248 17.8616 30.7481C17.6857 31.0498 17.7862 31.427 18.0751 31.6155C18.1756 31.6784 18.2887 31.7035 18.4018 31.7035C18.6153 31.7035 18.8289 31.5904 18.9419 31.4018C22.5851 25.3174 28.2131 20.5403 34.8084 17.9632C35.135 17.8375 35.2858 17.4729 35.1601 17.1461ZM109.857 31.3138H103.852C101.076 31.3138 98.8269 33.5641 98.8269 36.3297V50.6483C98.8269 53.4265 101.088 55.6767 103.852 55.6767H109.857C112.621 55.6767 114.869 53.4139 114.869 50.6483V36.3297C114.869 33.5641 112.621 31.3138 109.857 31.3138ZM113.613 50.6483C113.613 52.7351 111.93 54.4196 109.857 54.4196H103.852C101.767 54.4196 100.083 52.7225 100.083 50.6483V36.3297C100.083 34.2555 101.779 32.5709 103.852 32.5709H109.857C111.93 32.5709 113.613 34.2555 113.613 36.3297V50.6483Z"
        fill={color ?? 'url(#paint0_linear_941_1244)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_941_1244"
          x1="26.1277"
          y1="111.53"
          x2="85.1319"
          y2="9.41238"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
