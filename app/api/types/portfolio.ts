export interface PortfolioApiTokenGraphStamp {
  timestamp: number
  low: string
  high: string
  avg: number
  volume: number
}

export interface PortfolioApiNftData {
  id: string
  quantity: number
  value: number
}

export interface PortfolioApiTokenData {
  slug: string
  name: string
  icon: string
  type: 'nft' | 'crypto'
  staking: boolean
  imgUrl: string
  color: string
  shortcut: string
  availableTokens: number
  initialValue: number
  initialValueDesc?: string
  currentValue: number
  price: number
  profit: number
  nftData?: {
    cols: string[]
    nfts: PortfolioApiNftData[]
  }
  data?: {
    precision: number
    graph: PortfolioApiTokenGraphStamp[]
  }
}

export interface PortfolioApiData {
  tokens: PortfolioApiTokenData[]
  initialValue: number
  portfolioValue: number
  totalProfit: number
  buybacks: number
  buybacksValue: number
}
