export function TotalBurnedPrediction() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      data-name="Layer 1"
      viewBox="0 0 1053.7 425"
    >
      <defs>
        <linearGradient
          id="linear-gradient"
          x1={126.4}
          x2={126.4}
          y1={-31.2}
          y2={-0.7}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-2"
          x1={248.8}
          x2={248.8}
          y1={-31.2}
          y2={28.8}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-3"
          x1={368.5}
          x2={368.5}
          y1={-31.2}
          y2={47.9}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-4"
          x1={493.7}
          x2={493.7}
          y1={-31.2}
          y2={80.4}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-5"
          x1={614.7}
          x2={614.7}
          y1={-31.2}
          y2={115.1}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-6"
          x1={738.6}
          x2={738.6}
          y1={-31.2}
          y2={168.2}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-7"
          x1={861}
          x2={861}
          y1={-31.2}
          y2={230.6}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-8"
          x1={983.5}
          x2={983.5}
          y1={-31.2}
          y2={317.2}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0} stopColor="#7700b3" stopOpacity={0} />
          <stop offset={0.5} stopColor="#b847ee" />
          <stop offset={1} stopColor="#fd62d5" />
        </linearGradient>
        <linearGradient
          id="linear-gradient-9"
          x1={368.5}
          x2={983.5}
          y1={189.2}
          y2={189.2}
          gradientTransform="matrix(1 0 0 -1 0 350.7)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.2} stopColor="#7700b3" stopOpacity={0.4} />
          <stop offset={0.8} stopColor="#7700b3" stopOpacity={0.1} />
          <stop offset={0.9} stopOpacity={0} />
          <stop offset={1} stopOpacity={0} />
          <stop offset={1} stopColor="#7700b3" stopOpacity={0} />
        </linearGradient>
        <style>
          {
            '.cls-10,.cls-8{fill:none}.cls-8{stroke-linecap:round;stroke-linejoin:round;stroke:#7700b3}.cls-10{stroke:#1a1a1a;stroke-miterlimit:10}.cls-13,.cls-15{fill:#fd62d5;stroke-width:0}.cls-15{fill:#fff}'
          }
        </style>
      </defs>
      <path
        d="M33.3 222.1v-2.2l5.8-8.7 1.3 1-5.4 8.1.2.3h9v1.4H33.3Zm7.2 3.3v-9.8h1.7v9.8h-1.7ZM51.6 225.8c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 225.4v-13.9h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9v-11.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM38.7 179.8c-1.1 0-2-.2-2.7-.7-.7-.4-1.3-1-1.7-1.7s-.6-1.5-.6-2.3h1.8c0 .6 0 1.1.4 1.6s.6.8 1.1 1.1c.5.3 1 .4 1.7.4s1.2-.1 1.7-.4.8-.6 1.1-1.1.4-1 .4-1.6 0-1.1-.4-1.6c-.3-.5-.6-.8-1.1-1.1-.5-.3-1-.4-1.7-.4s-.9 0-1.4.2-.9.4-1.2.7h-1.7l.7-7.4h7.3v1.6h-6.2l.5-.6-.5 5.4-.5-.2c.4-.4.8-.7 1.3-.9.5-.2 1.1-.3 1.8-.3 1.1 0 2 .2 2.7.7.7.4 1.3 1 1.6 1.7s.6 1.4.6 2.2v.3c0 .7-.2 1.5-.6 2.2s-.9 1.3-1.7 1.7c-.7.4-1.6.7-2.7.7v-.2ZM51.6 179.8c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5s-.8-1.3-1-2.2-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6s-.5 1.5-1 2.2-1 1.1-1.8 1.5c-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 179.5v-13.9h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9v-11.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM38.7 133.9c-1 0-1.9-.2-2.5-.5-.7-.4-1.2-.8-1.6-1.5s-.7-1.3-.9-2.1-.3-1.6-.3-2.5v-.9c0-2.2.5-3.9 1.6-5.1 1.1-1.2 2.7-1.8 4.7-1.8H41c.4 0 .7 0 1.1.1v1.6c-.4 0-.8 0-1.2-.1h-1.3c-1.2 0-2.1.2-2.7.6s-1.2 1-1.4 1.9c-.3.8-.4 1.8-.4 2.9v.9l-.5 1.7c0-1 .2-1.8.6-2.5.4-.7.9-1.2 1.5-1.5.7-.3 1.4-.5 2.3-.5s1.8.2 2.5.6 1.2.9 1.5 1.6c.3.7.5 1.5.5 2.4s-.2 1.7-.6 2.4c-.4.7-.9 1.3-1.6 1.7s-1.6.6-2.7.6h.1Zm0-1.6c.7 0 1.3-.1 1.7-.4.5-.3.8-.6 1.1-1.1.2-.5.4-1 .4-1.6s0-1.1-.3-1.6-.6-.8-1-1.1-1-.4-1.7-.4-1.2.1-1.7.4c-.5.2-.9.6-1.2 1.1s-.4 1-.4 1.7 0 1.1.3 1.5.6.8 1.1 1.1 1.1.4 1.8.4ZM51.6 133.9c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5s-.8-1.3-1-2.2-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9c.8.6 1.5 1.4 1.9 2.4s.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6s-.5 1.5-1 2.2-1 1.1-1.8 1.5c-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 133.5v-13.9h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9v-11.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM36.4 87.6l5.9-12.1v-.2h-7.7v-1.6h9.3v2.2l-5.6 11.7h-1.9Zm-1.8-10.1v-3.8h1.5v3.8h-1.5ZM51.6 87.9c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5s-.8-1.3-1-2.2-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9c.8.6 1.5 1.4 1.9 2.4s.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6s-.5 1.5-1 2.2-1 1.1-1.8 1.5c-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 87.6V73.7h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9V75.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM38.5 42c-1 0-1.9-.2-2.6-.5-.8-.4-1.4-.8-1.8-1.5s-.7-1.4-.7-2.2 0-1.1.3-1.5c.2-.4.5-.8.9-1.2.4-.3.8-.6 1.3-.8v-.2c-.4-.2-.7-.4-1-.7-.3-.3-.6-.6-.7-1-.2-.4-.3-.8-.3-1.3 0-.8.2-1.5.6-2 .4-.6 1-1 1.7-1.3s1.5-.4 2.3-.4 1.6 0 2.3.4 1.3.7 1.7 1.3c.4.6.6 1.2.6 2s0 .9-.3 1.3c-.2.4-.4.7-.7 1s-.7.5-1.1.7v.2c.5.2.9.4 1.3.8s.7.7.9 1.2c.2.4.3.9.3 1.5s-.2 1.6-.7 2.2-1 1.1-1.8 1.5-1.6.5-2.7.5h.2Zm0-1.6c.7 0 1.3-.1 1.8-.3s.9-.5 1.1-1c.3-.4.4-.9.4-1.5s0-1-.4-1.4c-.3-.4-.6-.7-1.1-.9-.5-.2-1.1-.3-1.8-.3s-1.3 0-1.8.3c-.5.2-.9.5-1.1.9-.3.4-.4.9-.4 1.4s0 1 .4 1.5c.3.4.6.7 1.1 1 .5.2 1.1.3 1.8.3Zm0-6.9c.9 0 1.6-.2 2.1-.6s.8-.9.8-1.7-.3-1.4-.8-1.7c-.5-.4-1.2-.5-2.1-.5s-1.5.2-2.1.6c-.5.4-.8.9-.8 1.7s.3 1.3.8 1.7c.5.4 1.2.6 2.1.6ZM51.6 42c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5s-.8-1.3-1-2.2-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6s-.5 1.5-1 2.2-1 1.1-1.8 1.5c-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 41.6V27.7h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9V29.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM38.6 271.7c-1 0-1.9-.2-2.6-.6s-1.3-.9-1.6-1.6c-.4-.7-.6-1.4-.6-2.2h1.8c0 .9.3 1.5.8 2.1.6.5 1.3.8 2.2.8s1.2-.1 1.6-.3c.5-.2.8-.6 1.1-1s.4-.9.4-1.5c0-.9-.3-1.6-.8-2.1-.6-.5-1.3-.8-2.2-.8s-.7 0-1 .1c-.3 0-.5.2-.8.4l-.9-1.6 5.3-3.9-.2-.3h-6.7v-1.6h8.5v2.5l-4.4 3.2h-1.3c.2 0 .5 0 .7-.1h.9c1 0 1.9.2 2.6.6s1.3.9 1.6 1.5.6 1.3.6 2.1v.3c0 .7-.2 1.4-.6 2.1s-.9 1.2-1.6 1.6c-.7.4-1.6.6-2.7.6v-.3ZM51.6 271.7c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5Zm0-1.6c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 271.4v-13.9h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9v-11.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM34.2 317.3V315c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1H34c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6H34h.2ZM51.6 317.7c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 317.3v-13.9h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9v-11.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM40.4 363.3v-13l.8.6h-3.9v-1.6h4.9v13.9h-1.8ZM51.6 363.6c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5Zm0-1.6c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM65 363.3v-13.9h2.5l4.1 9.3h.3l4.1-9.3h2.5v13.9h-1.9v-11.8l-3.9 9h-2.3l-4-8.9h.2v11.8h-1.7ZM105 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM122.4 404.1c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9c.8.6 1.5 1.4 1.9 2.4s.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6s-.5 1.5-1 2.2c-.5.6-1 1.1-1.8 1.5-.7.4-1.6.5-2.6.5h.1Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4-1.6-1.3-2.9-1.3-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3 1.6 1.3 2.9 1.3ZM130.9 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM145.1 403.7v-13l.8.6H142v-1.6h4.9v13.9h-1.8ZM225.6 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM242.9 404.1c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9-.1 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5h.1Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM251.4 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM263.2 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1H263c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6H263h.2ZM348.1 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM365.4 404.1c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9c.8.6 1.5 1.4 1.9 2.4s.6 2.2.6 3.5v1c0 .9-.1 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4-1.6-1.3-2.9-1.3-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3 1.6 1.3 2.9 1.3ZM373.9 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM389.9 404.1c-1 0-1.9-.2-2.6-.6s-1.3-.9-1.6-1.6c-.4-.7-.6-1.4-.6-2.2h1.8c0 .9.3 1.5.8 2.1.6.5 1.3.8 2.2.8s1.2-.1 1.6-.3c.5-.2.8-.6 1.1-1 .3-.4.4-.9.4-1.5 0-.9-.3-1.6-.8-2.1-.6-.5-1.3-.8-2.2-.8s-.7 0-1 .1c-.3 0-.5.2-.8.4l-.9-1.6 5.3-3.9-.2-.3h-6.7V390h8.5v2.5l-4.4 3.2h-1.3c.2 0 .5 0 .7-.1h.9c1 0 1.9.2 2.6.6s1.3.9 1.6 1.5.6 1.3.6 2.1v.3c0 .7-.2 1.4-.6 2.1s-.9 1.2-1.6 1.6-1.6.6-2.7.6v-.3ZM470.2 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1H470c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6H470h.2ZM487.6 404.1c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9c.8.6 1.5 1.4 1.9 2.4s.6 2.2.6 3.5v1c0 .9-.1 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5s-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3s.9-2.3.9-4.2-.3-3.5-.9-4.4-1.6-1.3-2.9-1.3-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3 1.6 1.3 2.9 1.3ZM496.1 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6 1.2.9 1.5 1.6c.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5s-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM507.3 400.4v-2.2l5.8-8.7 1.3 1-5.4 8.1.2.3h9v1.4h-10.9Zm7.3 3.3v-9.8h1.7v9.8h-1.7ZM592.9 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM610.2 404.1c-1 0-1.9-.2-2.7-.5s-1.3-.9-1.8-1.5-.8-1.3-1-2.2c-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4-1.6-1.3-2.9-1.3-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM618.7 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM635 404.1c-1.1 0-2-.2-2.7-.7-.7-.4-1.3-1-1.7-1.7s-.6-1.5-.6-2.3h1.8c0 .6 0 1.1.4 1.6s.6.8 1.1 1.1c.5.3 1 .4 1.7.4s1.2-.1 1.7-.4.8-.6 1.1-1.1.4-1 .4-1.6 0-1.1-.4-1.6c-.3-.5-.6-.8-1.1-1.1-.5-.3-1-.4-1.7-.4s-.9 0-1.4.2-.9.4-1.2.7h-1.7l.7-7.4h7.3v1.6h-6.2l.5-.6-.5 5.4-.5-.2c.4-.4.8-.7 1.3-.9.5-.2 1.1-.3 1.8-.3 1.1 0 2 .2 2.7.7.7.4 1.3 1 1.6 1.7.4.7.6 1.4.6 2.2v.3c0 .7-.2 1.5-.6 2.2s-.9 1.3-1.7 1.7c-.7.4-1.6.7-2.7.7v-.2ZM715 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM732.3 404.1c-1 0-1.9-.2-2.7-.5s-1.3-.9-1.8-1.5-.8-1.3-1-2.2c-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4-1.6-1.3-2.9-1.3-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM740.8 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM757.8 404.1c-1 0-1.9-.2-2.5-.5-.7-.4-1.2-.8-1.6-1.5-.4-.6-.7-1.3-.9-2.1s-.3-1.6-.3-2.5v-.9c0-2.2.5-3.9 1.6-5.1s2.7-1.8 4.7-1.8h1.3c.4 0 .7 0 1.1.1v1.6c-.4 0-.8 0-1.2-.1h-1.3c-1.2 0-2.1.2-2.7.6-.7.4-1.2 1-1.4 1.9-.3.8-.4 1.8-.4 2.9v.9l-.5 1.7c0-1 .2-1.8.6-2.5.4-.7.9-1.2 1.5-1.5s1.4-.5 2.3-.5 1.8.2 2.5.6 1.2.9 1.5 1.6.5 1.5.5 2.4-.2 1.7-.6 2.4c-.4.7-.9 1.3-1.6 1.7s-1.6.6-2.7.6h.1Zm0-1.6c.7 0 1.3-.1 1.7-.4.5-.3.8-.6 1.1-1.1.2-.5.4-1 .4-1.6s0-1.1-.3-1.6-.6-.8-1-1.1c-.4-.3-1-.4-1.7-.4s-1.2.1-1.7.4c-.5.2-.9.6-1.2 1.1s-.4 1-.4 1.7 0 1.1.3 1.5c.2.5.6.8 1.1 1.1.5.3 1.1.4 1.8.4ZM838.2 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1H838c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6H838h.2ZM855.6 404.1c-1 0-1.9-.2-2.7-.5s-1.3-.9-1.8-1.5-.8-1.3-1-2.2c-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.4.6-1 1.1-1.8 1.5-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4-1.6-1.3-2.9-1.3-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM864.1 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM877.1 403.7l5.9-12.1v-.2h-7.7v-1.6h9.3v2.2l-5.6 11.7h-1.9Zm-1.8-10.1v-3.8h1.5v3.8h-1.5ZM960.1 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM977.4 404.1c-1 0-1.9-.2-2.7-.5-.7-.4-1.3-.9-1.8-1.5-.4-.6-.8-1.3-1-2.2-.2-.8-.3-1.7-.3-2.6v-1c0-1.3.2-2.4.6-3.5.4-1 1-1.8 1.9-2.4.8-.6 1.9-.9 3.2-.9s2.4.3 3.2.9 1.5 1.4 1.9 2.4.6 2.2.6 3.5v1c0 .9 0 1.8-.3 2.6-.2.8-.5 1.5-1 2.2-.5.6-1 1.1-1.8 1.5-.7.4-1.6.5-2.6.5Zm0-1.7c1.3 0 2.3-.4 2.9-1.3.6-.9.9-2.3.9-4.2s-.3-3.5-.9-4.4c-.6-.9-1.6-1.3-2.9-1.3s-2.3.4-2.9 1.3-.9 2.3-.9 4.3.3 3.4.9 4.3c.6.9 1.6 1.3 2.9 1.3ZM985.9 403.7v-2.3c0-.6 0-1 .2-1.4s.4-.8.8-1.1.9-.6 1.6-.9l2.6-1.2c.6-.3 1.1-.7 1.5-1.1.4-.5.6-1.1.6-1.8s-.2-1.6-.7-2.1-1.2-.7-2.1-.7-1.6.2-2.1.7-.8 1.2-.8 2.1h-1.8c0-.8.2-1.5.5-2.2s.8-1.2 1.5-1.6 1.6-.6 2.6-.6 1.9.2 2.6.6c.7.4 1.2.9 1.5 1.6.3.6.5 1.3.5 2.1v.3c0 1-.3 1.8-.9 2.5-.6.7-1.4 1.2-2.3 1.7l-2.6 1.2c-.6.3-1 .5-1.2.8s-.3.6-.3 1.1v1.7l-.5-.9h7.9v1.6h-9.3.2ZM1002.3 404.1c-1 0-1.9-.2-2.6-.5-.8-.4-1.4-.8-1.8-1.5-.4-.6-.7-1.4-.7-2.2s0-1.1.3-1.5.5-.8.9-1.2.8-.6 1.3-.8v-.2c-.4-.2-.7-.4-1-.7-.3-.3-.6-.6-.7-1-.2-.4-.3-.8-.3-1.3 0-.8.2-1.5.6-2 .4-.6 1-1 1.7-1.3s1.5-.4 2.3-.4 1.6.1 2.3.4 1.3.7 1.7 1.3c.4.6.6 1.2.6 2s0 .9-.3 1.3c-.2.4-.4.7-.7 1s-.7.5-1.1.7v.2c.5.2.9.4 1.3.8.4.3.7.7.9 1.2s.3.9.3 1.5-.2 1.6-.7 2.2c-.4.6-1 1.1-1.8 1.5s-1.6.5-2.7.5h.2Zm0-1.6c.7 0 1.3-.1 1.8-.3s.9-.5 1.1-1c.3-.4.4-.9.4-1.5s0-1-.4-1.4-.6-.7-1.1-.9c-.5-.2-1.1-.3-1.8-.3s-1.3.1-1.8.3c-.5.2-.9.5-1.1.9-.3.4-.4.9-.4 1.4s0 1 .4 1.5c.3.4.6.7 1.1 1 .5.2 1.1.3 1.8.3Zm0-6.9c.9 0 1.6-.2 2.1-.6s.8-.9.8-1.7-.3-1.4-.8-1.7-1.2-.5-2.1-.5-1.5.2-2.1.6-.8.9-.8 1.7.3 1.3.8 1.7c.5.4 1.2.6 2.1.6Z"
        className="cls-15"
      />
      <path
        d="M99.1 357.5h930.2M99.1 311.3h930.2M99.1 265.2h930.2M99.1 219.1h930.2M99.1 172.9h930.2M99.1 126.8h930.2M99.1 80.6h930.2M99.1 34.5h930.2"
        className="cls-10"
      />
      <path
        d="m126.4 351.8 122.4-29.5 119.7-19.1"
        style={{
          stroke: '#fd62d5',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path d="m368.5 303.2 2.9-.7" className="cls-8" />
      <path
        d="m384 299.2 109.7-28.5 121-34.7 123.9-53.1L861 120.5l134.6-95.4"
        style={{
          strokeDasharray: '0 0 0 0 0 0 6 13',
          stroke: '#7700b3',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path d="m1000.9 21.4 2.5-1.7" className="cls-8" />
      <path
        d="M126.4 351.8v29.5"
        style={{
          stroke: 'url(#linear-gradient)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M248.8 322.3v59"
        style={{
          stroke: 'url(#linear-gradient-2)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M368.5 303.2v78.1"
        style={{
          stroke: 'url(#linear-gradient-3)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M493.7 270.7v110.6"
        style={{
          stroke: 'url(#linear-gradient-4)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M614.7 236v145.3"
        style={{
          stroke: 'url(#linear-gradient-5)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M738.6 182.9v198.4"
        style={{
          stroke: 'url(#linear-gradient-6)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M861 120.5v260.8"
        style={{
          stroke: 'url(#linear-gradient-7)',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
        }}
      />
      <path
        d="M983.5 33.9v347.4"
        style={{
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          fill: 'none',
          stroke: 'url(#linear-gradient-8)',
        }}
      />
      <circle
        cx={126.4}
        cy={351.8}
        r={4.8}
        className="cls-13"
        transform="rotate(-5.7 125.57 349.587)"
      />
      <circle
        cx={126.4}
        cy={351.8}
        r={2.8}
        className="cls-13"
        transform="rotate(-5.7 125.57 349.587)"
      />
      <circle cx={248.8} cy={322.3} r={4.8} className="cls-13" />
      <circle cx={248.8} cy={322.3} r={2.8} className="cls-13" />
      <circle cx={368.5} cy={303.2} r={4.8} className="cls-13" />
      <circle cx={368.5} cy={303.2} r={2.8} className="cls-13" />
      <circle cx={493.7} cy={270.7} r={4.8} className="cls-13" />
      <circle cx={493.7} cy={270.7} r={2.8} className="cls-13" />
      <circle cx={614.7} cy={236} r={4.8} className="cls-13" />
      <circle cx={614.7} cy={236} r={2.8} className="cls-13" />
      <path
        d="M738.6 187.6c-2.6 0-4.8-2.1-4.8-4.8s2.1-4.8 4.8-4.8 4.8 2.1 4.8 4.8-2.1 4.8-4.8 4.8Z"
        className="cls-13"
      />
      <circle cx={738.6} cy={182.9} r={2.8} className="cls-13" />
      <path
        d="M861 125.2c-2.6 0-4.8-2.1-4.8-4.8s2.1-4.8 4.8-4.8 4.8 2.1 4.8 4.8-2.1 4.8-4.8 4.8Z"
        className="cls-13"
      />
      <circle cx={861} cy={120.5} r={2.8} className="cls-13" />
      <circle cx={983.5} cy={33.9} r={4.8} className="cls-13" />
      <circle cx={983.5} cy={33.9} r={2.8} className="cls-13" />
      <path
        d="m368.5 303.2 125.2-54.5 121-53.5 123.9-64.9L861 50.5l34.8-29.3 87.7-1.5v96L861 187.6l-122.4 51.2-123.9 34.7-121 19.1-125.2 10.6z"
        style={{
          fill: 'url(#linear-gradient-9)',
          strokeWidth: 0,
        }}
      />
    </svg>
  )
}
