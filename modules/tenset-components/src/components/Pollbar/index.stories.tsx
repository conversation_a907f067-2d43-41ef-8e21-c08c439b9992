import { Meta } from '@storybook/react'

import { PollBar, PollBarColor } from '.'

type PollBarType = typeof PollBar

export default {
  title: 'Components/Pollbar',
  component: PollBar,
  args: {
    name: 'Answer',
    index: 'A',
    votes: 492,
    participants: 1492,
  },
} as Meta<PollBarType>

export const Preview = {}

export const EmptyProgress = {
  args: {
    displayVotes: false,
    displayProgress: false,
  },
}

export const EmptyProgressWithIndex = {
  args: {
    index: 'A',
    displayVotes: false,
    displayProgress: false,
  },
}

export const EmptyProgressWithIndexAndHover = {
  args: {
    index: 'A',
    onClick: () => console.log('Story'),
    displayVotes: false,
    displayProgress: false,
  },
}

export const SelectedEmptyProgressWithIndex = {
  args: {
    index: 'A',
    onClick: () => console.log('Story'),
    displayVotes: false,
    displayProgress: false,
    isSelected: true,
  },
}

export const WithProgressAndCounter = {
  args: {
    votes: 678,
  },
}

export const WithProgressCounterAndIndex = {
  args: {
    index: 'A',
    votes: 678,
  },
}

export const SelectedWithProgressCounterAndIndex = {
  args: {
    index: 'A',
    isSelected: true,
    votes: 678,
  },
}

export const WithProgressCounterAndIndexNeutral700 = {
  args: {
    index: 'A',
    votes: 678,
    color: PollBarColor.Neutral700,
  },
}

export const WithProgressCounterAndIndexNeutral600 = {
  args: {
    index: 'A',
    votes: 678,
    color: PollBarColor.Neutral600,
  },
}

export const WithProgressCounterAndIndexNeutral500 = {
  args: {
    index: 'A',
    votes: 678,
    color: PollBarColor.Neutral500,
  },
}
