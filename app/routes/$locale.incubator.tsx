import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'

import IsYourProjectNextIncubatorBlock from '~/components/blocks/is-your-project-next-incubator'
import {
  HeroIncubator,
  LuckyOnesIncubator,
  MattersIncubator,
} from '~/sections/incubator'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import { withCache } from '~/utils'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.INCUBATOR)

  const title = t('meta.title')
  const description = t('meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export const handle = {
  i18n: [Namespace.INCUBATOR, Namespace.COMMON],
}

export default function Incubator() {
  return (
    <div className="container">
      <HeroIncubator />
      <MattersIncubator />
      <LuckyOnesIncubator />
      <IsYourProjectNextIncubatorBlock />
    </div>
  )
}
