import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'

import TokensAvailableOnExchangesBlock from '~/components/blocks/available-on-exchanges'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import {
  BurnsAlreadyBurned,
  BurnsBurnedMonthly,
  BurnsBurnedYearly,
  BurnsData,
  BurnsDeflationVsInflation,
  BurnsHero,
  BurnsInflationWorldwide,
  BurnsLatestBurn,
  BurnsMostTokensInflation,
  BurnsScarcityImpact,
  BurnsSourcesOfBurns,
  BurnsSupplyReduction,
} from '~/sections/burns'
import { withCache } from '~/utils'
import type { UseCountriesInflation } from '~/hooks/simulated-stats'
import { useSimulateStats } from '~/hooks/simulated-stats'
import { countriesInflationData } from '~/data'
import {
  fetchBurnsStats,
  fetchBuybacks,
  fetchMarketdata,
  fetchStrapiLastBuyback,
  fetchStrapiLastMarketplaceBuyback,
  fetchTensetPrice,
} from '~/api/fetchers'
import { BurnsAndMarketData } from '~/components/burns-and-market-data'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.BURNS)

  const title = t('meta.title')
  const description = t('meta.description')

  const [
    _tensetPrice,
    buybacks,
    marketdata,
    burns,
    latestBurn,
    strapiLastMarketplaceBuyback,
  ] = await Promise.all([
    fetchTensetPrice(),
    fetchBuybacks(),
    fetchMarketdata(),
    fetchBurnsStats(),
    fetchStrapiLastBuyback(),
    fetchStrapiLastMarketplaceBuyback(),
  ])

  const { usd: tensetPrice } = _tensetPrice

  const INITIAL_TENSET_PRICE = 0.1

  const priceIncreasedPercentage =
    ((tensetPrice - INITIAL_TENSET_PRICE) / INITIAL_TENSET_PRICE) * 100

  const { baseTime, debt } = countriesInflationData

  const countriesInflationApiData: UseCountriesInflation = {
    scrappedData: debt,
    secondsSinceBaseTime: Date.now() / 1000 - baseTime,
  }

  return withCache(
    json({
      title,
      description,
      priceIncreasedPercentage,
      initialTensetPrice: INITIAL_TENSET_PRICE,
      tensetPrice,
      countriesInflationApiData,
      marketDataApiData: marketdata,
      buybacksApiData: buybacks,
      burnsStatsApiData: burns,
      latestBurnApiData: latestBurn.data[0],
      strapiLastMarketplaceBuybackApiData: strapiLastMarketplaceBuyback,
    })
  )
}

export const handle = {
  i18n: [Namespace.BURNS],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function Burns() {
  const {
    priceIncreasedPercentage,
    initialTensetPrice,
    tensetPrice,
    countriesInflationApiData,
    marketDataApiData,
    buybacksApiData,
    burnsStatsApiData,
    latestBurnApiData,
    strapiLastMarketplaceBuybackApiData,
  } = useLoaderData<typeof loader>()

  const simulatedStats = useSimulateStats({
    marketDataApiData,
    buybacksApiData,
    burnsStatsApiData,
    strapiLastMarketplaceBuybackApiData,
  })

  return (
    <div className="container">
      <BurnsHero />

      <BurnsScarcityImpact />

      <BurnsData
        priceIncreasedPercentage={priceIncreasedPercentage}
        presalePrice={initialTensetPrice}
        tensetPrice={tensetPrice}
      />

      <BurnsSourcesOfBurns />

      <BurnsBurnedMonthly burnedMonthly={marketDataApiData.burnedMonthly} />

      <BurnsBurnedYearly />

      <BurnsAlreadyBurned
        totalBurned={simulatedStats.totalBurned}
        marketplaceBurns={simulatedStats.marketplaceBurns}
        otherBurns={simulatedStats.otherBurns}
        currentCirculation={simulatedStats.currentCirculation}
        tensetPrice={tensetPrice}
      />

      <BurnsDeflationVsInflation />

      <BurnsInflationWorldwide
        countriesInflationApiData={countriesInflationApiData}
      />

      <BurnsMostTokensInflation />

      <BurnsSupplyReduction
        initialTotalSupply={marketDataApiData.initialTotalSupply}
        currentSupply={simulatedStats.currentSupply}
        totalBurned={simulatedStats.totalBurned}
      />

      <BurnsAndMarketData
        marketDataApiData={marketDataApiData}
        simulatedStats={simulatedStats}
      />

      {latestBurnApiData && (
        <BurnsLatestBurn latestBurnData={latestBurnApiData} />
      )}

      <TokensAvailableOnExchangesBlock />
    </div>
  )
}
