import { AssetProps } from './index'

export default function MarketingOptimize({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M106.152 83.6106V28.4549C106.152 26.0757 104.245 24.153 101.886 24.153H88.8139L87.9202 21.9059C87.2171 20.1635 85.4297 18.9499 83.5827 18.9499H78.4707L76.016 13.3502C75.2177 11.5357 73.4303 10.3581 71.4641 10.3581H68.2706L67.2339 7.32994C66.936 6.45274 66.1138 5.86393 65.1844 5.86393H57.3198C56.3904 5.86393 55.5801 6.45274 55.2702 7.32994L54.2335 10.3581H51.0401C49.0739 10.3581 47.2865 11.5357 46.4881 13.3502L44.0334 18.9499H38.9215C37.0745 18.9499 35.2871 20.1635 34.584 21.9059L33.6903 24.153H20.1299C17.7705 24.153 15.864 26.0757 15.864 28.4549V113.88C15.864 116.259 17.7705 118.182 20.1299 118.182H75.5156C79.7577 123.049 85.9778 126.149 92.9249 126.149C105.723 126.149 116.102 115.683 116.102 102.777C116.102 94.834 112.157 87.8284 106.152 83.5986V83.6106Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M41.1037 98.6032H28.7994C28.4665 98.6032 28.205 98.867 28.205 99.2028C28.205 99.5385 28.4665 99.8023 28.7994 99.8023H41.1037C41.4365 99.8023 41.6981 99.5385 41.6981 99.2028C41.6981 98.867 41.4365 98.6032 41.1037 98.6032ZM81.9515 63.1342V40.0518C81.9515 39.7161 81.6899 39.4523 81.3571 39.4523H70.7528C68.0423 39.4523 65.8311 41.6826 65.8311 44.4165V49.6925H55.8212C53.1107 49.6925 50.8995 51.9228 50.8995 54.6567V56.4673H40.8897C38.1792 56.4673 35.968 58.6976 35.968 61.4315V63.1222C35.968 63.458 36.2295 63.7218 36.5624 63.7218H81.3571C81.6899 63.7218 81.9515 63.458 81.9515 63.1222V63.1342ZM50.8876 62.5347H37.1449V61.4435C37.1449 59.3691 38.8211 57.6784 40.8778 57.6784H50.8876V62.5347ZM65.8192 62.5347H52.0765V54.6687C52.0765 52.5943 53.7527 50.9035 55.8093 50.9035H65.8192V62.5347ZM80.7508 62.5347H67.008V44.4165C67.008 42.3421 68.6842 40.6513 70.7409 40.6513H80.7508V62.5347ZM104.266 80.9047V26.1544C104.266 23.4564 102.09 21.2621 99.4152 21.2621H86.7662L86.0172 19.3915C85.2326 17.437 83.2235 16.0581 81.1431 16.0581H76.4235L74.129 10.83C73.2374 8.80358 71.2402 7.48459 69.0409 7.48459H66.2828L65.3912 4.87057C65.0108 3.74343 63.9646 3 62.7877 3H54.9415C53.7646 3 52.7184 3.75542 52.338 4.87057L51.4464 7.48459H48.6883C46.489 7.48459 44.4918 8.79159 43.6002 10.83L41.3058 16.0581H36.5862C34.4938 16.0581 32.4966 17.425 31.712 19.3915L30.963 21.2741C30.963 21.2741 30.9155 21.2621 30.8917 21.2621H17.8504C15.1755 21.2621 13 23.4564 13 26.1544V111.397C13 114.095 15.1755 116.29 17.8504 116.29H72.8689C77.2081 121.158 83.485 124.24 90.4753 124.24C103.552 124.24 114.192 113.508 114.192 100.318C114.192 92.32 110.269 85.2454 104.277 80.9047H104.266ZM44.682 11.3217C45.3834 9.72688 46.9527 8.69567 48.6883 8.69567H51.8744C52.124 8.69567 52.3499 8.5278 52.4331 8.28798L53.4674 5.26627C53.6814 4.63076 54.2758 4.19909 54.9415 4.19909H62.7877C63.4535 4.19909 64.0479 4.63076 64.2618 5.26627L65.2961 8.28798C65.3793 8.5278 65.6052 8.69567 65.8549 8.69567H69.0409C70.7766 8.69567 72.3458 9.72688 73.0472 11.3217L75.1276 16.07H42.6135L44.6939 11.3217H44.682ZM32.8057 19.8352C33.412 18.3243 34.9694 17.2571 36.5862 17.2571H81.155C82.7718 17.2571 84.3291 18.3123 84.9354 19.8352L85.8389 22.0895L87.7767 26.9338H29.9882L32.8295 19.8352H32.8057ZM17.8504 115.091C15.8294 115.091 14.1888 113.436 14.1888 111.397V26.1544C14.1888 24.1159 15.8294 22.4612 17.8504 22.4612H30.4994L28.5616 27.3175C28.4903 27.4974 28.5141 27.7132 28.6211 27.8811C28.7281 28.0489 28.9183 28.1449 29.1085 28.1449H88.6564C88.8585 28.1449 89.0368 28.0489 89.1438 27.8811C89.2508 27.7132 89.2746 27.5093 89.2033 27.3175L87.2655 22.4612H99.4271C101.448 22.4612 103.089 24.1159 103.089 26.1544V80.1013C99.4271 77.7751 95.1117 76.3961 90.4753 76.3961C77.3983 76.3961 66.7584 87.128 66.7584 100.318C66.7584 105.894 68.6724 111.026 71.8584 115.091H17.8504ZM90.4753 123.041C78.0521 123.041 67.9472 112.848 67.9472 100.318C67.9472 87.7875 78.0521 77.5952 90.4753 77.5952C102.898 77.5952 113.003 87.7875 113.003 100.318C113.003 112.848 102.898 123.041 90.4753 123.041ZM51.6604 92.5598H28.7994C28.4665 92.5598 28.205 92.8236 28.205 93.1594C28.205 93.4951 28.4665 93.7589 28.7994 93.7589H51.6604C51.9932 93.7589 52.2548 93.4951 52.2548 93.1594C52.2548 92.8236 51.9932 92.5598 51.6604 92.5598ZM100.901 92.7397L87.1228 106.637L80.0494 99.5026C79.8116 99.2627 79.4431 99.2627 79.2053 99.5026C78.9675 99.7424 78.9675 100.114 79.2053 100.354L86.6948 107.908C86.8018 108.016 86.9564 108.088 87.1109 108.088C87.2655 108.088 87.42 108.028 87.527 107.908L101.722 93.5911C101.959 93.3512 101.959 92.9795 101.722 92.7397C101.484 92.4999 101.115 92.4999 100.877 92.7397H100.901ZM51.6604 86.5164H28.7994C28.4665 86.5164 28.205 86.7802 28.205 87.116C28.205 87.4517 28.4665 87.7155 28.7994 87.7155H51.6604C51.9932 87.7155 52.2548 87.4517 52.2548 87.116C52.2548 86.7802 51.9932 86.5164 51.6604 86.5164Z"
        fill={color ?? 'url(#paint0_linear_941_1258)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_941_1258"
          x1="30.1428"
          y1="124.204"
          x2="92.7864"
          y2="16.6326"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
