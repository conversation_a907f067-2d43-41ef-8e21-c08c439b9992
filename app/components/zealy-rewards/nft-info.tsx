import { Text, TextS } from '~/tenset-components'

export function NftInfoComponent({
  shortName,
  longName,
  img,
}: {
  shortName: string
  longName: string
  img: string
}) {
  return (
    <div className="flex">
      <img src={img} alt="" className="w-[100px]" />
      <div className="flex flex-col items-start justify-center">
        <Text isBold>{shortName}</Text>
        <TextS className="text-neutral-300">{longName}</TextS>
      </div>
    </div>
  )
}
