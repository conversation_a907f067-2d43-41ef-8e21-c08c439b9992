import type { LoaderFunctionArgs } from '@remix-run/node'
import { json, redirect } from '@remix-run/node'
import {
  useFetcher,
  useLoaderData,
  useOutletContext,
  useSearchParams,
} from '@remix-run/react'
import clsx from 'clsx'
import { BigNumber, ethers } from 'ethers'
import { formatEther, parseEther } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import { Fragment, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import type { GlpLock } from '~/api/types'
import { PurchaseTokensModal } from '~/components/gem-launch-platform'
import { SwitchNetworkButton } from '~/components/switch-network/button'
import { BSC_NETWORK } from '~/config'
import { isDevelopment } from '~/environment'
import { getTierAmount, getTierIndex } from '~/data/gem-launch-platform'
import type { GlpUpgradeOffer } from '~/data/gem-launch-platform/offers'
import { glpUpgradeOffers } from '~/data/gem-launch-platform/offers'
import { Namespace } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import type { GlpOutletData } from '~/routes/$locale.gem-launch-platform.panel'
import { getSession } from '~/sessions.server'
import {
  Button,
  DateFormatter,
  H2,
  H3,
  Icon,
  IconName,
  NumberFormatter,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Text,
  TextL,
  TextS,
  TextXs,
} from '~/tenset-components'
import {
  GlpMembershipContract,
  GlpUpgradeContract,
  TensetContract,
  chains,
  config,
  parseEthersError,
  useGlpContracts,
  useTensetContract,
  useWallet,
} from '~/tenset-web3'
import { cutDecimals, getDaysFromNow, getRpcLocks } from '~/utils'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  const locale = detectLocale(request)

  const baseUrl = `/${locale}/gem-launch-platform`
  const panelUrl = `${baseUrl}/panel`

  if (!walletAddress) return redirect(baseUrl)

  const { rpcUrls } = chains[BSC_NETWORK]

  const glpMembershipContract = new GlpMembershipContract(
    config.gemLaunchPlatform.membership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const tensetContract = new TensetContract(BSC_NETWORK, rpcUrls[0])

  const glpUpgradeContract = new GlpUpgradeContract(
    config.gemLaunchPlatform.upgrade[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const membershipBalance = await glpMembershipContract.balanceOf(walletAddress)

  if (membershipBalance.isZero()) return redirect(baseUrl)

  const jsonRpcProvider = new ethers.providers.JsonRpcProvider({
    url: rpcUrls[0],
    skipFetchSetup: true,
  })

  const [locksResponse, { timestamp: blockTimestamp }] = await Promise.all([
    getRpcLocks({
      walletAddress,
      glpMembershipContract,
      membershipBalance,
    }),
    jsonRpcProvider.getBlock('latest'),
  ])

  const locks = locksResponse.filter(
    ({ timestamps, membershipAttributes }) =>
      timestamps.unlock > blockTimestamp * 1000 &&
      membershipAttributes!.tierId < (isDevelopment ? 4 : 3)
  )

  if (locks.length === 0) return redirect(panelUrl)

  const upgradeAllowance = await tensetContract.allowance(
    walletAddress,
    glpUpgradeContract
  )

  return json({
    locks,
    upgradeAllowance,
  })
}

enum UpgradeStep {
  Tier,
  Allowance,
  Confirm,
}

type UpgradeStepData = {
  [key in UpgradeStep]: {
    title: string
    description: string
    icon: IconName
    action: JSX.Element
    note: string[]
  }
}

export default function GlpUpgrade() {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
    Namespace.GEM_LAUNCH_PLATFORM,
  ])

  const { isRevalidating, tensetBalance } = useOutletContext<GlpOutletData>()

  const { wallet } = useWallet()

  const { glpMembershipContract, glpUpgradeContract } =
    useGlpContracts(BSC_NETWORK)

  const { blockTimestamp } = useOutletContext<GlpOutletData>()

  const { locks, upgradeAllowance: upgradeAllowanceApi } =
    useLoaderData<typeof loader>()

  const [upgradeAllowance, setUpgradeAllowance] = useState(
    BigNumber.from(upgradeAllowanceApi)
  )

  useEffect(() => {
    setUpgradeAllowance(BigNumber.from(upgradeAllowanceApi))
  }, [upgradeAllowanceApi])

  const fetcher = useFetcher({ key: 'root' })

  const [hasUpgraded, setHasUpgraded] = useState(false)
  const [upgradedTierId, setUpgradedTierId] = useState<number>()

  const onUpgrade = async () => {
    setUpgradedTierId(selectedTierId)
    setHasUpgraded(true)
  }

  const [searchParameters, setSearchParameters] = useSearchParams()

  const [currentStep, setCurrentStep] = useState<UpgradeStep>(UpgradeStep.Tier)

  const [errorMessage, setErrorMessage] = useState<string | undefined>()

  const locksId = locks.map(({ id }) => id)

  const [upgradeOffers, setUpgradeOffers] =
    useState<GlpUpgradeOffer[]>(glpUpgradeOffers)

  const parameterId = searchParameters.get('id')
    ? Number.parseInt(searchParameters.get('id')!)
    : null

  const [selectedLockId, setSelectedLockId] = useState<number>(
    parameterId && locksId.includes(parameterId) ? parameterId : locksId[0]
  )

  const [selectedTierId, setSelectedTierId] = useState<number>(
    upgradeOffers[0].id
  )

  const [upgradeCost, setUpgradeCost] = useState<BigNumber>(BigNumber.from('0'))

  const [selectedLockUnlockTimestamp, setSelectedLockUnlockTimestamp] =
    useState<number>(0)

  const [selectedLockDaysToUnlock, setSelectedLockDaysToUnlock] =
    useState<number>(0)

  useEffect(() => {
    setSelectedLockDaysToUnlock(
      getDaysFromNow(new Date(selectedLockUnlockTimestamp))
    )
  }, [selectedLockUnlockTimestamp])

  const requiredAllowance = upgradeCost.sub(upgradeAllowance)

  const isRegularNftSelected = selectedTierId === 4

  // TODO: TMP wallet & chain change event via root fetcher
  // wallet has been changed, so we are going back to first step
  useEffect(() => {
    if (
      fetcher.state === 'submitting' &&
      fetcher.formAction.includes('/api/wallet')
    ) {
      setCurrentStep(UpgradeStep.Tier)
      setErrorMessage(undefined)
    }
  }, [fetcher.state])

  const [isMembershipUpgradeApproved, setIsMembershipUpgradeApproved] =
    useState<boolean>(false)

  useEffect(() => {
    if (!wallet || !glpMembershipContract) return

    if (!isRegularNftSelected) {
      setIsMembershipUpgradeApproved(true)

      return
    }

    async function getMembershipUpgradeApproval(): Promise<boolean> {
      if (!wallet || !glpMembershipContract || !glpUpgradeContract) return false

      return await wallet
        .interact(glpMembershipContract)
        .isApprovedForAll(wallet.account!, glpUpgradeContract.address)
    }

    getMembershipUpgradeApproval().then(isApproved => {
      setIsMembershipUpgradeApproved(isApproved)
    })
  }, [wallet, glpMembershipContract, selectedLockId, isRegularNftSelected])

  const onTierSelect = () =>
    setCurrentStep(
      requiredAllowance.gt(0) || !isMembershipUpgradeApproved
        ? UpgradeStep.Allowance
        : UpgradeStep.Confirm
    )

  const onAllowance = () => setCurrentStep(UpgradeStep.Confirm)

  const steps: UpgradeStepData = {
    [UpgradeStep.Tier]: {
      title: t('tier'),
      description: t('upgrade.tier.description'),
      icon: IconName.Tiers,
      action: (
        <UpgradeTier
          t={t}
          onTierSelect={onTierSelect}
          locks={locks}
          selectedLockId={selectedLockId}
          setSelectedLockId={setSelectedLockId}
          selectedTierId={selectedTierId}
          setSelectedTierId={setSelectedTierId}
          upgradeOffers={upgradeOffers}
          tensetBalance={tensetBalance}
          upgradeCost={upgradeCost}
          setUpgradeCost={setUpgradeCost}
          selectedLockUnlockTimestamp={selectedLockUnlockTimestamp}
          setSelectedLockUnlockTimestamp={setSelectedLockUnlockTimestamp}
          selectedLockDaysToUnlock={selectedLockDaysToUnlock}
          blockTimestamp={blockTimestamp}
          isRevalidating={isRevalidating}
        />
      ),
      note: [t('upgrade.tier.note')],
    },
    [UpgradeStep.Allowance]: {
      title: t('allowance'),
      description: t('upgrade.allowance.description'),
      icon: IconName.Security,
      action: (
        <UpgradeAllowance
          t={t}
          onAllowance={onAllowance}
          setErrorMessage={setErrorMessage}
          setRequiredAllowance={setUpgradeCost}
          requiredAllowance={requiredAllowance}
          isMembershipUpgradeApproved={isMembershipUpgradeApproved}
          selectedLockId={selectedLockId}
        />
      ),
      note: [t('upgrade.allowance.note')],
    },
    [UpgradeStep.Confirm]: {
      title: t('confirm'),
      description: t(
        isRegularNftSelected
          ? 'upgrade.confirm.description-nft'
          : 'upgrade.confirm.description-tokens'
      ),
      icon: IconName.Lock,
      action: (
        <UpgradeConfirm
          t={t}
          buttonLabel={
            isRegularNftSelected
              ? t('upgrade.confirm.button-label-nft')
              : t('upgrade.confirm.button-label-tokens', {
                  days:
                    selectedLockDaysToUnlock > 365
                      ? selectedLockDaysToUnlock
                      : 365,
                })
          }
          upgradeCost={upgradeCost}
          tensetBalance={tensetBalance}
          selectedLockUnlockTimestamp={selectedLockUnlockTimestamp}
          selectedTierId={selectedTierId}
          setErrorMessage={setErrorMessage}
          selectedLockId={selectedLockId}
          selectedLockDaysToUnlock={selectedLockDaysToUnlock}
          onUpgrade={onUpgrade}
          blockTimestamp={blockTimestamp}
        />
      ),
      note: isRegularNftSelected
        ? [t('upgrade.confirm.note-nft-1'), t('upgrade.confirm.note-nft-2')]
        : [t('upgrade.confirm.note-tokens')],
    },
  }

  const localizePathname = useLocalizePathname()

  const DASHBOARD_URL = localizePathname('/gem-launch-platform/panel')

  useEffect(() => {
    if (locksId.length === 0) return

    setSelectedLockId(
      parameterId && locksId.includes(parameterId) ? parameterId : locksId[0]
    )
  }, [JSON.stringify(locksId)])

  useEffect(() => {
    const selectedLock = locks.find(({ id }) => id === selectedLockId)
    const { tierId } = selectedLock!.membershipAttributes!

    setUpgradeOffers(glpUpgradeOffers.filter(({ id }) => id > tierId))

    setSearchParameters(
      previous => {
        previous.set('id', selectedLockId.toString())

        return previous
      },
      {
        preventScrollReset: true,
        replace: true,
      }
    )
  }, [selectedLockId])

  useEffect(() => {
    setSelectedTierId(upgradeOffers[0].id)
  }, [upgradeOffers])

  if (hasUpgraded)
    return (
      <div className="flex flex-col gap-4 md:items-center md:justify-center">
        <div className="flex flex-col-reverse items-center justify-center gap-6 text-center">
          <div className="flex flex-col gap-4">
            <H3 isBold>{t('you-upgraded-successfully.title')}</H3>

            <Text>
              {upgradedTierId === 4
                ? t('you-upgraded-successfully.description-lifetime')
                : t('you-upgraded-successfully.description', {
                    days: selectedLockDaysToUnlock,
                  })}
            </Text>
          </div>

          <Icon name={IconName.Tiers} />
        </div>

        <Button to={DASHBOARD_URL}>
          {t('you-upgraded-successfully.button')}
        </Button>
      </div>
    )

  return (
    <div className="flex flex-col items-center justify-center gap-4 md:max-w-[720px] md:pt-32">
      <div className="flex flex-col gap-6 text-center">
        <H2 isBold>{t('upgrade.title')}</H2>

        <div className="flex flex-row items-center justify-center gap-2 md:gap-4 align-middle">
          {Object.values(steps).map(({ title }, index) => {
            const isCurrentStep = currentStep === index
            const isStepDone = currentStep > index
            const isFutureStep = currentStep < index

            return (
              <Fragment key={title}>
                <div
                  className={clsx(
                    'flex h-12 w-12 items-center justify-center rounded-full',
                    isCurrentStep && 'bg-neutral-700 text-white',
                    isStepDone && 'bg-green-900 text-green-600',
                    isFutureStep && 'border border-neutral-400 text-neutral-100'
                  )}
                >
                  {isStepDone ? (
                    <Icon name={IconName.Success} />
                  ) : (
                    <TextS isBold>{index + 1}</TextS>
                  )}
                </div>

                <TextS
                  className={clsx(
                    isStepDone && 'text-green-600',
                    isCurrentStep && 'text-white',
                    isFutureStep && 'text-neutral-100'
                  )}
                  isBold
                >
                  {title}
                </TextS>
              </Fragment>
            )
          })}
        </div>

        <div className="flex items-center justify-center">
          <Icon name={steps[currentStep].icon} />
        </div>

        <TextL>{steps[currentStep].description}</TextL>

        <div className="relative mx-auto w-full flex flex-col gap-4 items-stretch justify-center md:w-[492px]">
          {steps[currentStep].action}

          {errorMessage && (
            <TextS className="text-red-500">{errorMessage}</TextS>
          )}

          {steps[currentStep].note.length > 0 && (
            <div>
              {steps[currentStep].note.map((note, index) => (
                <TextS key={index} className="text-neutral-100">
                  {note}
                </TextS>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface UpgradeTierProps {
  t: TFunction
  onTierSelect: () => void
  locks: GlpLock[]
  selectedLockId: number
  setSelectedLockId: (index: number) => void
  selectedTierId: number
  setSelectedTierId: (index: number) => void
  upgradeOffers: GlpUpgradeOffer[]
  tensetBalance: BigNumber
  upgradeCost: BigNumber
  setUpgradeCost: (cost: BigNumber) => void
  selectedLockUnlockTimestamp: number
  setSelectedLockUnlockTimestamp: (timestamp: number) => void
  selectedLockDaysToUnlock: number
  blockTimestamp: number
  isRevalidating: boolean
}

function UpgradeTier({
  t,
  onTierSelect,
  locks,
  selectedLockId,
  setSelectedLockId,
  selectedTierId,
  setSelectedTierId,
  upgradeOffers,
  tensetBalance,
  upgradeCost,
  setUpgradeCost,
  selectedLockUnlockTimestamp,
  setSelectedLockUnlockTimestamp,
  selectedLockDaysToUnlock,
  blockTimestamp,
  isRevalidating,
}: UpgradeTierProps) {
  const { wallet } = useWallet()

  const [isReferralAdded, setIsReferralAdded] = useState<boolean>(false)

  useEffect(() => {
    if (!selectedLockId) return
    const selectedLock = locks.find(({ id }) => id === selectedLockId)
    const { timestamps, membershipAttributes } = selectedLock!
    const { unlock } = timestamps
    const { bonusNumerator, bonusDenominator } = membershipAttributes!

    setSelectedLockUnlockTimestamp(unlock)

    setIsReferralAdded(
      Boolean(bonusNumerator && bonusDenominator && bonusNumerator > 0)
    )
  }, [selectedLockId])

  useEffect(() => {
    if (!selectedLockId || !selectedTierId) return

    const selectedLock = locks.find(({ id }) => id === selectedLockId)
    const { amount } = selectedLock!

    const tierAmount = getTierAmount(selectedTierId)

    setUpgradeCost(parseEther(tierAmount.toString()).sub(parseEther(amount)))
  }, [selectedLockId, selectedTierId])

  const isOnCorrectNetwork = wallet?.chain === BSC_NETWORK
  const insufficientBalance = tensetBalance.lt(upgradeCost)

  return (
    <div className="flex flex-col gap-6">
      <div className="w-full flex flex-col gap-2">
        <TextS isBold className="text-start">
          {t('upgrade.tier.select-lock')}
        </TextS>

        <Select
          disabled={locks.length < 2}
          value={selectedLockId.toString()}
          onValueChange={value => setSelectedLockId(Number.parseInt(value))}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('upgrade.tier.select-lock')} />
          </SelectTrigger>

          <SelectContent>
            <SelectGroup>
              {locks.map(({ id, amount, membershipAttributes, timestamps }) => {
                const { tierId } = membershipAttributes!
                const { unlock } = timestamps

                return (
                  <SelectItem value={id.toString()} key={id}>
                    <span>
                      <TextS isBold tag="span">
                        {t('tier')} {tierId}
                      </TextS>{' '}
                      (<NumberFormatter value={Number.parseFloat(amount)} />)
                    </span>{' '}
                    <TextXs tag="span" className="text-neutral-100">
                      {t('release-date')}:{' '}
                      <DateFormatter date={new Date(unlock)} />
                    </TextXs>
                  </SelectItem>
                )
              })}
            </SelectGroup>
          </SelectContent>
        </Select>

        {isReferralAdded && (
          <TextS className="text-blue-100 text-left">
            {t('upgrade.tier.affiliate-deactivated')}
          </TextS>
        )}
      </div>

      <div className="w-full flex flex-col gap-2">
        <TextS isBold className="text-start">
          {t('upgrade.upgrade-to')}
        </TextS>

        <Select
          value={selectedTierId.toString()}
          onValueChange={value => setSelectedTierId(Number.parseInt(value))}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('upgrade.upgrade-to')} />
          </SelectTrigger>

          <SelectContent>
            <SelectGroup>
              {upgradeOffers.map(tier => {
                const { price, id, isRegularNft } = tier

                const priceFormatted = Number.parseInt(formatEther(price))

                const unlockTimestampAfterUpgrade = isRegularNft ? (
                  t('lifetime-access')
                ) : (
                  <>
                    {t('release-date')}:{' '}
                    <DateFormatter
                      date={
                        selectedLockDaysToUnlock > 365
                          ? new Date(selectedLockUnlockTimestamp)
                          : new Date(blockTimestamp + 365 * 24 * 60 * 60 * 1000)
                      }
                    />
                  </>
                )

                return (
                  <SelectItem key={id} value={id.toString()}>
                    <TextS isBold tag="span">
                      {t('tier')} {getTierIndex(priceFormatted, isRegularNft)}
                    </TextS>{' '}
                    (
                    {isRegularNft ? (
                      <span>{t('regular-nft')}</span>
                    ) : (
                      <NumberFormatter value={priceFormatted} />
                    )}
                    ){' '}
                    <TextXs tag="span" className="text-neutral-100">
                      {unlockTimestampAfterUpgrade}
                    </TextXs>
                  </SelectItem>
                )
              })}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      {!upgradeCost.isZero() && (
        <div className="flex flex-col gap-1">
          <div className="flex justify-between gap-2">
            <TextS isBold>
              {t(
                upgradeCost.gte(0)
                  ? 'upgrade.tokens-needed'
                  : 'upgrade.get-return'
              )}
            </TextS>

            {isOnCorrectNetwork && (
              <TextS isBold className="flex items-center justify-center gap-1">
                {t('balance')}:{' '}
                {isRevalidating ? (
                  <span className="h-[16px] w-[75px] animate-pulse rounded-md bg-neutral-600"></span>
                ) : (
                  cutDecimals(formatEther(tensetBalance))
                )}
              </TextS>
            )}
          </div>

          <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
            <div className="p-3 text-neutral-100">
              <NumberFormatter
                value={Math.abs(Number.parseFloat(formatEther(upgradeCost)))}
              />
            </div>

            <Text className="border-l-2 border-neutral-500 p-3 px-8" isBold>
              10set
            </Text>
          </div>
        </div>
      )}

      <UpgradeAction
        t={t}
        onTierSelect={onTierSelect}
        isOnCorrectNetwork={isOnCorrectNetwork}
        insufficientBalance={insufficientBalance}
        isRevalidating={isRevalidating}
      />
    </div>
  )
}

interface UpgradeActionProps {
  t: TFunction
  onTierSelect: () => void
  isOnCorrectNetwork: boolean
  insufficientBalance: boolean
  isRevalidating: boolean
}

function UpgradeAction({
  t,
  onTierSelect,
  isOnCorrectNetwork,
  insufficientBalance,
  isRevalidating,
}: UpgradeActionProps) {
  const { name } = chains[BSC_NETWORK]

  if (!isOnCorrectNetwork)
    return <SwitchNetworkButton id={BSC_NETWORK} name={name} />

  if (isRevalidating)
    return <Button disabled>{t('subscribe.tier.button')}</Button>

  if (insufficientBalance)
    return (
      <>
        <TextS className="text-red-500">{t('insufficient-balance')}</TextS>

        <PurchaseTokensModal />
      </>
    )

  return <Button onClick={onTierSelect}>{t('subscribe.tier.button')}</Button>
}

interface UpgradeAllowanceProps {
  t: TFunction
  onAllowance: () => void
  setErrorMessage: (message?: string) => void
  setRequiredAllowance: (amount: BigNumber) => void
  requiredAllowance: BigNumber
  isMembershipUpgradeApproved: boolean
  selectedLockId: number
}

function UpgradeAllowance({
  t,
  onAllowance,
  setErrorMessage,
  setRequiredAllowance,
  requiredAllowance,
  isMembershipUpgradeApproved,
  selectedLockId,
}: UpgradeAllowanceProps) {
  const { wallet } = useWallet()

  const { tensetContract } = useTensetContract(BSC_NETWORK)

  const { glpUpgradeContract, glpMembershipContract } =
    useGlpContracts(BSC_NETWORK)

  async function increaseAllowance(): Promise<boolean> {
    if (!requiredAllowance.gt(0)) return true

    if (
      !wallet ||
      wallet?.isProcessing ||
      !tensetContract ||
      !glpUpgradeContract
    )
      return false

    setErrorMessage(undefined)

    return await wallet
      .interact(tensetContract)
      .increaseAllowance(glpUpgradeContract, requiredAllowance)
      .then(() => {
        setRequiredAllowance(BigNumber.from(0))

        return true
      })
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)

        return false
      })
  }

  async function approveMembershipForUpgrade(): Promise<boolean> {
    if (isMembershipUpgradeApproved) return true

    if (
      !wallet ||
      wallet?.isProcessing ||
      !tensetContract ||
      !glpUpgradeContract ||
      !glpMembershipContract
    )
      return false

    setErrorMessage(undefined)

    return await wallet
      .interact(glpMembershipContract)
      .setApprovalForAll(glpUpgradeContract.address, true)
      .then(() => true)
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)

        return false
      })
  }

  async function handleAllowance() {
    const hasIncreasedAllowance = await increaseAllowance()
    if (!hasIncreasedAllowance) return

    const hasApprovedMembershipForUpgrade = await approveMembershipForUpgrade()
    if (!hasApprovedMembershipForUpgrade) return

    onAllowance?.()
  }

  return (
    <Button
      loading={wallet?.isProcessing}
      disabled={wallet?.isProcessing}
      onClick={handleAllowance}
    >
      {t('upgrade.allowance.button')}
    </Button>
  )
}

interface UpgradeConfirmProps {
  t: TFunction
  buttonLabel: string
  upgradeCost: BigNumber
  tensetBalance: BigNumber
  selectedTierId: number
  selectedLockUnlockTimestamp: number
  setErrorMessage: (message?: string) => void
  onUpgrade?: () => void
  selectedLockId: number
  selectedLockDaysToUnlock: number
  blockTimestamp: number
}

function UpgradeConfirm({
  t,
  buttonLabel,
  upgradeCost,
  tensetBalance,
  selectedTierId,
  selectedLockUnlockTimestamp,
  setErrorMessage,
  onUpgrade,
  selectedLockId,
  selectedLockDaysToUnlock,
  blockTimestamp,
}: UpgradeConfirmProps) {
  const { wallet } = useWallet()

  const { glpUpgradeContract } = useGlpContracts(BSC_NETWORK)

  const isRegularNft = selectedTierId === 4

  const unlockTimestampAfterUpgrade = isRegularNft ? (
    t('lifetime-access')
  ) : (
    <>
      {t('release-date')}:{' '}
      <DateFormatter
        date={
          selectedLockDaysToUnlock > 365
            ? new Date(selectedLockUnlockTimestamp)
            : new Date(blockTimestamp + 365 * 24 * 60 * 60 * 1000)
        }
      />
    </>
  )

  async function upgrade() {
    if (!wallet || wallet?.isProcessing || !glpUpgradeContract) return

    setErrorMessage(undefined)

    await wallet
      .interact(glpUpgradeContract)
      .upgrade(BigNumber.from(selectedLockId), BigNumber.from(selectedTierId))
      .then(onUpgrade)
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)
      })
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-1">
        <TextS isBold className="text-start">
          {t('upgrade.upgrade-to')}
        </TextS>

        <div className="p-3 text-neutral-100 text-start rounded border-2 border-neutral-500 bg-neutral-700">
          <TextS isBold tag="span">
            {t('tier')} {selectedTierId}
          </TextS>{' '}
          (
          {isRegularNft ? (
            <span>{t('regular-nft')}</span>
          ) : (
            <NumberFormatter value={getTierAmount(selectedTierId)} />
          )}
          ){' '}
          <TextXs tag="span" className="text-neutral-100">
            {unlockTimestampAfterUpgrade}
          </TextXs>
        </div>
      </div>

      {!upgradeCost.isZero() && (
        <div className="flex flex-col gap-1">
          <div className="flex justify-between gap-2">
            <TextS isBold>
              {upgradeCost.gte(0)
                ? t('upgrade.tokens-needed')
                : t('upgrade.get-return')}
            </TextS>

            <TextS isBold>
              {t('balance')}: {cutDecimals(formatEther(tensetBalance))}
            </TextS>
          </div>

          <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
            <div className="p-3 text-neutral-100">
              <NumberFormatter
                value={Math.abs(Number.parseFloat(formatEther(upgradeCost)))}
              />
            </div>

            <Text className="border-l-2 border-neutral-500 p-3 px-8" isBold>
              10set
            </Text>
          </div>
        </div>
      )}

      <Button
        onClick={upgrade}
        loading={wallet?.isProcessing}
        disabled={wallet?.isProcessing}
      >
        {buttonLabel}
      </Button>
    </div>
  )
}
