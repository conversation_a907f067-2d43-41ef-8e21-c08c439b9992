import { Meta } from '@storybook/react'

import { incubatorCryptoMayhem } from '../../assets/videos'
import { TextNumeric, TextS } from '../Typo/Text'
import { Tag, TagColor } from '../Tag'
import { fameCover } from '../../assets/images/'
import { InfoIcon } from '../../assets/icons'
import { Button, ButtonVariant } from '../Button'

import { Tile } from '.'

type TileType = typeof Tile

const ActionButton = (
  <Button variant={ButtonVariant.Secondary}>
    Action
    <InfoIcon />
  </Button>
)

export default {
  title: 'Components/Tile',
  component: Tile,
  args: {
    title: 'Title',
    subtitle: 'Data',
    description: 'Description',
    tag: <Tag color={TagColor.NEUTRAL}>Tag</Tag>,
    cover: fameCover,
    actions: ActionButton,
  },
} as Meta<TileType>

export const Preview = {}

export const WithMultipleActions = {
  args: {
    actions: <>{Array.from({ length: 3 }, () => ActionButton)}</>,
  },
}

export const WithChildren = {
  args: {
    children: (
      <>
        <TextS isBold>Total Raised</TextS>
        <TextNumeric>1000</TextNumeric>
      </>
    ),
  },
}

export const WithVideoCover = {
  args: {
    cover: incubatorCryptoMayhem,
    isVideo: true,
  },
}
