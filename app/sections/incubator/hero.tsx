import { useTranslation } from 'react-i18next'

import { incubatorIllustration } from '~/assets/images'
import { Namespace } from '~/i18n'
import { Hero, HeroImage } from '~/tenset-components'
import { LaunchYourProjectButton } from '~/components/utils'

export function HeroIncubator() {
  const { t } = useTranslation([Namespace.INCUBATOR])

  return (
    <Hero
      title={t('title')}
      description={t('description')}
      rightContent={<HeroImage src={incubatorIllustration} alt={t('title')} />}
      leftContent={<LaunchYourProjectButton />}
    />
  )
}
