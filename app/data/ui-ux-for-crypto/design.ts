import type { TFunction } from 'i18next'

import { IconName } from '~/tenset-components'
import type { CardWithTile } from '~/components/cards-with-title'

export const design = (t: TFunction): CardWithTile[] => [
  {
    title: t('ui-ux.design.problem.title'),
    description: t('ui-ux.design.problem.description'),
    image: IconName.MarketingUP,
  },
  {
    title: t('ui-ux.design.solutions.title'),
    description: t('ui-ux.design.solutions.description'),
    image: IconName.MarketingDS,
  },
  {
    title: t('ui-ux.design.measure.title'),
    description: t('ui-ux.design.measure.description'),
    image: IconName.MarketingOptimize,
  },
]
