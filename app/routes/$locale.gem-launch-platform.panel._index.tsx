import type { ActionFunction, LoaderFunctionArgs } from '@remix-run/node'
import { defer, json, redirect } from '@remix-run/node'
import { useLoaderData, useOutletContext } from '@remix-run/react'
import { BigNumber } from 'ethers'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import {
  fetchGlpSubscriptionInfoForMany,
  getGLPCurrentLaunch,
} from '~/api/fetchers'
import type {
  GlpAssignWalletData,
  GlpLock,
  GlpReferralLockData,
  GlpSubscriptionInfo,
} from '~/api/types'
import {
  AssignWallet,
  TGLPStatus,
  TglpNewsletter,
  TglpNftsTable,
  TglpTokensTable,
  signatureValidation,
} from '~/components/gem-launch-platform'
import { LoadingIndicator } from '~/components/utils'
import { BSC_NETWORK } from '~/config'
import { referralApprovedAddresses } from '~/data/gem-launch-platform'
import {
  addReferralLock,
  deleteAssignWallet,
  getAssignedWallet,
  getReferralLocks,
  insertAssignWallet,
} from '~/database'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import type { GlpOutletData } from '~/routes/$locale.gem-launch-platform.panel'
import { getSession } from '~/sessions.server'
import { H2 } from '~/tenset-components'
import {
  GlpExtendContract,
  GlpLifetimeMembershipContract,
  GlpMembershipContract,
  TensetContract,
  chains,
  config,
} from '~/tenset-web3'
import {
  formDataToObject,
  getRpcLifetimeNfts,
  getRpcLocks,
  isFormOk,
  schema,
  translateValidations,
  validate,
  validateExistanceAndNoEmptiness,
  validateIfDate,
  validateIfEthAddress,
} from '~/utils'

const verificationAssignWalletFormSchema = schema({
  subscriptionAddress: [validateExistanceAndNoEmptiness],
  assignedAddress: [validateIfEthAddress],
  message: [validateExistanceAndNoEmptiness],
  signature: [validateExistanceAndNoEmptiness],
  date: [validateIfDate],
  launchId: [validateExistanceAndNoEmptiness],
})

const verificationSetReferralLockFormSchema = schema({
  subscriptionAddress: [validateExistanceAndNoEmptiness],
  lockId: [validateExistanceAndNoEmptiness],
  isReferral: [validateExistanceAndNoEmptiness],
  date: [validateIfDate],
  message: [validateExistanceAndNoEmptiness],
  signature: [validateExistanceAndNoEmptiness],
})

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  const locale = detectLocale(request)

  const baseUrl = `/${locale}/gem-launch-platform`
  const subscribeUrl = `${baseUrl}/panel/subscribe`

  if (!walletAddress) return redirect(baseUrl)

  const { rpcUrls } = chains[BSC_NETWORK]

  const glpLifetimeMembershipContract = new GlpLifetimeMembershipContract(
    config.gemLaunchPlatform.lifetimeMembership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const glpMembershipContract = new GlpMembershipContract(
    config.gemLaunchPlatform.membership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const tensetContract = new TensetContract(BSC_NETWORK, rpcUrls[0])

  const glpExtendContract = new GlpExtendContract(
    config.gemLaunchPlatform.extend[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const [
    membershipBalance,
    lifetimeMembershipBalance,
    glpSubscriptionInfoForMany,
  ] = await Promise.all([
    glpMembershipContract.balanceOf(walletAddress),
    glpLifetimeMembershipContract.balanceOf(walletAddress),
    fetchGlpSubscriptionInfoForMany(walletAddress),
  ])

  const {
    locks: oldReleasedLocks,
    nfts: nftsApi,
    assigningAddresses,
    fromDelegation,
  } = glpSubscriptionInfoForMany

  const [lifetimeMembershipNfts, assigningMembershipBalances] =
    await Promise.all([
      getRpcLifetimeNfts({
        walletAddress,
        glpLifetimeMembershipContract,
        lifetimeMembershipBalance,
      }),
      Promise.all(
        assigningAddresses.map(async address => {
          return {
            address,
            membershipBalance: await glpMembershipContract.balanceOf(address),
          }
        })
      ),
    ])

  const nfts = [...nftsApi, ...lifetimeMembershipNfts]

  const { locks: delegatedLocks, nfts: delegatedNfts } = fromDelegation.reduce(
    (accumulator: GlpSubscriptionInfo, delegation) => {
      accumulator.locks.push(...delegation.locks)
      accumulator.nfts.push(...delegation.nfts)
      return accumulator
    },
    { locks: [], nfts: [] }
  )

  const hasSubscription =
    assigningMembershipBalances.some(({ membershipBalance }) =>
      membershipBalance.gt(0)
    ) ||
    membershipBalance.gt(0) ||
    nfts.length > 0 ||
    delegatedLocks.length > 0 ||
    delegatedNfts.length > 0 ||
    oldReleasedLocks.length > 0

  if (!hasSubscription) return redirect(subscribeUrl)

  const rpcLocks = getRpcLocks({
    walletAddress,
    glpMembershipContract,
    membershipBalance,
  })

  const delegatedRpcLocksPromises = fromDelegation.map(({ address }) => {
    const membershipBalance = assigningMembershipBalances.find(
      balance => balance.address === address
    )?.membershipBalance

    return getRpcLocks({
      walletAddress: address,
      glpMembershipContract,
      membershipBalance: membershipBalance!,
    })
  })

  const delegatedRpcLocks = Promise.all(delegatedRpcLocksPromises).then(
    arrayOfLocksArrays => arrayOfLocksArrays.flat()
  )

  const glpCurrentLaunchData = await getGLPCurrentLaunch()

  const [assignedWallet, referralLocks, extendAllowance] = await Promise.all([
    glpCurrentLaunchData?.launchId
      ? getAssignedWallet(walletAddress, glpCurrentLaunchData?.launchId)
      : null,
    getReferralLocks(walletAddress),
    tensetContract.allowance(walletAddress, glpExtendContract),
  ])

  const isAddressApprovedForReferral = referralApprovedAddresses.some(
    address => address.toLowerCase() === walletAddress.toLowerCase()
  )

  return defer({
    rpcLocks,
    delegatedRpcLocks,
    oldReleasedLocks,
    membershipBalance: membershipBalance.toNumber(),
    nfts,
    assignedWallet,
    assigningAddresses,
    delegatedLocks,
    delegatedNfts,
    referralLocks,
    isAddressApprovedForReferral,
    extendAllowance,
    glpCurrentLaunchData,
  })
}

export type GlpPanelLoaderData = typeof loader

export const action: ActionFunction = async ({ request }) => {
  const requestClone = request.clone()
  const formDataClone = await requestClone.formData()
  const isSetReferralLockForm = !!formDataClone.get('isReferral')

  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.FORM_VALIDATORS)

  if (!isSetReferralLockForm) {
    const data: GlpAssignWalletData = formDataToObject(
      await request.formData(),
      Object.keys(verificationAssignWalletFormSchema)
    )

    const validations = validate(data, verificationAssignWalletFormSchema)
    const formOk = isFormOk(validations)

    if (!formOk) {
      return json({
        validations: translateValidations(t, validations),
        ok: false,
      })
    }

    const signingAddress = signatureValidation(data)
    const {
      subscriptionAddress,
      assignedAddress,
      message,
      signature,
      date,
      launchId,
    } = data

    const { value: messageValues } = JSON.parse(message)

    signatureValidation(data)

    if (
      assignedAddress.toLowerCase() !==
      messageValues.assignedAddress.toLowerCase()
    ) {
      throw new Response('Assigned address mismatch', {
        status: 400,
      })
    }

    if (request.method === 'POST') {
      const result = await insertAssignWallet({
        subscription_address: subscriptionAddress,
        assigned_address: assignedAddress,
        message: message,
        sign: signature,
        date: new Date(date),
        launch_id: launchId,
      })

      return json(result)
    }

    if (request.method === 'DELETE') {
      const result = await deleteAssignWallet(signingAddress, launchId)
      return json(result)
    }
  }

  if (isSetReferralLockForm) {
    const data: GlpReferralLockData = formDataToObject(
      await request.formData(),
      Object.keys(verificationSetReferralLockFormSchema)
    )

    const validations = validate(data, verificationSetReferralLockFormSchema)

    const formOk = isFormOk(validations)

    if (!formOk) {
      return json({
        validations: translateValidations(t, validations),
        ok: false,
      })
    }

    signatureValidation(data)

    const { subscriptionAddress, lockId, date, signature, message } = data

    const isApproved = referralApprovedAddresses.some(
      address => address.toLowerCase() === subscriptionAddress.toLowerCase()
    )

    if (!isApproved) {
      throw new Response('Subscription address not allowed to add referral', {
        status: 400,
      })
    }

    const result = await addReferralLock({
      subscription_address: subscriptionAddress,
      lock_id: Number(lockId),
      message: message,
      sign: signature,
      date: new Date(date),
    })

    return json(result)
  }

  return json({ error: 'Method not allowed' }, { status: 405 })
}

export default function GemLaunchPlatformPanel() {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM,
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
  ])

  const { tensetBalance: loaderTensetBalance, isRevalidating } =
    useOutletContext<GlpOutletData>()

  const {
    assigningAddresses,
    delegatedLocks,
    delegatedNfts,
    rpcLocks: rpcLocksApi,
    oldReleasedLocks,
    delegatedRpcLocks,
    nfts,
    assignedWallet,
    membershipBalance,
    referralLocks,
    isAddressApprovedForReferral,
    extendAllowance: extendAllowanceApi,
    glpCurrentLaunchData,
  } = useLoaderData<typeof loader>()

  const { blockTimestamp } = useOutletContext<GlpOutletData>()

  const [rpcLocks, setRpcLocks] = useState<Promise<GlpLock[]>>(rpcLocksApi)

  const [extendAllowance, setExtendAllowance] = useState(
    BigNumber.from(extendAllowanceApi)
  )
  const [tensetBalance, setTensetBalance] = useState(loaderTensetBalance)

  useEffect(() => {
    setExtendAllowance(BigNumber.from(extendAllowanceApi))
  }, [extendAllowanceApi])

  useEffect(() => {
    setTensetBalance(loaderTensetBalance)
  }, [loaderTensetBalance.toString()])

  useEffect(() => {
    setRpcLocks(rpcLocksApi)
  }, [rpcLocksApi])

  const removeLockFromTable = (lockId: number) => {
    setRpcLocks(previousLocks =>
      previousLocks.then(previousLocks =>
        previousLocks.filter(previousLock => previousLock.id !== lockId)
      )
    )
  }

  const refreshTensetBalance = (amount: BigNumber) => {
    setTensetBalance(previousBalance => previousBalance.add(amount))
  }

  const SNAPSHOT_DATE = glpCurrentLaunchData?.date
    ? new Date(glpCurrentLaunchData?.date)
    : null

  const isValidDate =
    SNAPSHOT_DATE instanceof Date && !Number.isNaN(SNAPSHOT_DATE.getTime())

  const isAfterSnapshot =
    !isValidDate || blockTimestamp > SNAPSHOT_DATE.getTime()

  if (isRevalidating)
    return (
      <div className="flex flex-col gap-8 items-center">
        <H2 isBold>{t('loading-indicator-title')}</H2>

        <LoadingIndicator />
      </div>
    )

  return (
    <div className="flex w-full flex-col gap-6 md:gap-12">
      <TGLPStatus
        tensetBalance={BigNumber.from(tensetBalance)}
        assignedWallet={assignedWallet}
        delegatedLocks={delegatedLocks}
        rpcLocks={rpcLocks}
        delegatedRpcLocks={delegatedRpcLocks}
        nfts={nfts}
        delegatedNfts={delegatedNfts}
        assigningAddresses={assigningAddresses}
        membershipBalance={membershipBalance}
      />

      {!isAfterSnapshot && glpCurrentLaunchData && (
        <AssignWallet
          assignedWallet={assignedWallet}
          launchId={glpCurrentLaunchData.launchId}
          launchName={glpCurrentLaunchData.name}
        />
      )}

      <TglpTokensTable
        membershipBalance={membershipBalance}
        removeLockFromTable={removeLockFromTable}
        refreshTensetBalance={refreshTensetBalance}
        rpcLocks={rpcLocks}
        oldReleasedLocks={oldReleasedLocks}
        referralLocks={referralLocks}
        isAddressApprovedForReferral={isAddressApprovedForReferral}
        extendAllowance={BigNumber.from(extendAllowance)}
        setExtendAllowance={setExtendAllowance}
        tensetBalance={BigNumber.from(tensetBalance)}
      />

      <TglpNftsTable nfts={nfts} />

      <TglpNewsletter
        header={t('newsletter.header-stay-updated')}
        description={t('newsletter.description')}
      />
    </div>
  )
}
