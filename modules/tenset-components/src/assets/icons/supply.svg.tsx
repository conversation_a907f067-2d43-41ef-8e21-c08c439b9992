import { AssetProps } from './index'

export default function Supply({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M100.059 53.5424V41.9304V30.3061C100.059 26.4151 92.0733 23.2704 82.2381 23.2704C72.3906 23.2704 64.4171 26.4151 64.4171 30.3061V41.392C61.2204 40.2051 56.9335 39.4709 52.1935 39.4709C42.3461 39.4709 34.3726 42.6156 34.3726 46.5067V104.603C34.3726 108.495 42.3583 111.639 52.1935 111.639C62.0287 111.639 70.0145 108.482 70.0145 104.603V93.5298C70.0145 93.5298 70.0635 93.5421 70.088 93.5543C71.717 94.1539 73.6399 94.6433 75.7588 94.9614C76.0038 94.9982 76.2487 95.0349 76.4815 95.0716C77.0081 95.145 77.5348 95.2062 78.086 95.2551C78.7351 95.3163 79.3965 95.3652 80.0579 95.4019C80.7805 95.4387 81.5032 95.4631 82.2381 95.4631C82.8505 95.4631 83.4629 95.4509 84.063 95.4264C93.0531 95.0716 100.059 92.0737 100.059 88.4274V76.8154V53.5791V53.5424Z"
        fill="#15121A"
      />
      <path
        d="M57.7664 49.4678C57.7664 49.4678 57.8644 49.4678 57.9134 49.4555C58.9177 49.2108 59.9221 48.9049 60.8774 48.5501C61.1959 48.44 61.3551 48.0851 61.2449 47.767C61.1224 47.4488 60.7794 47.2898 60.461 47.3999C59.5424 47.7425 58.587 48.0239 57.6317 48.2564C57.301 48.3421 57.105 48.6602 57.1785 48.9906C57.252 49.272 57.4969 49.4555 57.7786 49.4555L57.7664 49.4678ZM87.8109 33.2672C87.8109 33.2672 87.9089 33.2672 87.9579 33.255C88.9623 33.0103 89.9666 32.7166 90.922 32.3495C91.2404 32.2272 91.3996 31.8846 91.2894 31.5664C91.1669 31.2483 90.824 31.0892 90.5055 31.2116C89.5869 31.5542 88.6316 31.8356 87.6762 32.0681C87.3455 32.1415 87.1495 32.4719 87.223 32.8023C87.2965 33.0837 87.5415 33.2672 87.8232 33.2672H87.8109ZM96.9235 84.7443V73.1323V49.9083V38.2963V26.6475C96.9235 22.3527 88.8275 19 78.4901 19C68.1528 19 60.0568 22.3649 60.0568 26.6475V36.8646C56.909 35.8123 52.8917 35.1883 48.4456 35.1883C38.096 35.2005 30 38.5532 30 42.8481V100.945C30 105.24 38.096 108.592 48.4334 108.592C58.7707 108.592 66.8667 105.228 66.8667 100.945V90.6911C70.4309 91.8168 74.1176 92.4041 77.8532 92.4041C78.0615 92.4041 78.2697 92.4041 78.4779 92.4041C88.8153 92.4041 96.9113 89.0392 96.9113 84.7566L96.9235 84.7443ZM31.2248 45.6257C33.8459 48.5011 40.4721 50.5079 48.4334 50.5079C49.0335 50.5079 49.6337 50.4956 50.2216 50.4711C50.5645 50.4589 50.8217 50.1775 50.8095 49.8349C50.7972 49.4923 50.5155 49.2353 50.1726 49.2475C49.5969 49.272 49.0213 49.2842 48.4334 49.2842C38.1327 49.2842 31.2248 45.956 31.2248 42.8603C31.2248 39.7646 38.145 36.4364 48.4334 36.4364C58.7217 36.4364 65.6419 39.7646 65.6419 42.8603V43.3742C65.6419 43.3742 65.6419 43.4476 65.6419 43.4844V54.4723C65.6419 57.5803 58.7217 60.8963 48.4334 60.8963C38.145 60.8963 31.2248 57.5681 31.2248 54.4723V45.6257ZM31.2248 57.2377C33.8459 60.1132 40.4721 62.1199 48.4334 62.1199C56.3946 62.1199 63.0208 60.1254 65.6419 57.2377V66.0844C65.6419 69.1923 58.7217 72.5083 48.4334 72.5083C38.145 72.5083 31.2248 69.1801 31.2248 66.0844V57.2377ZM31.2248 68.8619C33.8459 71.7374 40.4721 73.7441 48.4334 73.7441C56.3946 73.7441 63.0208 71.7496 65.6419 68.8619V77.7086C65.6419 80.8166 58.7217 84.1325 48.4334 84.1325C38.145 84.1325 31.2248 80.8043 31.2248 77.7086V68.8619ZM31.2248 80.474C33.8459 83.3494 40.4721 85.3561 48.4334 85.3561C56.3946 85.3561 63.0208 83.3617 65.6419 80.474V89.3206C65.6419 92.4286 58.7217 95.7446 48.4334 95.7446C38.145 95.7446 31.2248 92.4164 31.2248 89.3206V80.474ZM65.6419 100.945C65.6419 104.053 58.7217 107.369 48.4334 107.369C38.145 107.369 31.2248 104.041 31.2248 100.945V92.0982C33.8459 94.9737 40.4721 96.9804 48.4334 96.9804C56.3946 96.9804 63.0208 94.9859 65.6419 92.0982V100.945ZM61.2693 37.3174V29.4251C63.8904 32.3006 70.5167 34.3073 78.4779 34.3073C79.0781 34.3073 79.6782 34.3073 80.2661 34.2706C80.6091 34.2584 80.8663 33.9769 80.854 33.6343C80.8418 33.2917 80.5601 33.047 80.2171 33.047C79.6415 33.0715 79.0658 33.0837 78.4779 33.0837C68.1773 33.0837 61.2693 29.7555 61.2693 26.6598C61.2693 23.564 68.1895 20.2358 78.4779 20.2358C88.7663 20.2358 95.6865 23.564 95.6865 26.6598V38.284C95.6865 41.392 88.7663 44.708 78.4657 44.708C74.4973 44.7814 70.6146 44.1696 66.8667 42.946V42.8603C66.8667 40.6578 64.7356 38.7123 61.2693 37.3296V37.3174ZM78.4779 67.9442C74.534 68.0177 70.6269 67.4058 66.879 66.1822V55.8428C70.4432 56.9685 74.1421 57.5558 77.8777 57.5558C78.086 57.5558 78.2942 57.5558 78.4901 57.5558C86.4514 57.5558 93.0776 55.5613 95.6987 52.6736V61.5203C95.6987 64.6283 88.7785 67.9442 78.4779 67.9442ZM95.6987 64.2857V73.1323C95.6987 76.2403 88.7785 79.5563 78.4779 79.5563C74.534 79.6297 70.6269 79.0179 66.879 77.7943V67.4548C70.4432 68.5805 74.1298 69.1678 77.8655 69.1678C78.0737 69.1678 78.2819 69.1678 78.4901 69.1678C86.4514 69.1678 93.0776 67.1734 95.6987 64.2857ZM78.4779 56.32C74.5218 56.3812 70.6269 55.7816 66.879 54.558V44.2185C70.4432 45.3442 74.1421 45.9316 77.8777 45.9316C78.086 45.9316 78.2942 45.9316 78.4901 45.9316C86.4514 45.9316 93.0776 43.9371 95.6987 41.0494V49.896C95.6987 53.004 88.7785 56.32 78.4779 56.32ZM78.4779 91.1683C74.534 91.2295 70.6269 90.6299 66.879 89.4063V79.0668C70.4432 80.1925 74.1298 80.7799 77.8655 80.7799C78.0737 80.7799 78.2819 80.7799 78.4901 80.7799C86.4514 80.7799 93.0776 78.7854 95.6987 75.8977V84.7443C95.6987 87.8523 88.7785 91.1683 78.4779 91.1683Z"
        fill="url(#paint0_linear_2146_9)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2146_9"
          x1="57.4602"
          y1="119.299"
          x2="108.496"
          y2="30.8133"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
