import SectionCard from './roadmap-item'

interface Section {
  title: string
  description: string
  statuses: {
    pending: string[]
    ongoing: string[]
    done: string[]
  }
}

const sections: { [key: string]: Section } = {
  listings: {
    title: 'Listings',
    description:
      'Expanding to new centralized exchanges to attract the attention of new community members.',
    statuses: {
      pending: ['Tier 1 CEX #2', 'Tier 1 CEX #3', 'Tier 2 CEX #2'],
      ongoing: ['Tier 1 CEX #1', 'Tier 2 CEX #1'],
      done: [],
    },
  },
  launchpad: {
    title: 'Launchpad',
    description:
      'Numerous technical improvements are being made to facilitate community use, along with an increased number of launched projects.',
    statuses: {
      pending: ['Tenset services via Telegram'],
      ongoing: [
        'Increase incubation projects',
        'Launching at least 24 projects, depends on the market conditions',
        'Merge Infinity and TGLP into one platform',
      ],
      done: [
        'Upgrades of TGLP subscriptions',
        'Transferring subscriptions to another wallet',
        'Launching projects on more networks (including Solana and TON)',
        'Extending TGLP subscriptions',
        'Upgrades to Regular NFT',
      ],
    },
  },
  marketing: {
    title: 'Marketing',
    description:
      'Conquering new countries and continents, expanding the community with the help of an extended network of influencer partners.',
    statuses: {
      pending: [
        'KOL management platform',
        'Tenset affiliate platform',
        'Tenset merch',
      ],
      ongoing: [
        'Expansion of marketing (Turkey, India, Middle East, Africa, SEA markets)',
        'New market marking provider',
      ],
      done: ['More exposure on launchpad aggregators like cryptorank'],
    },
  },
  newProducts: {
    title: 'New Products',
    description:
      'Introducing new products that could be game changers for the company and community.',
    statuses: {
      pending: ['Tenset DEX', 'Tenset Card', 'Tenset Hardware Wallet'],
      ongoing: ['Tenset Bridge', 'Tenset Bonds', 'Alpha Pool'],
      done: ['Alpha Launcher'],
    },
  },
}

const Sections = () => {
  return (
    <div>
      {Object.values(sections).map((section, index) => (
        <SectionCard
          key={index}
          title={section.title}
          description={section.description}
          statuses={section.statuses}
        />
      ))}
    </div>
  )
}

export default Sections
