import { AssetProps } from './index'

export function YearlyAccess({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '118'}
      height={size ? (size * 111) / 118 : '111'}
      viewBox="0 0 118 111"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M27.0449 68.9112H21.8139C19.9117 68.9112 18.3529 70.4523 18.3529 72.3621V77.578C18.3529 79.4747 19.8985 81.0289 21.8139 81.0289H27.0449C28.9471 81.0289 30.5059 79.4879 30.5059 77.578V72.3621C30.5059 70.4654 28.9603 68.9112 27.0449 68.9112ZM29.1849 77.578C29.1849 78.7503 28.2338 79.7118 27.0449 79.7118H21.8139C20.6382 79.7118 19.6739 78.7634 19.6739 77.578V72.3621C19.6739 71.1899 20.625 70.2284 21.8139 70.2284H27.0449C28.2206 70.2284 29.1849 71.1767 29.1849 72.3621V77.578ZM76.8059 63.2607H82.0369C83.9391 63.2607 85.4979 61.7196 85.4979 59.8098V54.5939C85.4979 52.6972 83.9524 51.143 82.0369 51.143H76.8059C74.9037 51.143 73.345 52.6841 73.345 54.5939V59.8098C73.345 61.7065 74.8905 63.2607 76.8059 63.2607ZM74.6659 54.5939C74.6659 53.4217 75.617 52.4602 76.8059 52.4602H82.0369C83.2126 52.4602 84.1769 53.4085 84.1769 54.5939V59.8098C84.1769 60.982 83.2258 61.9436 82.0369 61.9436H76.8059C75.6302 61.9436 74.6659 60.9952 74.6659 59.8098V54.5939ZM76.8059 45.4925H82.0369C83.9391 45.4925 85.4979 43.9514 85.4979 42.0416V36.8257C85.4979 34.929 83.9524 33.3748 82.0369 33.3748H76.8059C74.9037 33.3748 73.345 34.9159 73.345 36.8257V42.0416C73.345 43.9383 74.8905 45.4925 76.8059 45.4925ZM74.6659 36.8257C74.6659 35.6535 75.617 34.692 76.8059 34.692H82.0369C83.2126 34.692 84.1769 35.6403 84.1769 36.8257V42.0416C84.1769 43.2138 83.2258 44.1754 82.0369 44.1754H76.8059C75.6302 44.1754 74.6659 43.227 74.6659 42.0416V36.8257ZM63.7019 68.8981H58.4708C56.5686 68.8981 55.0099 70.4391 55.0099 72.349V77.5648C55.0099 79.4615 56.5554 81.0157 58.4708 81.0157H63.7019C65.6041 81.0157 67.1628 79.4747 67.1628 77.5648V72.349C67.1628 70.4523 65.6173 68.8981 63.7019 68.8981ZM65.8418 77.5648C65.8418 78.7371 64.8907 79.6986 63.7019 79.6986H58.4708C57.2952 79.6986 56.3309 78.7503 56.3309 77.5648V72.349C56.3309 71.1767 57.282 70.2152 58.4708 70.2152H63.7019C64.8775 70.2152 65.8418 71.1635 65.8418 72.349V77.5648ZM110.993 83.1363C110.993 83.0573 110.979 82.9783 110.94 82.8992C110.94 82.8729 110.9 82.8466 110.887 82.807C110.861 82.7675 110.847 82.7412 110.821 82.7017L103.291 74.1271C103.291 74.1271 103.265 74.1139 103.265 74.1008C103.225 74.0612 103.186 74.0349 103.146 74.0086C103.106 73.9822 103.08 73.969 103.04 73.9559L103.014 73.9427C102.974 73.9295 102.935 73.9427 102.895 73.9295C102.869 73.9295 102.842 73.9164 102.816 73.9164H84.5864C84.5864 73.9164 84.5336 73.9295 84.5072 73.9295C84.4675 73.9295 84.4279 73.9295 84.3883 73.9427L84.3619 73.9559C84.3222 73.969 84.2958 73.9954 84.2562 74.0086C84.2166 74.0349 84.1637 74.0612 84.1373 74.1008C84.1373 74.1008 84.1109 74.1139 84.1109 74.1271L76.5813 82.7017C76.5813 82.7017 76.5417 82.7675 76.5153 82.807C76.5021 82.8334 76.4757 82.8597 76.4624 82.8992C76.436 82.9783 76.4096 83.0573 76.4096 83.1363C76.4096 83.2154 76.4228 83.2944 76.4624 83.3734C76.4624 83.3997 76.5021 83.4261 76.5153 83.4656C76.5417 83.5051 76.5549 83.5315 76.5813 83.571L93.2124 102.538C93.2124 102.538 93.2388 102.551 93.2388 102.564C93.2652 102.59 93.2784 102.604 93.3048 102.63C93.318 102.643 93.3445 102.656 93.3577 102.67C93.3973 102.696 93.4237 102.709 93.4633 102.722L93.4898 102.735C93.5558 102.762 93.6219 102.775 93.7011 102.775C93.7804 102.775 93.8332 102.775 93.9125 102.735L93.9389 102.722C93.9785 102.709 94.005 102.683 94.0446 102.67C94.071 102.67 94.0842 102.656 94.0974 102.63C94.1238 102.617 94.1503 102.59 94.1635 102.564C94.1635 102.564 94.1899 102.551 94.1899 102.538L110.821 83.571C110.821 83.571 110.861 83.5051 110.887 83.4656C110.9 83.4393 110.927 83.4129 110.94 83.3734C110.966 83.2944 110.993 83.2154 110.993 83.1363ZM101.891 75.2203L99.738 81.7138L94.9825 75.2203H101.878H101.891ZM98.668 82.4778H88.7211L93.7011 75.6682L98.6812 82.4778H98.668ZM92.3934 75.2203L87.6379 81.7138L85.4847 75.2203H92.3801H92.3934ZM84.3222 75.8657L86.515 82.4909H78.5232L84.3222 75.8657ZM78.5232 83.8081H86.951L92.0499 99.2449L78.51 83.8081H78.5232ZM93.7011 100.022L88.338 83.8081H99.0643L93.7011 100.022ZM95.3523 99.2449L100.451 83.8081H108.879L95.3391 99.2449H95.3523ZM100.887 82.4909L103.08 75.8657L108.879 82.4909H100.887ZM63.7019 33.388H58.4708C56.5686 33.388 55.0099 34.929 55.0099 36.8389V42.0548C55.0099 43.9514 56.5554 45.5057 58.4708 45.5057H63.7019C65.6041 45.5057 67.1628 43.9646 67.1628 42.0548V36.8389C67.1628 34.9422 65.6173 33.388 63.7019 33.388ZM65.8418 42.0548C65.8418 43.227 64.8907 44.1885 63.7019 44.1885H58.4708C57.2952 44.1885 56.3309 43.2402 56.3309 42.0548V36.8389C56.3309 35.6666 57.282 34.7051 58.4708 34.7051H63.7019C64.8775 34.7051 65.8418 35.6535 65.8418 36.8389V42.0548ZM66.9647 52.1177C66.7005 51.8543 66.291 51.8543 66.0268 52.1177L61.4694 56.6618L56.9121 52.1177C56.6479 51.8543 56.2384 51.8543 55.9742 52.1177C55.71 52.3811 55.71 52.7894 55.9742 53.0529L60.5315 57.597L55.9742 62.1411C55.71 62.4046 55.71 62.8129 55.9742 63.0763C56.1063 63.208 56.278 63.2739 56.4365 63.2739C56.595 63.2739 56.78 63.208 56.8989 63.0763L61.4562 58.5322L66.0136 63.0763C66.1457 63.208 66.3174 63.2739 66.4759 63.2739C66.6344 63.2739 66.8194 63.208 66.9383 63.0763C67.2024 62.8129 67.2024 62.4046 66.9383 62.1411L62.3809 57.597L66.9383 53.0529C67.2024 52.7894 67.2024 52.3811 66.9383 52.1177H66.9647ZM27.0449 51.143H21.8139C19.9117 51.143 18.3529 52.6841 18.3529 54.5939V59.8098C18.3529 61.7065 19.8985 63.2607 21.8139 63.2607H27.0449C28.9471 63.2607 30.5059 61.7196 30.5059 59.8098V54.5939C30.5059 52.6972 28.9603 51.143 27.0449 51.143ZM29.1849 59.8098C29.1849 60.982 28.2338 61.9436 27.0449 61.9436H21.8139C20.6382 61.9436 19.6739 60.9952 19.6739 59.8098V54.5939C19.6739 53.4217 20.625 52.4602 21.8139 52.4602H27.0449C28.2206 52.4602 29.1849 53.4085 29.1849 54.5939V59.8098ZM102.988 64.4461V14.6451C102.988 10.5225 99.6191 7.16375 95.4844 7.16375H80.7556V4.30555C80.7556 2.27716 79.1044 0.630737 77.0701 0.630737C75.0358 0.630737 73.3846 2.27716 73.3846 4.30555V7.16375H30.4795V4.30555C30.4795 2.27716 28.8282 0.630737 26.7939 0.630737C24.7596 0.630737 23.1084 2.27716 23.1084 4.30555V7.16375H8.3664C4.23176 7.16375 0.863281 10.5225 0.863281 14.6451V87.0877C0.863281 91.2104 4.23176 94.5691 8.3664 94.5691H71.0729C74.3885 103.842 83.2654 110.52 93.7011 110.52C106.95 110.52 117.743 99.7718 117.743 86.5477C117.743 76.6165 111.64 68.0683 102.988 64.4461ZM74.6924 4.31873C74.6924 3.01476 75.7491 1.96105 77.0569 1.96105C78.3647 1.96105 79.4214 3.01476 79.4214 4.31873V16.4364C79.4214 17.7404 78.3647 18.7941 77.0569 18.7941C75.7491 18.7941 74.6924 17.7404 74.6924 16.4364V4.31873ZM24.4162 4.31873C24.4162 3.01476 25.473 1.96105 26.7807 1.96105C28.0885 1.96105 29.1453 3.01476 29.1453 4.31873V16.4364C29.1453 17.7404 28.0885 18.7941 26.7807 18.7941C25.473 18.7941 24.4162 17.7404 24.4162 16.4364V4.31873ZM2.18425 14.6451C2.18425 11.2469 4.95829 8.48089 8.3664 8.48089H23.0952V16.4364C23.0952 18.4648 24.7464 20.1112 26.7807 20.1112C28.815 20.1112 30.4662 18.4648 30.4662 16.4364V8.48089H73.3714V16.4364C73.3714 18.4648 75.0226 20.1112 77.0569 20.1112C79.0912 20.1112 80.7424 18.4648 80.7424 16.4364V8.48089H95.4712C98.8793 8.48089 101.653 11.2469 101.653 14.6451V22.6665H2.18425V14.6451ZM8.3664 93.252C4.95829 93.252 2.18425 90.486 2.18425 87.0877V23.9836H101.653V63.972C99.1567 63.0895 96.4884 62.589 93.6879 62.589C88.7739 62.589 84.058 64.0642 80.0159 66.8697L71.3899 64.5647C71.1654 64.512 70.9144 64.5647 70.7559 64.7359C70.5841 64.9071 70.5181 65.1442 70.5841 65.3681L73.0015 74.3774C70.8087 78.0785 69.6462 82.2802 69.6462 86.5477C69.6462 88.8791 70.0029 91.1182 70.6238 93.252H8.3664ZM93.7011 109.203C81.1783 109.203 70.9804 99.0474 70.9804 86.5477C70.9804 82.4251 72.1297 78.3683 74.2961 74.812C74.3885 74.654 74.4149 74.4696 74.3753 74.2983L72.1825 66.1189L80.0027 68.2131C80.1876 68.2658 80.3989 68.2131 80.5575 68.1078C84.4279 65.3549 88.9853 63.8929 93.7143 63.8929C106.237 63.8929 116.435 74.0481 116.435 86.5477C116.435 99.0474 106.25 109.203 93.7143 109.203H93.7011ZM45.38 68.8981H40.149C38.2468 68.8981 36.688 70.4391 36.688 72.349V77.5648C36.688 79.4615 38.2336 81.0157 40.149 81.0157H45.38C47.2822 81.0157 48.8409 79.4747 48.8409 77.5648V72.349C48.8409 70.4523 47.2954 68.8981 45.38 68.8981ZM47.52 77.5648C47.52 78.7371 46.5689 79.6986 45.38 79.6986H40.149C38.9733 79.6986 38.009 78.7503 38.009 77.5648V72.349C38.009 71.1767 38.9601 70.2152 40.149 70.2152H45.38C46.5557 70.2152 47.52 71.1635 47.52 72.349V77.5648ZM27.0449 33.388H21.8139C19.9117 33.388 18.3529 34.929 18.3529 36.8389V42.0548C18.3529 43.9514 19.8985 45.5057 21.8139 45.5057H27.0449C28.9471 45.5057 30.5059 43.9646 30.5059 42.0548V36.8389C30.5059 34.9422 28.9603 33.388 27.0449 33.388ZM29.1849 42.0548C29.1849 43.227 28.2338 44.1885 27.0449 44.1885H21.8139C20.6382 44.1885 19.6739 43.2402 19.6739 42.0548V36.8389C19.6739 35.6666 20.625 34.7051 21.8139 34.7051H27.0449C28.2206 34.7051 29.1849 35.6535 29.1849 36.8389V42.0548ZM45.38 33.388H40.149C38.2468 33.388 36.688 34.929 36.688 36.8389V42.0548C36.688 43.9514 38.2336 45.5057 40.149 45.5057H45.38C47.2822 45.5057 48.8409 43.9646 48.8409 42.0548V36.8389C48.8409 34.9422 47.2954 33.388 45.38 33.388ZM47.52 42.0548C47.52 43.227 46.5689 44.1885 45.38 44.1885H40.149C38.9733 44.1885 38.009 43.2402 38.009 42.0548V36.8389C38.009 35.6666 38.9601 34.7051 40.149 34.7051H45.38C46.5557 34.7051 47.52 35.6535 47.52 36.8389V42.0548ZM45.38 51.1562H40.149C38.2468 51.1562 36.688 52.6972 36.688 54.6071V59.823C36.688 61.7197 38.2336 63.2739 40.149 63.2739H45.38C47.2822 63.2739 48.8409 61.7328 48.8409 59.823V54.6071C48.8409 52.7104 47.2954 51.1562 45.38 51.1562ZM47.52 59.823C47.52 60.9952 46.5689 61.9567 45.38 61.9567H40.149C38.9733 61.9567 38.009 61.0084 38.009 59.823V54.6071C38.009 53.4348 38.9601 52.4733 40.149 52.4733H45.38C46.5557 52.4733 47.52 53.4217 47.52 54.6071V59.823Z"
        fill="url(#paint0_linear_2932_19466)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2932_19466"
          x1="27.93"
          y1="106.99"
          x2="88.4039"
          y2="1.94061"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
