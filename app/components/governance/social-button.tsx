import { Button, ButtonVariant, TextS } from '~/tenset-components'

interface SocialButtonProps {
  href: string
  buttonContent: JSX.Element
  underButtonContent?: string
}

export function SocialButton({
  href,
  buttonContent,
  underButtonContent,
}: SocialButtonProps) {
  return (
    <div className="flex flex-col gap-2 text-center">
      <Button to={href} className="w-full" variant={ButtonVariant.Secondary}>
        {buttonContent}
      </Button>

      {underButtonContent && (
        <TextS isBold className="text-neutral-100">
          {underButtonContent}
        </TextS>
      )}
    </div>
  )
}
