import { AssetProps } from './index'

export default function LineLogo({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_631_763)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.76192 0H19.2359C21.8565 0 24 2.14351 24 4.76192V19.2359C24 21.8565 21.8565 24 19.2359 24H4.76192C2.14351 24 0 21.8565 0 19.2359V4.76192C0 2.14351 2.14351 0 4.76192 0Z"
          fill={color ?? '#3ACD01'}
        />
        <path
          d="M11.9989 3.95117C16.7801 3.95117 20.6564 7.05519 20.6564 10.8844C20.6564 12.2214 20.1836 13.4707 19.3643 14.5297C19.3236 14.5896 19.2702 14.6559 19.2038 14.7286L19.1996 14.735C18.9193 15.0687 18.6049 15.3832 18.2583 15.6741C15.8624 17.8861 11.9219 20.5216 11.4021 20.1152C10.9507 19.7622 12.1465 18.0337 10.7667 17.7471C10.6705 17.7342 10.5742 17.7235 10.4779 17.7107V17.7085C6.42195 17.1331 3.34146 14.2965 3.34146 10.8844C3.34146 7.05519 7.21774 3.95117 11.9989 3.95117ZM6.97601 13.0942H7.00596H7.01452H8.72162C8.96977 13.0942 9.173 12.891 9.173 12.6429V12.6065C9.173 12.3583 8.96977 12.1551 8.72162 12.1551H7.46589V9.29496C7.46589 9.04681 7.26267 8.84359 7.01452 8.84359H6.97601C6.72786 8.84359 6.52463 9.04681 6.52463 9.29496V12.6429C6.52463 12.891 6.72786 13.0942 6.97601 13.0942ZM17.7962 10.9957V10.9593C17.7962 10.709 17.593 10.5058 17.3448 10.5058H16.087V9.79126H17.3448C17.593 9.79126 17.7962 9.58804 17.7962 9.33989V9.30352C17.7962 9.05323 17.593 8.85 17.3448 8.85H15.6356H15.627H15.5992C15.3489 8.85 15.1457 9.05323 15.1457 9.30352V12.6514C15.1457 12.8996 15.3489 13.1028 15.5992 13.1028H15.627H15.6356H17.3448C17.593 13.1028 17.7962 12.8996 17.7962 12.6514V12.6129C17.7962 12.3648 17.593 12.1615 17.3448 12.1615H16.087V11.447H17.3448C17.593 11.447 17.7962 11.2438 17.7962 10.9957ZM14.4376 12.9595C14.5189 12.8782 14.5681 12.7669 14.5681 12.6429V9.29496C14.5681 9.04681 14.3649 8.84359 14.1168 8.84359H14.0804C13.8301 8.84359 13.6269 9.04681 13.6269 9.29496V11.2609L11.9968 9.07248C11.9176 8.93557 11.77 8.84359 11.6032 8.84359H11.5668C11.3186 8.84359 11.1154 9.04681 11.1154 9.29496V12.6429C11.1154 12.891 11.3186 13.0942 11.5668 13.0942H11.6032C11.8513 13.0942 12.0545 12.891 12.0545 12.6429V10.6405L13.6996 12.8867C13.7082 12.9017 13.721 12.9167 13.7317 12.9317C13.7766 12.9937 13.8387 13.0343 13.9071 13.06C13.9606 13.0835 14.0183 13.0942 14.0782 13.0942H14.1168C14.1938 13.0942 14.2644 13.075 14.3285 13.0429C14.3735 13.0215 14.412 12.9937 14.4376 12.9595ZM9.99018 13.0942H10.0265C10.2747 13.0942 10.4779 12.891 10.4779 12.6429V9.29496C10.4779 9.04681 10.2747 8.84359 10.0265 8.84359H9.99018C9.74203 8.84359 9.53881 9.04681 9.53881 9.29496V12.6429C9.53881 12.891 9.74203 13.0942 9.99018 13.0942Z"
          fill={color ?? 'white'}
        />
      </g>
      <defs>
        <clipPath id="clip0_631_763">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
