import type { TFunction } from 'i18next'

import {
  Button,
  ButtonVariant,
  Icon,
  IconName,
  VideoButton,
} from '~/tenset-components'
import type { CardWithTile } from '~/components/cards-with-title'
import type { UseLocalizePathname } from '~/i18n/use-localize-pathname'

export const whatIsUnique = (
  t: TFunction,
  localizePathname: UseLocalizePathname
): CardWithTile[] => [
  {
    title: t('what-is-unique.claim-back.title'),
    description: t('what-is-unique.claim-back.description'),
    image: IconName.Refund,
    action: (
      <VideoButton
        buttonVariant={ButtonVariant.Secondary}
        youtubeId="oRrVNFRfSXc"
        buttonLabel={t('what-is-unique.claim-back.action')}
      />
    ),
  },
  {
    title: t('what-is-unique.tradable.title'),
    description: t('what-is-unique.tradable.description'),
    image: IconName.TradableVesting,
  },
  {
    title: t('what-is-unique.voting.title'),
    description: t('what-is-unique.voting.description'),
    image: IconName.Voting,
    action: (
      <Button
        variant={ButtonVariant.Secondary}
        to={localizePathname('/governance')}
      >
        {t('what-is-unique.voting.action')}

        <Icon name={IconName.ChevronRight16} />
      </Button>
    ),
  },
]
