/** @type {import('@remix-run/dev').AppConfig} */
module.exports = {
  ignoredRouteFiles: ['**/.*'],
  serverModuleFormat: 'cjs',
  serverDependenciesToBundle: [
    'react-sticky-box',
    /^remix-utils.*/,
    /^remix-i18next.*/,
  ],
  browserNodeBuiltinsPolyfill: {
    modules: {
      buffer: true,
      events: true,
    },
  },
  watchPaths: [
    './modules/tenset-components/src',
    // disabled, because any change in this lib causes "wallet is not defined" error,
    // because HMR is setting walletContext to null.
    // './modules/tenset-web3/lib'
  ],
}
