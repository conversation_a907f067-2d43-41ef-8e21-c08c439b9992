import { useState, useEffect } from 'react'

import { Voting } from './voting'

import { store } from '~/state/governance'
// import { countVotings } from '~/data'

export const useVotings = () => {
  const [loading, setLoading] = useState(true)
  const [votings, setVotings] = useState<Voting[]>([])

  useEffect(() => {
    setLoading(true)

    store
      .select()
      .then(votings => {
        //To update json remove where('endsAt', '>', new Date()) from app/state/governance/firebase/voting.repository.ts and uncomment line below
        // countVotings(votings) // -> data/governance.ts
        return votings.map(dto => Voting.make(dto))
      })
      .then(votings => {
        setLoading(false)

        setVotings(votings)
      })
  }, [])

  return { loading, votings }
}
