import clsx from 'clsx'
import type { HTMLAttributes } from 'react'

import { H3, Text } from '~/tenset-components'

export interface KeyPointProps extends HTMLAttributes<HTMLDivElement> {
  label: string
}

export function KeyPoint({ label, children, className }: KeyPointProps) {
  return (
    <div
      className={clsx('rounded-[10px] p-6 sm:p-8 bg-neutral-800', className)}
    >
      <H3 isBold className="mb-2">
        {label}
      </H3>

      <Text>{children}</Text>
    </div>
  )
}
