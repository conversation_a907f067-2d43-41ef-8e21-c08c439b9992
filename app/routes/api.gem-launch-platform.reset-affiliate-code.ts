import type { ActionFunctionArgs } from '@remix-run/node'

import { commitSession, getSession } from '~/sessions.server'

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request.headers.get('Cookie'))

  session.unset('lastGlpAffiliateCode')

  return new Response('', {
    status: 200,
    headers: {
      'Set-Cookie': await commitSession(session),
    },
  })
}
