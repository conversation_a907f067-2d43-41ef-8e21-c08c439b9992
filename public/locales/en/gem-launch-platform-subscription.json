{"affiliate": {"cancel": "Subscribe without affiliation", "info": "You are subscribing from an affiliate link. The number of 10SET tokens that you can withdraw after 365 days will be reduced by {{reducedPercentage}}%. In return, you get a {{higherAllocationPercentage}}% higher allocation in each launch."}, "allocation-weight": "Allocation weight for upcoming IDO", "allowance": "Allowance", "assign-wallet": {"assign-wallet": "Delegate wallet", "assigned-wallet": "Delegated wallet", "assigned-wallet-info": "Your participation in the upcoming IDO has been delegated to the specified address. Post-sale, delegation automatically reverts to your original wallet. Snapshots of both delegated and original wallets are taken at the same time.", "assigned-wallet-title": "Another wallet delegated for the upcoming IDO", "invalid-address": "Invalid delegation wallet address", "modal-info": "Enter the address you wish to delegate to. This will only be eligible for the upcoming sale. You must sign a transaction on your wallet to verify the change.", "not-assigned-wallet-info": "Delegate another wallet for upcoming IDO participation before the snapshot. Post-sale, it reverts to your original wallet. Snapshots capture both original and delegated wallets at the same time.", "not-assigned-wallet-title": "Want to use another wallet for the upcoming IDO?", "remove-wallet": "Remove delegation", "update-wallet": "Update wallet", "wallet-address": "Delegated wallet address"}, "balance": "Balance", "chain": "Chain", "confirm": "Confirm", "extend": {"balance": "You have {{amount}} 10SET", "balance-required": "It requires {{amount}} 10SET to extend subscription for this length of time.", "button": "Extend", "button-allowance": "Extend - increase allowance", "description": "You can extend your subscription for multiple years in one go. Additional {{amount}} 10SET is required for each year you wish to add to your subscription.", "error": "Not enough balance. You need {{amount}} 10SET tokens to proceed.", "number-of-years": "Number of years", "title": "Extend subscription"}, "extended": "Extended", "extended-successfully": "Subscription extended successfully!", "insufficient-balance": "It looks like you don't have enough 10set tokens to purchase this tier.", "insufficient-balance-upgrade": "It looks like you don't have enough 10set tokens to upgrade to this tier.", "lifetime-access": "Lifetime access", "lock": "Lock", "lock-more": {"button": "Get more allocations"}, "locks": "Locks", "meta": {"description": "Get access to launchpad featuring only the greatest crypto projects with huge potential.", "title": "Tenset Gem Launch Platform - Subscriptions"}, "nfts": "NFTs", "nfts-table": {"actions": {"go-to-nft": "Go to NFT"}, "headers": {"collection": "Collection", "image": "Image", "level": "Level", "title": "Title"}, "title": "NFTs"}, "not-sufficient-balance": {"balance": "Your balance: <semibold>{{amount}} 10SET</semibold>", "description": "You need to own at least {{amount}} 10SET to get access to this platform. Purchase tokens on one of the below exchanges, then come back here to proceed.", "title": "10SET balance on your wallet is not sufficient to subscribe"}, "purchase-tokens": {"action": "Purchase more 10set tokens", "header": "Purchase tokens on one of the below exchanges, then come back here to proceed."}, "referral-program": {"boost": "+{{bonus}}% bonus", "modal-info": "You can receive a {{bonus}}% boost to your allocation in each launch for the duration of your lock, by giving up {{commission}}% of your locked 10SET tokens.", "modal-title": "Add referral", "set-referral-button": "Add referral"}, "regular-nft": "Regular NFT", "release-date": "Release date", "status": {"active": {"title": "Your subscription is active"}, "delegating-addresses": "Delegating addresses", "inactive": {"title": "Your subscription is inactive"}}, "subscribe": {"allowance": {"button": "Allow locking", "description": "First, you need to 'Allow locking' to allow the smart contract to move your 10SET tokens.", "note": "None of your tokens will be locked at this stage"}, "lock": {"balance": "Balance", "button": "Confirm & lock for 365 days", "description": "Your tokens will remain safely locked and you will be able to withdraw them after 365 days. During that time you will have access to all Gem Launches.", "note": "You can review your lock in this service at any time", "you-lock": "You lock"}, "quantity": "Quantity", "tier": {"button": "Confirm", "description": "Select the subscription from the tier you want to buy. If you want, you can even buy several subscriptions from a given tier.", "note-1": "You can review your lock in this service at any time.", "note-2": "You will only be able to withdraw your tokens after 365 days.", "subscription-tier": "Subscription tier"}, "title": "Subscribe to Gem Launch Platform"}, "subscription": "Subscription", "subscriptions": "Subscriptions", "switch-network": {"button": "Switch network to {{network}}", "title": "Please switch your network to {{network}}"}, "tier": "Tier", "tokens-table": {"headers": {"amount": "Amount", "level": "Level", "release-date": "Release date", "status": "Status"}, "statuses": {"locked": "Locked", "released": "Released", "withdrawn": "Withdrawn"}, "title": "Locks"}, "transfer": {"allowance": {"button": "Allow transferring", "description": "First, you need to 'Allow transferring' to allow the smart contract to move your NFTs.", "note": "None of your NFTs will be transferred at this stage.", "title": "Allowance"}, "note-1": "Ensure that you enter the correct wallet address that you wish to transfer to.", "note-2": "This transaction cannot be reversed if a mistake is made.", "subscriptions": {"button": "Select subscriptions to transfer", "description": "Select which subscriptions you want to transfer.", "infinity-locks-page": "Infinity locks page", "nft-locked-in-infinity": "This NFT has been locked on Infinity platform. If you want to transfer it, please go to <link></link> and withdraw it first.", "note": "This transaction cannot be reversed if a mistake is made.", "subscription-type": "Subscription type", "title": "Subscriptions"}, "title": "Transfer Subscriptions of Gem Launch Platform", "transfer": {"button": "Transfer subscriptions", "description": "Transfer selected subscriptions to destination wallet address. This wallet will access upcoming launches and can withdraw 10SET tokens after lock expiration or trade transferred NFTs.", "title": "Transfer"}, "wallet": {"button": "Confirm destination wallet address", "description": "You can transfer your subscription to another wallet. This wallet will then have access to upcoming launches, and can withdraw the 10SET tokens after lock expires or trade the transferred NFTs.", "invalid-address": "Invalid destination wallet address", "title": "Wallet", "transfer-destination": "Subscription transfer destination"}}, "upgrade": {"allowance": {"button": "Allow upgrading", "description": "First, you need to 'Allow upgrading' to allow the smart contract to move your 10SET tokens.", "note": "None of your tokens will be locked at this stage"}, "button": "Upgrade", "confirm": {"button-label-nft": "Confirm & permanently burn 5,000 10SET tokens", "button-label-tokens": "Confirm & lock for {{days}} days", "description-nft": "Your assets will be permanently burned and you will never be able to withdraw them. You will receive a Regular NFT that will have lifetime access to all Gem Launches.", "description-tokens": "Your assets will remain safely locked and you will only be able to withdraw them after the lock ends. During that time you will have access to all Gem Launches.", "note-nft-1": "Regular NFT will be minted immediately after burn tokens.", "note-nft-2": "NFT is tradable, so you can trade it on the secondary market.", "note-tokens": "You can review your subscriptions in this service at any time."}, "get-return": "You get return", "tier": {"affiliate-deactivated": "Affiliate bonus will be deactivated after upgrade.", "button": "Upgrade selected tier", "description": "You can upgrade any active lock you have on your wallet.", "note": "You can review your subscriptions in this service at any time.", "select-lock": "Select lock"}, "title": "Upgrade Tier to Gem Launch Platform", "tokens-needed": "Additional tokens needed", "upgrade-to": "Upgrade to"}, "wallet-address": "Wallet address", "withdraw": "Withdraw", "withdraw-info": "You can withdraw after 365 days", "withdrawn-successfully": "Subscription withdrawn successfully!", "you-subscribed-successfully": {"button": "Show me my subscription", "description": "Well done! You can now participate in Gem Launches for the next 365 days.", "title": "You subscribed to Gem Launch Platform successfully!"}, "you-transferred-successfully": {"back-to-panel": "Back to TGLP panel", "description": "Your subscription should appear on your new wallet address within a few minutes.", "title": "Subscriptions successfully transferred!", "track": "You can track status:", "transfer-more": "Transfer more subscriptions"}, "you-upgraded-successfully": {"button": "Show me my subscription", "description": "Well done! You can now participate in Gem Launches for the next {{days}} days.", "description-lifetime": "Well done! You can now participate in Gem Launches forever.", "title": "You upgraded your Gem Launch Platform subscription successfully!"}}