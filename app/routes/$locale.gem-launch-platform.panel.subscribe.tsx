import { AddressZero } from '@ethersproject/constants'
import type { LoaderFunctionArgs } from '@remix-run/node'
import { json, redirect } from '@remix-run/node'
import {
  useFetcher,
  useLoaderData,
  useOutletContext,
  useSearchParams,
} from '@remix-run/react'
import clsx from 'clsx'
import { BigNumber } from 'ethers'
import { formatEther } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import type { ChangeEvent } from 'react'
import { Fragment, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { ConnectWallet } from '~/components/connect-wallet'
import {
  AffiliateInfo,
  PurchaseTokensModal,
} from '~/components/gem-launch-platform'
import { SwitchNetworkButton } from '~/components/switch-network/button'
import { BSC_NETWORK } from '~/config'
import type {
  GlpAffiliateCodeWithParameter,
  GlpSubscribeOffer,
} from '~/data/gem-launch-platform'
import {
  getTierIndex,
  glpAffiliateCodes,
  glpSubscribeOffers,
  GlpTokenId,
} from '~/data/gem-launch-platform'
import { detectLocale, Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import type { GlpOutletData } from '~/routes/$locale.gem-launch-platform.panel'
import { getSession } from '~/sessions.server'
import {
  Button,
  H2,
  H3,
  Icon,
  IconName,
  Input,
  NumberFormatter,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Text,
  TextL,
  TextS,
} from '~/tenset-components'
import {
  chains,
  config,
  GlpSubscribeContract,
  parseEthersError,
  TensetContract,
  useGlpContracts,
  useTensetContract,
  useWallet,
} from '~/tenset-web3'
import { cutDecimals, withoutCache } from '~/utils'

enum SubscribeStep {
  Tier,
  Allowance,
  Lock,
}

type SubscribeStepData = {
  [key in SubscribeStep]: {
    title: string
    description: string
    icon: IconName
    action: JSX.Element
    note: string[]
  }
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const locale = detectLocale(request)

  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  const url = new URL(request.url)

  const affiliateCode =
    url.searchParams.get('affiliate_code') ||
    session.get('lastGlpAffiliateCode')

  const supportedAffiliateParameters = glpAffiliateCodes.map(
    ({ parameter }) => parameter
  )

  const SUBSCRIBE_WITHOUT_AFFILIATE_URL = `/${locale}/gem-launch-platform/panel/subscribe`

  if (affiliateCode && !supportedAffiliateParameters.includes(affiliateCode)) {
    return redirect(SUBSCRIBE_WITHOUT_AFFILIATE_URL)
  }

  let subscribeAllowance = BigNumber.from(0)

  if (walletAddress) {
    const { rpcUrls } = chains[BSC_NETWORK]

    const tensetContract = new TensetContract(BSC_NETWORK, rpcUrls[0])

    const glpSubscribeContract = new GlpSubscribeContract(
      config.gemLaunchPlatform.subscribe[BSC_NETWORK]!,
      BSC_NETWORK,
      undefined,
      rpcUrls[0]
    )

    subscribeAllowance = await tensetContract.allowance(
      walletAddress,
      glpSubscribeContract
    )
  }

  return withoutCache(json({ affiliateCode, subscribeAllowance }))
}

export default function GlpSubscribe() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])
  const localizePathname = useLocalizePathname()

  const { tensetBalance, isRevalidating } = useOutletContext<GlpOutletData>()

  const fetcher = useFetcher({ key: 'root' })

  const { affiliateCode, subscribeAllowance: subscribeAllowanceApi } =
    useLoaderData<typeof loader>()

  const [subscribeAllowance, setSubscribeAllowance] = useState(
    BigNumber.from(subscribeAllowanceApi)
  )

  useEffect(() => {
    setSubscribeAllowance(BigNumber.from(subscribeAllowanceApi))
  }, [subscribeAllowanceApi])

  const [hasSubscribed, setHasSubscribed] = useState(false)

  const onLock = async () => {
    setHasSubscribed(true)
  }

  const [searchParameters, setSearchParameters] = useSearchParams()

  const [currentStep, setCurrentStep] = useState<SubscribeStep>(
    SubscribeStep.Tier
  )

  const [errorMessage, setErrorMessage] = useState<string | undefined>()

  const [quantityInput, setQuantityInput] = useState<string>('1')
  const [quantity, setQuantity] = useState<BigNumber>(BigNumber.from('1'))

  useEffect(() => {
    setQuantity(BigNumber.from(quantityInput || '1'))
  }, [quantityInput])

  const offersId = glpSubscribeOffers.map(({ id }) => id)

  const parameterId = searchParameters.get('id')
    ? Number.parseInt(searchParameters.get('id')!)
    : null

  const [selectedOfferId, setSelectedOfferId] = useState<number>(
    parameterId && offersId.includes(parameterId)
      ? parameterId
      : offersId.at(-1)!
  )

  const [selectedOffer, setSelectedOffer] = useState<GlpSubscribeOffer>(
    glpSubscribeOffers.find(({ id }) => id === selectedOfferId)!
  )

  useEffect(() => {
    setSelectedOffer(
      glpSubscribeOffers.find(({ id }) => id === selectedOfferId)!
    )
  }, [selectedOfferId])

  const selectedAffiliateCode = glpAffiliateCodes.find(
    ({ parameter }) => parameter === affiliateCode
  ) || {
    parameter: '',
    affiliate: AddressZero,
    commissionNumerator: 0,
    commissionDenominator: 100,
    bonusNumerator: 0,
    bonusDenominator: 100,
    signature: '0x',
  }

  useEffect(() => {
    setSearchParameters(
      previous => {
        previous.set('id', selectedOfferId.toString())

        if (affiliateCode) previous.set('affiliate_code', affiliateCode)

        return previous
      },
      {
        preventScrollReset: true,
        replace: true,
      }
    )
  }, [selectedOfferId, affiliateCode])

  const selectedOfferPriceFormatted = Number.parseInt(
    formatEther(selectedOffer.price)
  )

  const { commissionNumerator = 0, commissionDenominator = 100 } =
    selectedAffiliateCode || {}

  const selectedOfferAffiliateWithdrawable =
    selectedOfferPriceFormatted -
    selectedOfferPriceFormatted * (commissionNumerator / commissionDenominator)

  const [requiredAllowance, setRequiredAllowance] = useState(
    selectedOffer.price.mul(quantity).sub(subscribeAllowance)
  )

  // TODO: TMP wallet & chain change event via root fetcher
  // wallet has been changed, so we are going back to first step
  useEffect(() => {
    if (
      fetcher.state === 'submitting' &&
      fetcher.formAction.includes('/api/wallet')
    ) {
      setCurrentStep(SubscribeStep.Tier)
      setErrorMessage(undefined)
    }
  }, [fetcher.state])

  useEffect(() => {
    setRequiredAllowance(
      selectedOffer.price.mul(quantity).sub(subscribeAllowance)
    )
  }, [selectedOffer, quantity, subscribeAllowance])

  useEffect(() => {
    if (parameterId) return

    setSelectedOfferId(offersId.at(-1)!)
  }, [affiliateCode, parameterId])

  const onTierSelect = () =>
    setCurrentStep(
      requiredAllowance.gt(0) ? SubscribeStep.Allowance : SubscribeStep.Lock
    )
  const onIncreaseAllowance = () => setCurrentStep(SubscribeStep.Lock)

  const steps: SubscribeStepData = {
    [SubscribeStep.Tier]: {
      title: t('tier'),
      description: t('subscribe.tier.description'),
      icon: IconName.Tiers,
      action: (
        <TierSelector
          balance={tensetBalance}
          t={t}
          offers={glpSubscribeOffers}
          selectedOfferId={selectedOfferId}
          setSelectedOfferId={setSelectedOfferId}
          selectedOffer={selectedOffer}
          selectedOfferPriceFormatted={selectedOfferPriceFormatted}
          selectedOfferAffiliateWithdrawable={
            selectedOfferAffiliateWithdrawable
          }
          onTierSelect={onTierSelect}
          affiliateCode={affiliateCode}
          setQuantityInput={setQuantityInput}
          quantityInput={quantityInput}
          quantity={quantity}
          isRevalidating={isRevalidating}
        />
      ),
      note: [t('subscribe.tier.note-1'), t('subscribe.tier.note-2')],
    },
    [SubscribeStep.Allowance]: {
      title: t('allowance'),
      description: t('subscribe.allowance.description'),
      icon: IconName.Security,
      action: (
        <SubscriptionAllowance
          requiredAllowance={requiredAllowance}
          setRequiredAllowance={setRequiredAllowance}
          t={t}
          onIncreaseAllowance={onIncreaseAllowance}
          setErrorMessage={setErrorMessage}
        />
      ),
      note: [t('subscribe.allowance.note')],
    },
    [SubscribeStep.Lock]: {
      title: t('lock'),
      description: t('subscribe.lock.description'),
      icon: IconName.Lock,
      action: (
        <Lock
          balance={tensetBalance}
          t={t}
          onLock={onLock}
          selectedAffiliateCode={selectedAffiliateCode}
          selectedOffer={selectedOffer}
          selectedOfferPriceFormatted={selectedOfferPriceFormatted}
          selectedOfferAffiliateWithdrawable={
            selectedOfferAffiliateWithdrawable
          }
          quantity={quantity}
          setErrorMessage={setErrorMessage}
        />
      ),
      note: [t('subscribe.lock.note')],
    },
  }

  const DASHBOARD_URL = localizePathname('/gem-launch-platform/panel')

  if (hasSubscribed)
    return (
      <div className="flex flex-col gap-4 md:items-center md:justify-center">
        <div className="flex flex-col-reverse items-center justify-center gap-6 text-center">
          <div className="flex flex-col gap-4">
            <H3 isBold>{t('you-subscribed-successfully.title')}</H3>

            <Text>{t('you-subscribed-successfully.description')}</Text>
          </div>

          <Icon name={IconName.YearlyAccess} />
        </div>

        <Button to={DASHBOARD_URL}>
          {t('you-subscribed-successfully.button')}
        </Button>
      </div>
    )

  return (
    <>
      {affiliateCode && selectedAffiliateCode && (
        <AffiliateInfo selectedAffiliateOffer={selectedAffiliateCode} />
      )}

      <div className="flex flex-col items-center justify-center gap-4 md:max-w-[720px] md:pt-32">
        <div className="flex flex-col gap-6 text-center">
          <H2 isBold>{t('subscribe.title')}</H2>

          <div className="flex flex-row items-center justify-center gap-2 md:gap-4 align-middle">
            {Object.values(steps).map(({ title }, index) => {
              const isCurrentStep = currentStep === index
              const isStepDone = currentStep > index
              const isFutureStep = currentStep < index

              return (
                <Fragment key={title}>
                  <div
                    className={clsx(
                      'flex h-12 w-12 items-center justify-center rounded-full',
                      isCurrentStep && 'bg-neutral-700 text-white',
                      isStepDone && 'bg-green-900 text-green-600',
                      isFutureStep &&
                        'border border-neutral-400 text-neutral-100'
                    )}
                  >
                    {isStepDone ? (
                      <Icon name={IconName.Success} />
                    ) : (
                      <TextS isBold>{index + 1}</TextS>
                    )}
                  </div>

                  <TextS
                    className={clsx(
                      isStepDone && 'text-green-600',
                      isCurrentStep && 'text-white',
                      isFutureStep && 'text-neutral-100'
                    )}
                    isBold
                  >
                    {title}
                  </TextS>
                </Fragment>
              )
            })}
          </div>

          <div className="flex items-center justify-center">
            <Icon name={steps[currentStep].icon} />
          </div>

          <TextL>{steps[currentStep].description}</TextL>

          <div className="relative mx-auto w-full flex flex-col gap-4 items-stretch justify-center md:w-[492px]">
            {steps[currentStep].action}

            {errorMessage && (
              <TextS className="text-red-500">{errorMessage}</TextS>
            )}

            {steps[currentStep].note.length > 0 && (
              <div>
                {steps[currentStep].note.map((note, index) => (
                  <TextS key={index} className="text-neutral-100">
                    {note}
                  </TextS>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

interface TierSelectorProps {
  balance: BigNumber
  t: TFunction
  offers: GlpSubscribeOffer[]
  selectedOfferId: number
  setSelectedOfferId: (index: number) => void
  selectedOffer: GlpSubscribeOffer
  selectedOfferPriceFormatted: number
  selectedOfferAffiliateWithdrawable: number
  onTierSelect: () => void
  affiliateCode: string | null
  quantity: BigNumber
  setQuantityInput: (quantity: string) => void
  quantityInput: string
  isRevalidating: boolean
}

function TierSelector({
  balance,
  t,
  offers,
  selectedOfferId,
  setSelectedOfferId,
  selectedOffer,
  selectedOfferPriceFormatted,
  selectedOfferAffiliateWithdrawable,
  onTierSelect,
  affiliateCode,
  quantity,
  setQuantityInput,
  quantityInput,
  isRevalidating,
}: TierSelectorProps) {
  const { wallet } = useWallet()

  const isOnCorrectNetwork = wallet?.chain === BSC_NETWORK
  const insufficientBalance = balance.lt(selectedOffer.price.mul(quantity))

  function onQuantityInput(event: ChangeEvent<HTMLInputElement>) {
    const { currentTarget } = event
    const value = currentTarget.value.replace(/^0+(?=\d)/, '') // remove leading zeros

    if (value === '0') return event.preventDefault()

    try {
      BigNumber.from(value || '1').toNumber()

      setQuantityInput(value)
    } catch {
      event.preventDefault()
    }
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-3 sm:gap-6">
        <div className="w-full">
          <TextS isBold className="text-start">
            {t('subscription')}
          </TextS>

          <Select
            disabled={offers.length < 2}
            value={selectedOfferId.toString()}
            onValueChange={value => setSelectedOfferId(Number.parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={t('subscribe.tier.subscription-tier')}
              />
            </SelectTrigger>

            <SelectContent>
              <SelectGroup>
                {offers.map(tier => {
                  const { price, id } = tier

                  const priceFormatted = Number.parseInt(formatEther(price))

                  return (
                    <SelectItem key={id} value={id.toString()}>
                      {t('gem-launch-platform:tiers-privileges.tier', {
                        tier: getTierIndex(priceFormatted),
                      })}{' '}
                      (
                      <NumberFormatter value={priceFormatted} />)
                    </SelectItem>
                  )
                })}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="h-full">
          <TextS isBold className="text-start">
            {t('subscribe.quantity')}
          </TextS>

          <div className="w-28">
            <Input
              type="text"
              inputMode="numeric"
              inputSize="medium"
              className="bg-neutral-900 border-neutral-100 h-[54px]"
              value={quantityInput}
              onInput={onQuantityInput}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-1">
        <div className="flex justify-between gap-2">
          <TextS isBold>{t('subscribe.lock.you-lock')}</TextS>

          {isOnCorrectNetwork && (
            <TextS isBold className="flex items-center justify-center gap-1">
              {t('balance')}:{' '}
              {isRevalidating ? (
                <span className="h-[16px] w-[75px] animate-pulse rounded-md bg-neutral-600"></span>
              ) : (
                cutDecimals(formatEther(balance))
              )}
            </TextS>
          )}
        </div>

        <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
          <div className="p-3 text-neutral-100">
            <NumberFormatter
              value={selectedOfferPriceFormatted * quantity.toNumber()}
            />
          </div>

          <Text className="border-l-2 border-neutral-500 p-3 px-8" isBold>
            10set
          </Text>
        </div>
      </div>

      {affiliateCode && (
        <div className="flex flex-col gap-1">
          <div className="flex">
            <TextS isBold>{t('withdraw-info')}</TextS>
          </div>

          <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
            <div className="p-3 text-neutral-100">
              <NumberFormatter
                value={selectedOfferAffiliateWithdrawable * quantity.toNumber()}
              />
            </div>

            <Text className="border-l-2 border-neutral-500 p-3 px-8" isBold>
              10set
            </Text>
          </div>
        </div>
      )}

      <SubscriptionAction
        t={t}
        isOnCorrectNetwork={isOnCorrectNetwork}
        insufficientBalance={insufficientBalance}
        onTierSelect={onTierSelect}
        isRevalidating={isRevalidating}
      />
    </div>
  )
}

interface SubscriptionActionProps {
  t: TFunction
  onTierSelect: () => void
  isOnCorrectNetwork: boolean
  insufficientBalance: boolean
  isRevalidating: boolean
}

function SubscriptionAction({
  t,
  onTierSelect,
  isOnCorrectNetwork,
  insufficientBalance,
  isRevalidating,
}: SubscriptionActionProps) {
  const { wallet } = useWallet()

  const { name } = chains[BSC_NETWORK]

  if (!wallet?.isConnected) return <ConnectWallet />

  if (!isOnCorrectNetwork)
    return <SwitchNetworkButton id={BSC_NETWORK} name={name} />

  if (isRevalidating)
    return <Button disabled>{t('subscribe.tier.button')}</Button>

  if (insufficientBalance)
    return (
      <>
        <TextS className="text-red-500">{t('insufficient-balance')}</TextS>

        <PurchaseTokensModal />
      </>
    )

  return <Button onClick={onTierSelect}>{t('subscribe.tier.button')}</Button>
}

interface SubscriptionAllowanceProps {
  requiredAllowance: BigNumber
  setRequiredAllowance: (value: BigNumber) => void
  t: TFunction
  onIncreaseAllowance?: () => void
  setErrorMessage: (message?: string) => void
}

function SubscriptionAllowance({
  requiredAllowance,
  setRequiredAllowance,
  t,
  onIncreaseAllowance,
  setErrorMessage,
}: SubscriptionAllowanceProps) {
  const { wallet } = useWallet()

  const { tensetContract } = useTensetContract(BSC_NETWORK)

  const { glpSubscribeContract } = useGlpContracts(BSC_NETWORK)

  const increaseAllowance = async () => {
    if (
      !wallet ||
      wallet?.isProcessing ||
      !tensetContract ||
      !glpSubscribeContract
    )
      return

    setErrorMessage(undefined)

    await wallet
      .interact(tensetContract)
      .increaseAllowance(glpSubscribeContract, requiredAllowance)
      .then(() => {
        setRequiredAllowance(BigNumber.from(0))

        onIncreaseAllowance?.()
      })
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)
      })
  }

  return (
    <Button
      loading={wallet?.isProcessing}
      disabled={wallet?.isProcessing}
      onClick={() => increaseAllowance()}
    >
      {t('subscribe.allowance.button')}
    </Button>
  )
}

interface LockProps {
  balance: BigNumber
  t: TFunction
  onLock?: () => void
  selectedAffiliateCode: GlpAffiliateCodeWithParameter
  selectedOffer: GlpSubscribeOffer
  selectedOfferPriceFormatted: number
  selectedOfferAffiliateWithdrawable: number
  quantity: BigNumber
  setErrorMessage: (message?: string) => void
}

function Lock({
  balance,
  t,
  onLock,
  selectedAffiliateCode,
  selectedOffer,
  selectedOfferPriceFormatted,
  selectedOfferAffiliateWithdrawable,
  quantity,
  setErrorMessage,
}: LockProps) {
  const { wallet } = useWallet()

  const { glpSubscribeContract } = useGlpContracts(BSC_NETWORK)

  const subscribe = async () => {
    if (!wallet || wallet?.isProcessing || !glpSubscribeContract) return

    setErrorMessage(undefined)

    if (!selectedOffer) throw new Error('Offer not found')

    // remove "parameter" key
    const { parameter, ...affiliateCode } = selectedAffiliateCode || {}

    const tokenId = GlpTokenId['10SET']

    await wallet
      .interact(glpSubscribeContract)
      .subscribe(
        BigNumber.from(quantity),
        selectedOffer.id,
        tokenId,
        wallet.account!,
        affiliateCode
      )
      .then(onLock)
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)
      })
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-3 sm:gap-6">
        <div className="flex flex-col gap-1 w-full">
          <TextS isBold className="text-start">
            {t('subscription')}
          </TextS>

          <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
            <div className="p-3 text-neutral-100">
              {t('gem-launch-platform:tiers-privileges.tier', {
                tier: getTierIndex(selectedOfferPriceFormatted),
              })}{' '}
              (<NumberFormatter value={selectedOfferPriceFormatted} />)
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-1 w-40">
          <TextS isBold className="text-start">
            {t('subscribe.quantity')}
          </TextS>

          <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
            <div className="p-3 text-neutral-100">{quantity.toString()}</div>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-1">
        <div className="flex justify-between gap-2">
          <TextS isBold className="text-start">
            {t('subscribe.lock.you-lock')}
          </TextS>

          <TextS isBold>
            {t('balance')}: {cutDecimals(formatEther(balance))}
          </TextS>
        </div>

        <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
          <div className="p-3 text-neutral-100">
            <NumberFormatter
              value={selectedOfferPriceFormatted * quantity.toNumber()}
            />
          </div>

          <Text className="border-l-2 border-neutral-500 p-3 px-8" isBold>
            10set
          </Text>
        </div>
      </div>

      {selectedAffiliateCode && (
        <div className="flex flex-col gap-1">
          <div className="flex">
            <TextS isBold>{t('withdraw-info')}</TextS>
          </div>

          <div className="flex items-center justify-between gap-2 rounded border-2 border-neutral-500 bg-neutral-700">
            <div className="p-3 text-neutral-100">
              <NumberFormatter
                value={selectedOfferAffiliateWithdrawable * quantity.toNumber()}
              />
            </div>

            <Text className="border-l-2 border-neutral-500 p-3 px-8" isBold>
              10set
            </Text>
          </div>
        </div>
      )}

      <Button
        onClick={subscribe}
        loading={wallet?.isProcessing}
        disabled={wallet?.isProcessing}
      >
        {t('subscribe.lock.button')}
      </Button>
    </div>
  )
}
