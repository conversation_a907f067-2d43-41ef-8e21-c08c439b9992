import { AssetProps } from './index'

export default function ScAuditModel({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M40.1323 52.03V85.9007L68.092 102.07L96.0386 85.9007V52.03L68.092 35.8612L40.1323 52.03Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M64.8618 117.95C63.3056 117.95 62.0502 119.208 62.0502 120.767C62.0502 122.326 63.3056 123.584 64.8618 123.584C66.4181 123.584 67.6735 122.326 67.6735 120.767C67.6735 119.208 66.4181 117.95 64.8618 117.95Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M118.99 69.0636C117.433 69.0374 116.152 70.2822 116.126 71.8414C116.099 73.4007 117.342 74.6847 118.898 74.7109C120.454 74.7371 121.736 73.4924 121.762 71.9331C121.788 70.3739 120.546 69.0898 118.99 69.0636Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M94.9532 103.969H92.2853C91.9192 103.969 91.6315 104.258 91.6315 104.625C91.6315 104.991 91.9192 105.28 92.2853 105.28H94.9532C95.3193 105.28 95.607 104.991 95.607 104.625C95.607 104.258 95.3193 103.969 94.9532 103.969ZM94.9532 115.159H92.2853C91.9192 115.159 91.6315 115.447 91.6315 115.814C91.6315 116.181 91.9192 116.469 92.2853 116.469H94.9532C95.3193 116.469 95.607 116.181 95.607 115.814C95.607 115.447 95.3193 115.159 94.9532 115.159ZM6.65387 16.2464C7.02005 16.2464 7.30775 15.9581 7.30775 15.5912V7.31028H15.5727C15.9389 7.31028 16.2266 7.02202 16.2266 6.65514C16.2266 6.28826 15.9389 6 15.5727 6H6.65387C6.28771 6 6 6.28826 6 6.65514V15.5912C6 15.9581 6.28771 16.2464 6.65387 16.2464ZM123.292 6H114.373C114.007 6 113.719 6.28826 113.719 6.65514C113.719 7.02202 114.007 7.31028 114.373 7.31028H122.638V15.5912C122.638 15.9581 122.926 16.2464 123.292 16.2464C123.658 16.2464 123.946 15.9581 123.946 15.5912V6.65514C123.946 6.28826 123.658 6 123.292 6ZM15.5727 122.864H7.30775V114.583C7.30775 114.216 7.02005 113.928 6.65387 113.928C6.28771 113.928 6 114.216 6 114.583V123.519C6 123.886 6.28771 124.174 6.65387 124.174H15.5727C15.9389 124.174 16.2266 123.886 16.2266 123.519C16.2266 123.152 15.9389 122.864 15.5727 122.864ZM93.3185 83.3064C93.3185 83.3064 93.3839 83.2539 93.4231 83.2146C93.4492 83.1884 93.4754 83.1491 93.4885 83.1098C93.5146 83.0705 93.5277 83.0443 93.5408 83.005C93.5539 82.9657 93.5669 82.9133 93.5669 82.874C93.5669 82.8478 93.58 82.8216 93.58 82.7953V76.3226C102.08 75.0909 108.92 73.2303 113.092 70.9111C113.17 71.0029 113.235 71.1077 113.314 71.1994C113.955 71.8676 114.818 72.2476 115.746 72.2607C115.773 72.2607 115.786 72.2607 115.812 72.2607C116.714 72.2607 117.564 71.92 118.218 71.278C118.885 70.636 119.264 69.7712 119.277 68.8409C119.317 66.9279 117.786 65.3425 115.864 65.3031C114.909 65.3031 114.059 65.6307 113.392 66.2728C112.725 66.9148 112.346 67.7796 112.333 68.7099C112.333 69.0636 112.385 69.4043 112.49 69.7319C108.528 71.9463 101.871 73.7413 93.5669 74.9599V48.9116C93.5669 48.9116 93.5669 48.8854 93.5669 48.8723C93.5669 48.8461 93.5669 48.8199 93.5669 48.7806C93.5669 48.7543 93.5539 48.7281 93.5408 48.6888C93.5408 48.6626 93.5277 48.6364 93.5146 48.6102C93.5146 48.6102 93.5146 48.584 93.5146 48.5709C93.5146 48.5578 93.5016 48.5578 93.4885 48.5447C93.4754 48.5185 93.4492 48.5054 93.4362 48.4792C93.41 48.453 93.3969 48.4399 93.3708 48.4137C93.3446 48.4006 93.3316 48.3744 93.3054 48.3613C93.3054 48.3613 93.2923 48.3482 93.2792 48.3351L65.3195 32.1662C65.3195 32.1662 65.2672 32.1531 65.2411 32.14C65.2018 32.1269 65.1626 32.1007 65.1103 32.1007C65.0711 32.1007 65.0318 32.1007 64.9926 32.1007C64.9534 32.1007 64.9011 32.1007 64.8618 32.1007C64.8226 32.1007 64.7834 32.1269 64.7441 32.14C64.718 32.14 64.6918 32.14 64.6657 32.1662L54.6745 37.9446C56.9369 21.8675 61.0563 12.1321 64.9926 12.1321C67.752 12.1321 70.5375 16.7967 72.7476 24.999L71.0475 23.2826C70.799 23.0205 70.3805 23.0205 70.119 23.2826C69.8574 23.5315 69.8574 23.9508 70.119 24.2129L73.5061 27.6327C73.5061 27.6327 73.5453 27.6589 73.5584 27.672C73.5976 27.6982 73.6368 27.7244 73.6761 27.7506C73.7153 27.7637 73.7415 27.7768 73.7807 27.7899C73.8199 27.7899 73.8591 27.8161 73.9115 27.8161C73.9245 27.8161 73.9507 27.8161 73.9638 27.8161C74.0161 27.8161 74.0553 27.8161 74.1076 27.803C74.1076 27.803 74.1076 27.803 74.1207 27.803C74.2122 27.7768 74.2907 27.7244 74.3692 27.672C74.3953 27.6589 74.4084 27.6327 74.4215 27.6065C74.4738 27.5541 74.513 27.4885 74.5392 27.4099C74.5392 27.3968 74.5653 27.3837 74.5653 27.3575L75.9646 22.6405C76.0692 22.2998 75.8731 21.933 75.52 21.8281C75.1669 21.7233 74.8138 21.9199 74.7092 22.2736L73.9768 24.7239C72.1591 17.9235 69.2297 10.8349 64.9403 10.8349C59.4085 10.8349 55.3545 22.8895 53.2359 38.7569L36.6537 48.3482C36.6537 48.3482 36.6406 48.3613 36.6275 48.3744C36.6013 48.3875 36.5752 48.4137 36.5621 48.4268C36.536 48.453 36.5229 48.4661 36.4967 48.4923C36.4837 48.5185 36.4575 48.5316 36.4444 48.5578C36.4444 48.5578 36.4313 48.5709 36.4183 48.584C36.4183 48.584 36.4183 48.6102 36.4183 48.6233C36.4183 48.6495 36.3921 48.6757 36.3921 48.7019C36.3921 48.7281 36.379 48.7543 36.366 48.7937C36.366 48.8199 36.366 48.8461 36.366 48.8854C36.366 48.8985 36.366 48.9116 36.366 48.9247V74.5144C32.4819 73.8986 28.8725 73.1779 25.6032 72.3393C16.0043 69.876 10.5379 66.7313 10.5902 63.7177C10.6425 60.953 15.3373 58.2408 23.5762 56.1705L21.8369 57.8477C21.5753 58.0966 21.5622 58.5159 21.8107 58.778C21.9415 58.909 22.1115 58.9745 22.2815 58.9745C22.4384 58.9745 22.6084 58.909 22.7392 58.7911L26.2178 55.4499C26.2178 55.4499 26.2309 55.4237 26.244 55.4106C26.2963 55.3581 26.3355 55.2926 26.3617 55.214C26.3617 55.1878 26.3878 55.1616 26.3878 55.1354C26.414 55.0437 26.4271 54.9389 26.3878 54.834C26.3878 54.834 26.3878 54.834 26.3878 54.8078C26.3617 54.7161 26.3224 54.6375 26.257 54.5589C26.2309 54.5327 26.2178 54.5196 26.1917 54.4934C26.1393 54.4409 26.074 54.4016 26.0086 54.3754C25.9955 54.3754 25.9824 54.3492 25.9563 54.3492L21.2614 52.8555C20.9214 52.7507 20.5553 52.9341 20.4376 53.2879C20.3329 53.6286 20.516 53.9955 20.8691 54.1134L23.3015 54.8864C16.4882 56.5898 9.36092 59.4069 9.28245 63.6915C9.20399 68.474 17.9398 71.7235 25.2762 73.6103C28.6241 74.4751 32.3512 75.2088 36.3529 75.8378V82.7953C36.3529 82.7953 36.3529 82.8478 36.366 82.874C36.366 82.9264 36.366 82.9657 36.3921 83.005C36.4052 83.0443 36.4313 83.0836 36.4444 83.1098C36.4706 83.1491 36.4837 83.1753 36.5098 83.2146C36.536 83.2539 36.5752 83.2802 36.6144 83.3064C36.6406 83.3195 36.6537 83.3457 36.6798 83.3588L53.4452 93.0548C54.8314 102.698 56.9499 110.429 59.5654 114.963C58.7808 115.605 58.2708 116.561 58.2708 117.649C58.2708 119.562 59.827 121.121 61.7363 121.121C63.6456 121.121 65.2018 119.562 65.2018 117.649C65.2018 115.736 63.6456 114.176 61.7363 114.176C61.3832 114.176 61.0432 114.242 60.7163 114.347C58.2969 110.18 56.2961 102.921 54.9229 93.9065L64.6264 99.5145H64.6526C64.7441 99.5538 64.8357 99.5931 64.9403 99.5931C65.0449 99.5931 65.1365 99.5669 65.228 99.5145H65.2542L93.2139 83.3457C93.2139 83.3457 93.2531 83.3064 93.2792 83.2933L93.3185 83.3064ZM115.825 66.6134C115.825 66.6134 115.851 66.6134 115.864 66.6134C116.439 66.6134 116.976 66.8624 117.381 67.2686C117.786 67.6879 117.996 68.2382 117.983 68.8147C117.956 69.9939 117.002 70.9504 115.825 70.9504C115.825 70.9504 115.799 70.9504 115.786 70.9504C115.21 70.9504 114.674 70.7015 114.269 70.2953C113.863 69.876 113.654 69.3257 113.667 68.7492C113.693 67.5699 114.648 66.6134 115.825 66.6134ZM61.7494 115.5C62.9394 115.5 63.9072 116.469 63.9072 117.662C63.9072 118.854 62.9394 119.824 61.7494 119.824C60.5593 119.824 59.5916 118.854 59.5916 117.662C59.5916 116.469 60.5593 115.5 61.7494 115.5ZM53.0659 40.4079C52.4251 45.5704 51.9936 51.0997 51.7843 56.7208L38.3276 48.9378L53.0659 40.4079ZM37.6737 50.0646L51.732 58.1883C51.6536 60.4813 51.6143 62.7874 51.6143 65.0935C51.6143 68.8933 51.7189 72.6145 51.8889 76.2178C46.8933 75.8771 42.1201 75.3661 37.6737 74.7109V50.0515V50.0646ZM37.6737 82.4154V76.0474C42.1462 76.7026 46.9457 77.2136 51.9543 77.5542C52.229 82.4547 52.6605 87.1062 53.2359 91.4301L37.6737 82.4285V82.4154ZM64.3257 97.8242L54.7006 92.2556C54.0598 87.722 53.589 82.7953 53.2882 77.6198C56.5184 77.8163 59.827 77.9473 63.1879 78.0128C63.5672 78.0128 63.9464 78.0128 64.3257 78.0128V97.8111V97.8242ZM64.3257 76.7157C63.9595 76.7157 63.5933 76.7157 63.2141 76.7157C59.8139 76.6501 56.4792 76.5191 53.2098 76.3226C53.0267 72.68 52.9221 68.9195 52.9221 65.1066C52.9221 62.9971 52.9482 60.953 53.0005 58.9352L64.3126 65.4866V76.7288L64.3257 76.7157ZM64.9795 64.3335L53.0528 57.4415C53.2752 50.9163 53.759 44.9414 54.4391 39.6086L64.9664 33.5158L91.6184 48.9247L64.9664 64.3335H64.9795ZM92.2853 82.4154L65.6334 97.8242V78.039C66.1565 78.039 66.6927 78.0521 67.2158 78.0521C76.3177 78.0521 84.8835 77.5018 92.2853 76.506V82.4154ZM92.2853 75.1433C84.4389 76.2178 75.2715 76.7812 65.6334 76.7288V65.4735L92.2853 50.0646V75.1564V75.1433ZM115.786 103.969H98.7195C98.3533 103.969 98.0656 104.258 98.0656 104.625C98.0656 104.991 98.3533 105.28 98.7195 105.28H115.786C116.152 105.28 116.439 104.991 116.439 104.625C116.439 104.258 116.152 103.969 115.786 103.969ZM115.786 109.564H98.7195C98.3533 109.564 98.0656 109.853 98.0656 110.219C98.0656 110.586 98.3533 110.875 98.7195 110.875H115.786C116.152 110.875 116.439 110.586 116.439 110.219C116.439 109.853 116.152 109.564 115.786 109.564ZM116.439 115.814C116.439 115.447 116.152 115.159 115.786 115.159H98.7195C98.3533 115.159 98.0656 115.447 98.0656 115.814C98.0656 116.181 98.3533 116.469 98.7195 116.469H115.786C116.152 116.469 116.439 116.181 116.439 115.814ZM123.292 113.941C122.926 113.941 122.638 114.229 122.638 114.596V122.877H114.373C114.007 122.877 113.719 123.165 113.719 123.532C113.719 123.899 114.007 124.187 114.373 124.187H123.292C123.658 124.187 123.946 123.899 123.946 123.532V114.596C123.946 114.229 123.658 113.941 123.292 113.941ZM94.9532 109.564H92.2853C91.9192 109.564 91.6315 109.853 91.6315 110.219C91.6315 110.586 91.9192 110.875 92.2853 110.875H94.9532C95.3193 110.875 95.607 110.586 95.607 110.219C95.607 109.853 95.3193 109.564 94.9532 109.564Z"
        fill={color ?? 'url(#paint0_linear_940_1177)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_940_1177"
          x1="24.8185"
          y1="134.774"
          x2="105.373"
          y2="-4.46546"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
