import { Meta } from '@storybook/react'

import { TensetLogoWithLabelMono } from '../../assets/icons'

import { applyElements, communityElements, legalElements } from './elements'

import { Footer, ListItem } from '.'

type FooterType = typeof Footer

const lists: ListItem[] = [
  {
    title: 'Community',
    elements: communityElements,
  },
  {
    title: 'Apply',
    elements: applyElements,
  },
  {
    title: 'Legal',
    elements: legalElements,
  },
]

export default {
  title: 'Components/Footer',
  component: Footer,
  args: {
    icon: <TensetLogoWithLabelMono />,
  },
} as Meta<FooterType>

export const Preview = {
  args: { lists },
}

export const TwoCols = {
  args: { lists: lists.slice(0, 2) },
}

export const FiveCols = {
  args: { lists: [...lists, ...lists.slice(0, 2)] },
}
