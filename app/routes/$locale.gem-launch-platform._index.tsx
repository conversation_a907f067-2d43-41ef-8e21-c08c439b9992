import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import { useTranslation } from 'react-i18next'

import { getGemsList } from '~/api/fetchers'
import { GemStatus } from '~/api/types'
import IsYourProjectNextGemBlock from '~/components/blocks/is-your-project-next-gem'
import { GemsWithHeader } from '~/components/gems-with-header'
import { BSC_NETWORK } from '~/config'
import { i18next, Namespace } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import {
  GlpAccessTheLaunchpad,
  GlpHero,
  GlpHowToParticipate,
  GlpPreviouslyLaunched,
  GlpTiersPrivileges,
  GlpWhatIsUnique,
} from '~/sections/gem-launch-platform'
import { FrequentlyAskedQuestions } from '~/sections/gem-launch-platform/faq'
import {
  chains,
  config,
  GlpLifetimeMembershipContract,
  GlpMembershipContract,
} from '~/tenset-web3'
import { withCache } from '~/utils'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.GEM_LAUNCH_PLATFORM)

  const title = t('meta.title')
  const description = t('meta.description')

  const { rpcUrls } = chains[BSC_NETWORK]

  const glpMembershipContract = new GlpMembershipContract(
    config.gemLaunchPlatform.membership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const glpLifetimeMembershipContract = new GlpLifetimeMembershipContract(
    config.gemLaunchPlatform.lifetimeMembership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const [gems, lifetimeMembershipTotalSupply, rpcLockedAmount] =
    await Promise.all([
      getGemsList(1, 100).then(gems => gems || []),
      glpLifetimeMembershipContract.totalSupply(),
      glpMembershipContract.lockedAmount(),
    ])

  return withCache(
    json({
      title,
      description,
      rpcLockedAmount,
      lifetimeMembershipTotalSupply,
      gems,
    })
  )
}

export type GlpLoaderData = typeof loader

export const handle = {
  i18n: [Namespace.GEM_LAUNCH_PLATFORM, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function GemLaunchPlatform() {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM,
    Namespace.COMMON,
  ])

  const { gems } = useLoaderData<typeof loader>()

  const currentGems = gems.filter(gem => gem.status === GemStatus.CURRENT)
  const futureGems = gems.filter(gem => gem.status === GemStatus.FUTURE)
  const previousGems = gems
    .filter(gem => gem.status === 'previous')
    .sort((a, b) => {
      const launchDateA = a.launchData.find(({ key }) => key === 'launchDate')
      const launchDateB = b.launchData.find(({ key }) => key === 'launchDate')

      if (
        launchDateA?.data === undefined ||
        Number.isNaN(new Date(launchDateA?.data).getTime())
      )
        return -1
      if (
        launchDateB?.data === undefined ||
        Number.isNaN(new Date(launchDateB?.data).getTime())
      )
        return 1
      return (
        new Date(launchDateB.data).getTime() -
        new Date(launchDateA.data).getTime()
      )
    })

  return (
    <div className="container">
      <GlpHero />
      <GlpWhatIsUnique />
      <GlpHowToParticipate />
      <GlpTiersPrivileges />
      <GlpAccessTheLaunchpad />

      {currentGems.length > 0 && (
        <GemsWithHeader
          header={t('gem-launch-platform:current-gems')}
          gems={currentGems}
        />
      )}

      {futureGems.length > 0 && (
        <GemsWithHeader
          header={t('gem-launch-platform:future-launches')}
          gems={futureGems}
        />
      )}

      {previousGems.length > 0 && <GlpPreviouslyLaunched gems={previousGems} />}

      <FrequentlyAskedQuestions />
      <IsYourProjectNextGemBlock />
    </div>
  )
}
