import { ethers } from 'ethers'

import { BSC_NETWORK } from '~/config'
import { chains, config, infinityVaultAbi } from '~/tenset-web3'

const acceptedPacakes = new Set([
  '6_months',
  '9_months',
  '12_months',
  '24_months',
])

export async function hasPremiumPackage(address: string) {
  const { rpcUrls } = chains[BSC_NETWORK]

  const provider = new ethers.providers.JsonRpcProvider(rpcUrls[0])

  const infinityVaultContract = new ethers.Contract(
    config.infinity.vault[BSC_NETWORK]!,
    infinityVaultAbi,
    provider
  )

  const deposits = await infinityVaultContract.getDepositsByAddress(address)

  if (deposits.length > 0) {
    const walletPackages = await Promise.all(
      deposits.map(async (deposit: number) => {
        const { packageKey, withdrawn } =
          await infinityVaultContract.getDepositDetails(deposit)

        return acceptedPacakes.has(packageKey) && !withdrawn
      })
    )

    return walletPackages.some(Boolean)
  }

  return false
}

export async function hasLockedPackage(address: string) {
  const { rpcUrls } = chains[BSC_NETWORK]

  const provider = new ethers.providers.JsonRpcProvider(rpcUrls[0])

  const infinityVaultContract = new ethers.Contract(
    config.infinity.vault[BSC_NETWORK]!,
    infinityVaultAbi,
    provider
  )

  const deposits = await infinityVaultContract.getDepositsByAddress(address)

  if (deposits.length > 0) {
    const walletPackages = await Promise.all(
      deposits.map(async (deposit: number) => {
        const { withdrawn, unlockTime } =
          await infinityVaultContract.getDepositDetails(deposit)

        const isNotExpired = unlockTime > Date.now() / 1000
        return !withdrawn && isNotExpired
      })
    )

    return walletPackages.some(Boolean)
  }

  return false
}
