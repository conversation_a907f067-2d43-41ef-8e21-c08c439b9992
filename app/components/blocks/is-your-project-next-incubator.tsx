import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import { H2, TextL } from '~/tenset-components'
import { LaunchYourProjectButton } from '~/components/utils'

export default function IsYourProjectNextIncubatorBlock() {
  const { t } = useTranslation(Namespace.COMMON)

  return (
    <div className="flex flex-col gap-6 md:items-center md:text-center">
      <div className="flex flex-col gap-4">
        <header>
          <H2 isBold>{t('common:is-your-project-next-incubator.title')}</H2>
        </header>

        <main className="max-w-[800px]">
          <TextL>
            {t('common:is-your-project-next-incubator.description')}
          </TextL>
        </main>
      </div>

      <LaunchYourProjectButton />
    </div>
  )
}
