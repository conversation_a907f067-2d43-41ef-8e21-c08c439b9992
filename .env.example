PORT="3000"
NODE_ENV="development"

AGREEMENTS_DATABASE_URL="postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=require"
TELEGRAM_BOT_DATABASE_URL="postgresql://postgres:test@localhost:5432/postgres?sslmode=disable"
TELEGRAM_BOT_TOKEN="123456789"
TELEGRAM_BOT_CHAT_ID="123456789"
DATA_TENSET_URL="https://data.tenset.io/api/"
DATA2_TENSET_URL="https://api2.tenset.io/v1/"
DATABASE_URL="postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=require"
STRAPI_URL="https://s.tenset.io"
STRAPI_TOKEN="123456789"

RECAPTCHA_SECRET="6LcAoXklAAAAABbnyvkLAkY5m6aJlPIPSrgqXaPn"
RECAPTCHA_PUBLIC="6LcAoXklAAAAAPfTpsNBMcbC7dlh_hGQ-h6jXbnJ"
RECAPTCHA_URL="https://www.google.com/recaptcha/api/siteverify"

SENTRY_ENABLED="false"
SENTRY_DSN=
SENTRY_AUTH_TOKEN=
SENTRY_ORG=
SENTRY_PROJECT=
SENTRY_ENVIRONMENT="development-local"

FIREBASE_API_HOST="https://us-central1-tenset-voting.cloudfunctions.net/api"
FIREBASE_MEASUREMENT_ID="G-DFSRBDEZHC"
FIREBASE_APP_ID="1:202936261286:web:7a976592fdd0068c28f267"
FIREBASE_MESSAGING_SENDER_ID="202936261286"
FIREBASE_STORAGE_BUCKET="storageBucket"
FIREBASE_PROJECT_ID="tenset-voting"
FIREBASE_DATABASE_URL="https://tenset-voting-default-rtdb.europe-west1.firebasedatabase.app"
FIREBASE_AUTH_DOMAIN="tenset-voting.firebaseapp.com"
FIREBASE_API_KEY="AIzaSyCmAn9mtFYyd3-aUBcUs7Y2D4JGlVjr6Aw"

LOCIZE_PROJECTID="f26aa4f0-ce3f-49c2-a675-13bca31a4e66"
LOCIZE_API_KEY="3ce18561-9df2-41fe-9f8c-dd0f8cbc896a"

CRISP_WEBSITE_ID="e0e80764-f419-409a-9680-ba6282df7b64"
