import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import { H2, Stepper, Text } from '~/tenset-components'
import { participateSteps } from '~/data/gem-launch-platform'

export function GlpHowToParticipate() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])

  return (
    <div className="flex flex-col gap-12">
      <H2 isBold>{t('how-to-participate.title')}</H2>

      <Stepper
        steps={participateSteps.map(({ title, content }) => {
          return {
            title: t(title),
            content: <Text>{t(content)}</Text>,
          }
        })}
      />
    </div>
  )
}
