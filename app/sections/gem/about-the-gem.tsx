import type { GemAttributesType } from '~/api/types'
import type { IconName } from '~/tenset-components'
import { H2, H3, But<PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Icon } from '~/tenset-components'
import { renderMarkdown } from '~/utils'

type GemDetailsAboutTheGemSectionProps = Pick<
  GemAttributesType,
  'aboutTheGemTitle' | 'sidebar' | 'about'
>

export function GemsDetailsAboutTheGemSection({
  aboutTheGemTitle,
  about,
  sidebar,
}: GemDetailsAboutTheGemSectionProps) {
  return (
    <section className="flex flex-col gap-8 md:gap-12">
      <header>
        <H2 isBold>{aboutTheGemTitle}</H2>
      </header>

      <div className="flex flex-col-reverse gap-8 lg:grid lg:grid-cols-[3fr,1fr]">
        {about && (
          <main
            className="prose flex flex-col lg:mr-[240px] break-all text-base text-white prose-headings:text-2xl prose-headings:font-medium prose-headings:text-white prose-p:mt-0 prose-p:break-normal prose-p:text-white prose-a:break-words prose-a:text-white prose-strong:break-normal prose-strong:text-white prose-em:break-normal prose-em:text-white prose-li:break-normal prose-li:text-white prose-th:mt-0 prose-th:break-normal prose-th:text-center prose-th:text-base prose-th:text-white prose-img:cursor-pointer md:prose-headings:text-4xl md:prose-p:text-base md:prose-a:text-base md:prose-strong:text-base md:prose-em:text-base md:prose-li:text-base md:prose-th:text-base"
            dangerouslySetInnerHTML={{
              __html: renderMarkdown(about) as string,
            }}
          ></main>
        )}

        {sidebar && (
          <div className="flex flex-col gap-8 md:gap-12 lg:border-l lg:border-neutral-400 lg:px-12">
            {sidebar.map(({ title, items }) => (
              <div key={title} className="flex flex-col gap-4 md:gap-6">
                <H3 isBold>{title}</H3>

                <div className="flex flex-col gap-4 md:gap-6">
                  {items.map(({ prefixIcon, suffixIcon, label, link }) => (
                    <Button
                      variant={ButtonVariant.Secondary}
                      to={link}
                      key={label}
                    >
                      {prefixIcon && <Icon name={prefixIcon as IconName} />}
                      {label}
                      {suffixIcon && <Icon name={suffixIcon as IconName} />}
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
