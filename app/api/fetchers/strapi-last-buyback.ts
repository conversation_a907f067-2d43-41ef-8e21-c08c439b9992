import type { PostsPaginationType } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function fetchStrapiLastBuyback(): Promise<PostsPaginationType> {
  const STRAPI_POSTS_URL = `${getServerEnvironment().STRAPI_URL}/api/posts?`

  const latestBuybackSearchParameters = decodeURIComponent(
    new URLSearchParams({
      populate: 'cover',
      'filters[isBuyback][$eq]': 'true',
      'sort[0]': 'date:desc',
      'pagination[limit]': '1',
    }).toString()
  )

  return await fetch(
    STRAPI_POSTS_URL + latestBuybackSearchParameters
  ).then<PostsPaginationType>(response => response.json())
}
