import { useTranslation } from 'react-i18next'

import { SocialButton } from '~/components/governance/social-button'
import { Namespace } from '~/i18n'
import { H3, Icon, IconName, Text } from '~/tenset-components'

interface GovernanceSocialsProps {
  svgsIdPrefix?: string
}

export function GovernanceSocials({ svgsIdPrefix }: GovernanceSocialsProps) {
  const { t } = useTranslation(Namespace.GOVERNANCE)

  return (
    <div className="flex flex-col gap-4 lg:mx-auto lg:max-w-[311px]">
      <div className="flex flex-col gap-2 lg:text-center xl:text-left">
        <H3 isBold>{t('socials.title')}</H3>

        <Text>{t('socials.description')}</Text>
      </div>

      <SocialButton
        href="https://twitter.com/TenseT_io"
        buttonContent={
          <>
            <Icon name={IconName.Twitter} />
            {t('socials.x.label')}
          </>
        }
        underButtonContent={t('socials.x.count')}
      />

      <SocialButton
        href="https://t.me/tenset_io_eng"
        buttonContent={
          <>
            <Icon name={IconName.Telegram} idPrefix={svgsIdPrefix} />
            {t('socials.telegram.label')}
          </>
        }
      />

      <SocialButton
        href="https://discord.gg/PRavwRHubW"
        buttonContent={
          <>
            <Icon name={IconName.Discord} idPrefix={svgsIdPrefix} />
            {t('socials.discord.label')}
          </>
        }
      />

      <SocialButton
        href="https://open.kakao.com/o/g8S5EQkh"
        buttonContent={
          <>
            <Icon name={IconName.KakaoTalk} idPrefix={svgsIdPrefix} />
            {t('socials.kakao.label')}
          </>
        }
      />

      <SocialButton
        href="https://line.me/ti/g2/QBYVIHDNVY_InQzC-r7kjw"
        buttonContent={
          <>
            <Icon name={IconName.LineMono} idPrefix={svgsIdPrefix} />
            {t('socials.line.label')}
          </>
        }
      />
    </div>
  )
}
