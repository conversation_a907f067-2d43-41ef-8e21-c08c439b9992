import { AssetProps } from './index'

export default function WhaleHunt({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M63.5418 14.6413C63.7224 14.6863 63.9143 14.7089 64.0949 14.7089C65.156 14.7089 66.1268 13.9763 66.3865 12.9056C66.6912 11.6432 65.9123 10.3697 64.648 10.0653C63.3837 9.76103 62.1081 10.55 61.8034 11.801C61.4986 13.0633 62.2775 14.3369 63.5418 14.6413ZM62.887 12.0715C63.0225 11.508 63.5305 11.1248 64.0836 11.1248C64.1739 11.1248 64.2755 11.1248 64.3658 11.1586C65.0206 11.3164 65.4382 11.9814 65.2802 12.6351C65.1222 13.2888 64.4448 13.6945 63.8014 13.5367C63.1467 13.3789 62.729 12.714 62.887 12.0603V12.0715ZM73.4643 48.4647V49.4115C73.4643 49.727 73.7127 49.975 74.0287 49.975C74.3448 49.975 74.5932 49.727 74.5932 49.4115V48.4647C75.7107 48.3745 76.7718 48.0477 77.5846 47.4954C78.6005 46.7966 79.165 45.8612 79.165 44.8355C79.165 42.8181 76.8847 41.6121 74.5819 41.2514V36.8108C75.6204 36.946 76.5235 37.4081 76.9524 38.1182C77.1105 38.3887 77.4604 38.4676 77.7313 38.3098C78.0023 38.152 78.0813 37.8026 77.9232 37.5321C77.3024 36.5065 76.0268 35.8077 74.5819 35.6612V34.8159C74.5819 34.5003 74.3335 34.2523 74.0174 34.2523C73.7014 34.2523 73.453 34.5003 73.453 34.8159V35.6724C72.5499 35.7626 71.7146 36.0669 71.0486 36.5516C70.1907 37.1827 69.7278 38.0393 69.7278 38.9635C69.7278 40.609 71.1502 41.815 73.4643 42.2095V47.3376C72.1661 47.2136 71.0147 46.6727 70.439 45.8837C70.2471 45.6358 69.8972 45.5794 69.6488 45.7597C69.4005 45.9401 69.344 46.3007 69.5247 46.5487C70.3148 47.6194 71.7936 48.3295 73.4643 48.4647ZM78.0361 44.8243C78.0361 45.4554 77.6523 46.0753 76.9524 46.5487C76.309 46.9882 75.4849 47.2475 74.5932 47.3264V42.3785C76.388 42.6828 78.0361 43.5619 78.0361 44.8243ZM70.8567 38.9635C70.8567 38.4112 71.1615 37.8702 71.7259 37.4532C72.2 37.1038 72.7983 36.8784 73.4643 36.7882V41.0711C72.121 40.8006 70.868 40.1469 70.868 38.9635H70.8567ZM74.2884 54.6411H74.2997C77.6523 54.6411 80.8018 53.3337 83.1724 50.9555C85.5429 48.5887 86.8411 45.4329 86.8411 42.0855C86.8411 35.1765 81.1969 29.5637 74.2771 29.5637H74.2658C70.9131 29.5637 67.7637 30.8711 65.3931 33.2492C63.0225 35.6161 61.7243 38.7719 61.7243 42.1193C61.7243 49.0282 67.3686 54.6411 74.2884 54.6411ZM66.1946 34.0382C68.3506 31.8855 71.2179 30.6908 74.2771 30.6908H74.2884C80.5873 30.6908 85.7123 35.7964 85.7235 42.0855C85.7235 45.1286 84.5383 48.0026 82.3822 50.1553C80.2261 52.3193 77.3588 53.5027 74.2997 53.5027H74.2884C67.9894 53.5027 62.8645 48.3858 62.8532 42.0967C62.8532 39.0536 64.0385 36.1796 66.1946 34.0269V34.0382ZM91.6161 29.6426C91.966 29.7215 92.316 29.7666 92.6772 29.7666C93.5013 29.7666 94.3028 29.5412 95.0252 29.1016C96.0525 28.4704 96.7636 27.4899 97.0458 26.3177C97.3281 25.1456 97.1361 23.9396 96.504 22.9252C95.8718 21.9109 94.8897 21.1895 93.7158 20.9078C92.5418 20.626 91.3339 20.8176 90.3179 21.4488C89.302 22.0799 88.5795 23.0605 88.2973 24.2326C88.0151 25.4048 88.207 26.6108 88.8391 27.6251C89.4713 28.6508 90.4534 29.3608 91.6274 29.6426H91.6161ZM89.3923 24.4919C89.6068 23.6128 90.1373 22.8802 90.9049 22.4068C91.4468 22.0799 92.0451 21.9109 92.6659 21.9109C92.9256 21.9109 93.1965 21.9447 93.4561 22.001C94.3366 22.2152 95.0704 22.7449 95.5445 23.5113C96.0186 24.2777 96.1541 25.1794 95.9509 26.0585C95.7364 26.9376 95.2058 27.6702 94.4382 28.1436C93.6706 28.617 92.7675 28.7522 91.887 28.5493C90.0809 28.121 88.9633 26.2952 89.3923 24.4919ZM34.7224 76.0104C32.9614 76.0104 31.5391 77.4417 31.5391 79.1887C31.5391 80.9357 32.9727 82.3783 34.7224 82.3783C36.4721 82.3783 37.9058 80.9469 37.9058 79.1887C37.9058 77.4305 36.4721 76.0104 34.7224 76.0104ZM34.7224 81.2512C33.5823 81.2512 32.6679 80.3271 32.6679 79.1887C32.6679 78.0504 33.5936 77.1374 34.7224 77.1374C35.8513 77.1374 36.7769 78.0616 36.7769 79.1887C36.7769 80.3158 35.8513 81.2512 34.7224 81.2512ZM117.929 86.4358C117.817 86.2329 117.579 86.1202 117.354 86.154C113.978 86.6499 111.924 84.3732 109.926 82.1754C107.713 79.741 105.433 77.2163 101.697 79.4367C100.647 74.872 97.5764 73.4068 94.8446 72.0994C91.9096 70.7018 89.5842 69.5861 89.7761 65.1679C89.7761 64.9087 89.618 64.672 89.3697 64.6044C89.1214 64.5368 88.8504 64.6382 88.7263 64.8523C82.7434 74.8946 87.3039 85.8835 93.9189 93.2771C80.1358 90.8539 72.787 81.0033 66.9283 70.6117C61.1486 60.3553 53.2016 54.9116 41.9131 53.4689C33.6049 52.4207 24.0323 56.1739 18.1171 62.8349C14.2 67.2417 12.1343 72.6179 12.0214 78.4223C11.9988 78.5125 11.9875 78.6026 12.0214 78.7041C11.9988 81.0033 12.281 83.3701 12.8793 85.7595C15.6562 96.8274 27.0463 105.438 43.3919 108.819C47.5912 116.946 52.8968 118.873 57.6492 119.989C57.6944 119.989 57.7395 120 57.7734 120C57.9201 120 58.0669 119.944 58.1798 119.831C58.3152 119.684 58.3717 119.482 58.3152 119.29C57.1525 114.894 56.5655 112.325 56.1366 110.251C56.9606 110.273 57.796 110.307 58.6087 110.307C75.3156 110.307 89.3133 104.458 95.7251 94.6972C97.599 94.9339 99.3261 95.0579 100.906 95.0579C109.17 95.0579 113.685 91.9472 117.862 87.0669C118.02 86.8866 118.042 86.6386 117.929 86.4245V86.4358ZM13.9743 85.5003C13.3873 83.1786 13.1389 81.0258 13.1276 79.0197C15.4756 78.5012 16.5029 79.8311 18.5574 82.9757C20.2168 85.5228 22.497 88.9943 26.8205 92.2628C28.7057 93.6716 30.557 94.6183 32.3631 95.5313C36.1673 97.4586 39.4523 99.1379 41.518 104.627C41.9131 105.675 42.3308 106.622 42.7485 107.535C27.3172 104.12 16.5819 95.9483 13.963 85.5003H13.9743ZM55.9108 109.135C55.8544 108.831 55.7866 108.538 55.7302 108.233L55.4931 107.061C55.1319 105.28 55.2448 104.198 55.8318 103.533C56.4639 102.823 57.7734 102.542 59.7376 102.699C64.2868 103.06 80.8357 101.956 93.6142 95.7116C86.5815 104.672 72.4709 109.777 55.9108 109.135ZM59.8279 101.584C57.4686 101.392 55.8882 101.787 54.9964 102.79C53.9127 104.007 54.1046 105.833 54.3981 107.287L54.6352 108.459C55.1093 110.871 55.5947 113.362 56.9945 118.681C52.0276 117.396 46.564 114.793 42.5904 104.232C40.3666 98.3264 36.7318 96.4893 32.8824 94.5282C31.1214 93.6378 29.304 92.7136 27.5091 91.3611C23.3324 88.2166 21.2102 84.9706 19.5056 82.3671C17.6091 79.4705 16.1868 77.3178 13.1389 77.8588C13.3986 71.4007 16.2207 66.6895 18.9638 63.6013C24.6531 57.2108 33.8193 53.5929 41.7777 54.6073C52.671 55.9936 60.3584 61.257 65.9462 71.1752C71.6582 81.2963 79.4246 91.8683 93.727 94.4042C81.0953 100.817 64.3658 101.944 59.8392 101.584H59.8279ZM95.7251 93.5814C89.2907 86.9204 84.3576 76.7317 88.7601 67.2192C89.3358 70.7244 91.887 71.9416 94.3592 73.1138C97.1362 74.4324 100.003 75.8075 100.748 80.451C100.782 80.6426 100.906 80.8117 101.087 80.8793C101.268 80.9469 101.471 80.9244 101.629 80.8117C104.982 78.3885 106.697 80.282 109.079 82.908C110.908 84.9368 112.963 87.1909 116.112 87.3262C111.574 92.2515 106.325 94.9339 95.7251 93.5589V93.5814Z"
        fill="url(#paint0_linear_2185_45)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_45"
          x1="45.0852"
          y1="114.072"
          x2="97.6218"
          y2="22.9327"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
