import { AssetProps } from '.'

export default function BurnTokens({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2010_20)">
        <path
          d="M90.2889 83.1915C107.317 74.9531 104.573 53.1448 100.731 49.99C100.292 67.1831 91.5924 69.2358 82.9478 71.9911V66.1361L86.4331 62.6231C87.7367 61.3006 87.7367 59.179 86.4331 57.8564L68.7323 40.0847C67.415 38.7621 65.3019 38.7621 63.9846 40.0847L46.2838 57.8564C44.9803 59.1652 44.9666 61.3006 46.2838 62.6231L49.7691 66.1361V94.9704L46.2838 98.4834C44.9803 99.806 44.9803 101.928 46.2838 103.25L63.9846 121.022C65.3019 122.344 67.415 122.344 68.7323 121.022L75.2912 114.437L75.5108 113.252C75.4696 113.651 75.4147 114.051 75.3324 114.464C86.2684 116.572 84.814 107.259 88.0934 104.407C91.318 101.611 95.6403 101.253 99.9626 96.6236C105.534 90.6721 105.822 84.6656 103.475 76.7441C102.131 81.3868 97.2731 85.7677 90.2889 83.1915ZM66.3722 101.735L60.6915 96.0312C60.0603 95.3975 59.7035 94.5434 59.7035 93.6479V67.4724C59.7035 66.577 60.0603 65.7228 60.6915 65.0891L66.3722 59.3856L72.0529 65.0891C72.6841 65.7228 73.0409 66.577 73.0409 67.4724V76.7303C70.8591 78.535 68.9793 80.9459 67.6483 84.4589C65.1921 90.9201 66.6741 95.3286 68.9793 99.1172L66.3859 101.735H66.3722Z"
          fill="#15121A"
        />
        <path
          d="M105.012 32.9071L88.903 16.7334C87.5857 15.4109 85.4726 15.4109 84.1553 16.7334L82.0696 18.8275C81.4384 19.4612 80.5877 19.8194 79.6958 19.8194H53.0211C52.1292 19.8194 51.2785 19.4612 50.6473 18.8275L48.5616 16.7334C47.2443 15.4109 45.1312 15.4109 43.8139 16.7334L27.7185 32.9071C26.4013 34.2296 26.4013 36.3512 27.7185 37.6738L33.7697 43.7492C35.087 45.0718 37.2001 45.0718 38.5174 43.7492L51.4431 30.7717C52.0743 30.138 52.925 29.7798 53.8169 29.7798H78.9137C79.8056 29.7798 80.6563 30.138 81.2875 30.7717L90.6593 40.1811C90.4535 40.4015 90.2751 40.6495 90.0281 40.8562C86.1312 44.2727 85.8979 52.1805 94.1446 56.5201C91.5649 49.7145 92.498 45.981 94.0074 43.5564L94.2132 43.763C95.5305 45.0856 97.6436 45.0856 98.9609 43.763L105.012 37.6875C106.329 36.365 106.329 34.2434 105.012 32.9209V32.9071Z"
          fill="#15121A"
        />
        <path
          d="M101.417 73.9887C101.115 73.9887 100.841 74.1953 100.758 74.4846C100.017 77.0057 98.2747 79.1824 96.0655 80.2983C94.2817 81.2076 92.2235 81.428 90.0555 80.9458C99.1117 75.7934 101.458 67.0177 102.048 62.0995C102.871 55.1837 101.17 49.0807 99.1117 47.3999C98.9059 47.2346 98.6314 47.1933 98.3982 47.3035C98.1649 47.4137 98.0002 47.6479 98.0002 47.9097C97.5886 63.9456 89.8771 66.384 81.699 68.9464C81.6579 68.9464 81.6304 68.974 81.5893 68.974V64.3451L84.8824 61.0387C86.4604 59.4544 86.4604 56.8782 84.8824 55.2939L67.1816 37.5221C65.6036 35.9378 63.0377 35.9378 61.4597 37.5221L43.7589 55.2939C42.1809 56.8782 42.1809 59.4544 43.7589 61.0387L47.0521 64.3451V92.6145L43.7589 95.9209C42.1809 97.5052 42.1809 100.081 43.7589 101.666L61.4597 119.437C62.2419 120.223 63.2847 120.622 64.3138 120.622C65.3429 120.622 66.3858 120.223 67.1679 119.437L73.4524 113.128C74.4678 113.307 75.4008 113.403 76.2241 113.403C81.8225 113.403 83.3044 109.587 84.5257 106.446C85.0882 105 85.6234 103.622 86.4741 102.878C87.7365 101.79 89.2184 101.06 90.7827 100.302C93.1154 99.1584 95.7636 97.8634 98.3982 95.053C103.667 89.4184 104.724 83.4669 102.076 74.4984C101.993 74.2091 101.719 74.0024 101.417 74.0024V73.9887ZM72.6428 111.998L66.1937 118.473C65.192 119.479 63.4219 119.479 62.4202 118.473L44.7194 100.701C43.6766 99.6544 43.6766 97.9598 44.7194 96.9128L48.2184 93.3998C48.3419 93.2758 48.4243 93.0967 48.4243 92.9176V64.0833C48.4243 63.9042 48.3556 63.7251 48.2184 63.5874L44.7332 60.0743C43.6903 59.0273 43.6903 57.3328 44.7332 56.2858L62.434 38.5141C63.4356 37.5084 65.2057 37.5084 66.2074 38.5141L83.9082 56.2858C84.951 57.3328 84.951 59.0273 83.9082 60.0743L80.4092 63.5874C80.2857 63.7114 80.2034 63.8904 80.2034 64.0695V69.4286C77.2395 70.3792 74.2757 71.4675 71.6548 73.286V65.4059C71.6548 64.3175 71.2295 63.2981 70.4748 62.5403L64.7941 56.8369C64.6706 56.7129 64.4922 56.6302 64.3138 56.6302C64.1354 56.6302 63.9571 56.6991 63.8336 56.8369L58.1528 62.5403C57.3844 63.3118 56.9728 64.3313 56.9728 65.4059V91.5813C56.9728 92.6696 57.3982 93.6891 58.1528 94.4468L63.8336 100.15C63.9708 100.288 64.1492 100.357 64.3138 100.357C64.4785 100.357 64.6706 100.288 64.7941 100.15L66.7837 98.1527C67.3874 99.0895 68.0324 99.9988 68.6635 100.88C71.1334 104.338 73.4661 107.603 72.6428 111.998ZM66.0565 96.9404L64.3138 98.69L59.1271 93.4825C58.6194 92.9727 58.3449 92.2977 58.3449 91.5813V65.4059C58.3449 64.6895 58.6194 64.0144 59.1271 63.5047L64.3138 58.2972L69.5006 63.5047C70.0083 64.0144 70.2827 64.6895 70.2827 65.4059V74.3331C68.1284 76.1516 66.276 78.6176 64.9313 82.1444C62.4751 88.6056 63.8473 93.1518 66.0427 96.9404H66.0565ZM97.3965 94.0886C94.9403 96.7199 92.5253 97.9047 90.1789 99.0482C88.5324 99.861 86.9681 100.619 85.5822 101.831C84.4708 102.795 83.867 104.325 83.2496 105.95C81.9323 109.353 80.5739 112.866 74.0698 111.847C74.7971 107.08 72.2449 103.526 69.7887 100.081C66.523 95.5076 63.1338 90.7823 66.2211 82.6403C67.5109 79.2237 69.3222 76.8955 71.4353 75.1734C71.4353 75.1734 71.449 75.1734 71.4627 75.1597C74.5227 72.6937 78.2412 71.5089 82.097 70.2965C90.0417 67.803 98.2335 65.213 99.2763 49.9624C100.402 52.2906 101.293 56.768 100.69 61.9617C100.113 66.88 97.6709 75.8209 87.9423 80.5325C87.6953 80.6565 87.5444 80.9045 87.5581 81.18C87.5581 81.4555 87.7502 81.7035 88.0109 81.7999C91.1257 82.9434 94.1308 82.8607 96.6967 81.552C98.6314 80.5738 100.264 78.8793 101.307 76.8542C103.132 84.2797 101.966 89.2255 97.4102 94.1024L97.3965 94.0886ZM51.7723 28.4021H76.869C77.5826 28.4021 78.2549 28.6776 78.7626 29.1873L87.6679 38.1283C87.6679 38.1283 87.5856 38.2248 87.5307 38.2661C85.445 40.0984 84.4022 42.9639 84.7452 45.9396C85.1843 49.6593 87.6816 52.9105 91.7844 55.0597C91.8804 55.1148 91.9902 55.1423 92.1 55.1423C92.2646 55.1423 92.4293 55.0872 92.5528 54.9632C92.7723 54.7704 92.8409 54.4673 92.7449 54.2055C90.4945 48.2541 91.0022 44.8237 92.1548 42.5506C93.7328 43.7216 95.9694 43.6114 97.4102 42.1648L103.461 36.0894C105.039 34.5051 105.039 31.9289 103.461 30.3446L87.3386 14.1847C85.7606 12.6004 83.1947 12.6004 81.6167 14.1847L79.531 16.2787C79.0233 16.7885 78.351 17.064 77.6374 17.064H50.949C50.2355 17.064 49.5631 16.7885 49.0555 16.2787L46.9698 14.1847C45.3918 12.6004 42.8259 12.6004 41.2479 14.1847L25.1799 30.3583C23.602 31.9426 23.602 34.5189 25.1799 36.1032L31.2311 42.1786C32.0133 42.9639 33.0561 43.3634 34.0852 43.3634C35.1143 43.3634 36.1572 42.9639 36.9393 42.1786L49.865 29.2011C50.359 28.7052 51.0451 28.4159 51.7586 28.4159L51.7723 28.4021ZM92.1 40.0708C90.4534 42.1924 88.6147 45.7881 90.8376 52.883C88.0933 51.0094 86.433 48.5296 86.1174 45.7743C85.8292 43.2945 86.7211 40.8147 88.4363 39.3131C88.6696 39.1065 88.8617 38.8998 89.0675 38.6794C89.0812 38.6656 89.1087 38.6518 89.1361 38.6381C89.1636 38.6243 89.1636 38.5967 89.1773 38.5692C91.2355 36.296 92.0176 33.2652 92.0176 31.0334C92.0176 30.1241 91.8941 29.2976 91.6746 28.6087C92.2783 29.0082 92.7586 29.6282 93.1154 30.4548C94.4189 33.4718 93.6368 38.1145 92.1137 40.0846L92.1 40.0708ZM35.9788 41.2005C34.936 42.2475 33.2482 42.2475 32.2054 41.2005L26.1542 35.125C25.1113 34.078 25.1113 32.3835 26.1542 31.3365L42.2633 15.1628C43.3061 14.1158 44.9939 14.1158 46.0367 15.1628L48.1224 17.2569C48.8908 18.0283 49.9062 18.4416 50.9765 18.4416H77.6649C78.7489 18.4416 79.7643 18.0146 80.519 17.2569L82.6046 15.1628C83.6475 14.1158 85.3352 14.1158 86.3781 15.1628L102.487 31.3365C103.53 32.3835 103.53 34.078 102.487 35.125L96.436 41.2005C95.4617 42.1786 93.9112 42.2337 92.8684 41.3658C92.9781 41.2143 93.0879 41.0627 93.1977 40.925C94.9952 38.5967 95.8871 33.3892 94.3777 29.9037C93.5407 27.975 92.1137 26.8591 90.2338 26.6938C89.9182 26.68 89.6575 26.8316 89.5478 27.1071C89.4243 27.3826 89.5066 27.6995 89.7261 27.8923C90.2887 28.3883 90.6455 29.5869 90.6455 31.0196C90.6455 32.8243 90.0692 35.2077 88.601 37.1089L79.7368 28.2092C78.9684 27.4377 77.953 27.0244 76.8828 27.0244H51.786C50.7158 27.0244 49.6729 27.4515 48.932 28.2092L36.0062 41.1867L35.9788 41.2005Z"
          fill="url(#paint0_linear_2010_20)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2010_20"
          x1="37.3647"
          y1="105.633"
          x2="89.682"
          y2="15.3854"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2010_20">
          <rect
            width="82"
            height="109"
            fill="white"
            transform="translate(24 13)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
