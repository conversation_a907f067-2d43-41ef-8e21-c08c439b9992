import { useTranslation } from 'react-i18next'

import { launchpadIllustration } from '~/assets/images'
import {
  MySubscriptionButton,
  SubscriptionsQuantity,
} from '~/components/gem-launch-platform'
import { Namespace } from '~/i18n'
import { <PERSON><PERSON><PERSON>arian<PERSON>, Hero, HeroImage } from '~/tenset-components'
import { LaunchYourProjectButton } from '~/components/utils'

export function GlpHero() {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM,
    Namespace.COMMON,
  ])

  return (
    <section className="grid place-items-center">
      <Hero
        title={t('hero.title')}
        description={t('hero.description')}
        className="lg:!grid-cols-[1.8fr_1fr] lg:!gap-24"
        leftContent={
          <div className="flex max-w-md flex-col gap-12">
            <SubscriptionsQuantity />

            <div className="flex gap-4">
              <MySubscriptionButton />
              <LaunchYourProjectButton variant={ButtonVariant.Ghost} />
            </div>
          </div>
        }
        rightContent={
          <HeroImage src={launchpadIllustration} alt={t('hero.title')} />
        }
      />
    </section>
  )
}
