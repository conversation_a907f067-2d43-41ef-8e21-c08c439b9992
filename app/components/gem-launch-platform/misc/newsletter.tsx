import { socialButtons } from '~/data/homepage/tokens-available-buttons'
import { Button, ButtonVariant, Icon, Text } from '~/tenset-components'

interface TglpNewsletterProps {
  header: string
  description: string
}

export function TglpNewsletter({ header, description }: TglpNewsletterProps) {
  return (
    <div className="flex flex-col gap-4">
      <Text>{header}</Text>

      <div className="flex w-full flex-col items-stretch justify-start gap-4 sm:flex-row">
        {socialButtons.map(({ label, icon, link }) => (
          <Button
            variant={ButtonVariant.Secondary}
            key={label}
            to={link}
            reverseChildren
          >
            {label}

            <Icon name={icon} size={24} />
          </Button>
        ))}
      </div>
    </div>
  )
}
