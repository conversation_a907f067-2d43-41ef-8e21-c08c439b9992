import { StoryObj, Meta } from '@storybook/react'

import { <PERSON><PERSON>, ButtonVariant } from '../Button'
import { Icon, IconName } from '../Icon'

import { CallToActionSection } from './index'

type CallToActionSectionType = typeof CallToActionSection

export default {
  title: 'Components/CallToActionSection',
  component: CallToActionSection,
  parameters: {},
} as Meta<CallToActionSectionType>

export const Default: StoryObj<CallToActionSectionType> = {
  render: arguments_ => (
    <CallToActionSection
      {...arguments_}
      title="Lorem Ipsum"
      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras at sem mauris. Morbi porttitor arcu vitae orci congue posuere. Aliquam mattis est urna, at tempus purus pharetra in. Curabitur tincidunt, justo eu pulvinar finibus, ex nisi tincidunt eros, quis dictum ligula ipsum ut odio. Vestibulum feugiat dapibus dui, vehicula maximus turpis molestie eget."
    >
      <Button variant={ButtonVariant.Secondary}>
        {' '}
        <Icon name={IconName.Telegram} /> Create a telegram group{' '}
      </Button>
    </CallToActionSection>
  ),
}
