import { Meta } from '@storybook/react'

import { ExplorerButton, ExplorerType } from '.'

type ExplorerButtonType = typeof ExplorerButton

const TOKEN_ADDRESS = '0x1ae369a6ab222aff166325b7b87eb9af06c86e57'
const TX_ADDRESS =
  '0x653654b7de47971e28384b74d5faa073ab4c52c284984efa4174f2996609b844'

export default {
  title: 'Components/ExplorerButton',
  component: ExplorerButton,
  args: {
    address: TOKEN_ADDRESS,
    explorerUrl: 'https://bscscan.com',
  },
} as Meta<ExplorerButtonType>

export const Preview = {}

export const NoAddress = {
  args: {
    address: undefined,
  },
}

export const TransactionAddress = {
  args: {
    address: TX_ADDRESS,
  },
}

export const AddTokenToMetaMask = {
  args: {
    address: TOKEN_ADDRESS,
    explorerType: ExplorerType.TOKEN,
    handleAddToMetamask: () =>
      console.log('addToMetaMaskFn', { token: TOKEN_ADDRESS }),
  },
}
