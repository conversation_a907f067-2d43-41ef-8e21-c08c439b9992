import { verifyTypedData } from 'ethers/lib/utils'

export function signatureValidation({
  message,
  signature,
  subscriptionAddress,
}: {
  message: string
  signature: string
  subscriptionAddress: string
}) {
  let signingAddress

  const { types: messageTypes, value: messageValues } = JSON.parse(message)

  try {
    signingAddress = verifyTypedData(
      { name: 'tenset.io' },
      messageTypes,
      messageValues,
      signature
    ).toLowerCase()
  } catch (error) {
    console.log(error)
    throw new Response('Invalid signature', {
      status: 400,
    })
  }

  if (
    signingAddress !== subscriptionAddress.toLowerCase() ||
    signingAddress !== messageValues.subscriptionAddress.toLowerCase()
  ) {
    throw new Response('Message and signature mismatch', {
      status: 400,
    })
  }

  return signingAddress
}
