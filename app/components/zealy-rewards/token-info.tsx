import { tensetXAddress } from '~/data'
import {
  ExplorerButton,
  ExplorerType,
  IconName,
  TokenInfo,
} from '~/tenset-components'
import { Utils, getNetworkExplorer } from '~/tenset-web3'

export function TokenInfoComponent() {
  const tensetXNetworkExplorer = getNetworkExplorer(Utils.Network.SMART_CHAIN)

  return (
    <div className="flex justify-between">
      <TokenInfo currency="10SETX" icon={IconName.Tenset} name="Tenset X" />
      <ExplorerButton
        address={tensetXAddress}
        explorerUrl={tensetXNetworkExplorer}
        explorerType={ExplorerType.TOKEN}
      />
    </div>
  )
}
