import { Meta } from '@storybook/react'

import { Button, ButtonVariant, Icon, IconName, Tag, TagColor } from '../'
import { fameCover } from '../../assets/images/'

import { GemInfo } from '.'

type GemInfoType = typeof GemInfo

export default {
  title: 'Components/GemInfo',
  component: GemInfo,
  args: {
    title: 'Fame MMA',
    subtitle: '1 BNB = 37,037.04 FAME',
    description:
      'FAME MMA is the biggest freak fight organisation in Europe that hosts MMA events for celebrities, personalities, super stars and famous professional athletes to fight each other.',
    tag: <Tag color={TagColor.NEUTRAL}>Tag</Tag>,
    cover: fameCover,
    actions: (
      <Button variant={ButtonVariant.Secondary}>
        Read more
        <Icon name={IconName.ChevronRight16} />
      </Button>
    ),
    items: [
      {
        title: 'Total Raised',
        value: '24,537 BNB',
      },
      {
        title: 'Participants',
        value: '9,116',
      },
      {
        title: 'Max Investment',
        value: '2.7 BNB',
      },
      {
        title: 'ATH Since Launch',
        value: '30,000 %',
      },
    ],
  },
} as Meta<GemInfoType>

export const Preview = {}
