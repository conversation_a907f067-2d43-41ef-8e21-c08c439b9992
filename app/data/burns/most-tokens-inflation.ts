interface MostTokenInflation {
  token: string
  value: number
  inflationPercentage: number
}

export const mostTokensInflation: MostTokenInflation[] = [
  {
    token: 'Bitcoin',
    value: 1_396_819_338_587,
    inflationPercentage: 1.76,
  },
  {
    token: '<PERSON><PERSON>',
    value: 79_715_651_466,
    inflationPercentage: 13.21,
  },
  {
    token: 'XRP',
    value: 29_256_671_368,
    inflationPercentage: 10.29,
  },
  {
    token: '<PERSON><PERSON>oi<PERSON>',
    value: 23_504_561_662,
    inflationPercentage: 3.46,
  },
]
