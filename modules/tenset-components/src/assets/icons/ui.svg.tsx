import { AssetProps } from './index'

export default function UiUx({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M129.907 110.556L116.458 97.031V26.7289C116.458 23.5991 113.919 21.0726 110.801 21.0726H9.72891C6.59907 21.0726 4.07257 23.6116 4.07257 26.7289V106.571C4.07257 109.701 6.61164 112.228 9.72891 112.228H110.223L110.324 118.814L116.684 113.761L120.97 123.93L126.136 121.743L121.85 111.574L129.907 110.543V110.556Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M9.02501 22.3547C7.91888 22.3547 7.01386 23.2597 7.01386 24.3658C7.01386 25.4719 7.91888 26.377 9.02501 26.377C10.1311 26.377 11.0362 25.4719 11.0362 24.3658C11.0362 23.2597 10.1311 22.3547 9.02501 22.3547ZM9.02501 25.1326C8.61021 25.1326 8.27083 24.7932 8.27083 24.3784C8.27083 23.9636 8.61021 23.6242 9.02501 23.6242C9.43981 23.6242 9.77919 23.9636 9.77919 24.3784C9.77919 24.7932 9.43981 25.1326 9.02501 25.1326ZM126.916 106.672L113.642 93.3355V23.2848C113.642 19.8156 110.827 17 107.357 17H6.28482C2.8156 17 0 19.8156 0 23.2848V103.127C0 106.596 2.8156 109.412 6.28482 109.412H106.163L106.251 115.383C106.251 115.621 106.39 115.835 106.616 115.936C106.829 116.036 107.093 116.011 107.282 115.86L112.989 111.323L116.961 120.725C117.023 120.876 117.149 121.001 117.3 121.064C117.375 121.089 117.451 121.114 117.539 121.114C117.627 121.114 117.702 121.102 117.778 121.064L122.944 118.877C123.258 118.739 123.409 118.374 123.283 118.047L119.311 108.645L126.551 107.728C126.79 107.703 126.991 107.539 127.067 107.313C127.142 107.087 127.092 106.835 126.916 106.672V106.672ZM1.25696 23.2848C1.25696 20.5195 3.5195 18.257 6.28482 18.257H107.357C110.135 18.257 112.385 20.5195 112.385 23.2848V30.1479H1.25696V23.2848ZM106.88 86.5353C106.704 86.3468 106.427 86.2965 106.188 86.397C105.95 86.4976 105.799 86.7238 105.799 86.9878L106.138 108.155H6.28482C3.50693 108.155 1.25696 105.893 1.25696 103.127V31.4048H112.385V92.0659L106.88 86.5353ZM118.331 107.514C118.13 107.539 117.966 107.652 117.866 107.828C117.765 107.992 117.752 108.205 117.828 108.381L121.875 117.972L117.866 119.669L113.818 110.078C113.743 109.902 113.579 109.764 113.391 109.714C113.341 109.701 113.29 109.689 113.24 109.689C113.102 109.689 112.963 109.739 112.85 109.827L107.496 114.088L107.408 108.784L107.093 88.5213L112.574 94.0268L125.131 106.647L118.343 107.514H118.331ZM94.1969 77.9754C94.1215 77.9377 94.0335 77.9251 93.9581 77.9251H85.9764V75.1849C85.9764 73.1863 84.3423 71.5523 82.3438 71.5523H57.4433V66.4364H64.3063C67.2099 66.4364 69.573 64.0733 69.573 61.1572V43.8362C69.573 40.9326 67.2099 38.5569 64.3063 38.5569H49.3107C46.4071 38.5569 44.044 40.92 44.044 43.8362V61.1572C44.044 64.0608 46.4071 66.4364 49.3107 66.4364H56.1738V71.5523H31.2984C29.2998 71.5523 27.6658 73.1738 27.6658 75.1849V77.9251H19.6841C19.5961 77.9251 19.5207 77.9377 19.4452 77.9754C19.2944 78.0382 19.1687 78.1639 19.1059 78.3147C19.0682 78.3902 19.0556 78.4782 19.0556 78.5536V95.7866C19.0556 95.8745 19.0682 95.95 19.1059 96.0254C19.1687 96.1762 19.2944 96.3019 19.4452 96.3648C19.5207 96.4025 19.6087 96.415 19.6841 96.415H36.9171C37.005 96.415 37.0805 96.4025 37.1559 96.3648C37.3067 96.3019 37.4324 96.1762 37.4953 96.0254C37.533 95.95 37.5455 95.862 37.5455 95.7866V78.5536C37.5455 78.4656 37.533 78.3902 37.4953 78.3147C37.4324 78.1639 37.3067 78.0382 37.1559 77.9754C37.0805 77.9377 36.9925 77.9251 36.9171 77.9251H28.9353V75.1849C28.9353 73.8777 30.0037 72.8092 31.311 72.8092H56.2115V77.9251H48.2297C48.1418 77.9251 48.0663 77.9377 47.9909 77.9754C47.8401 78.0382 47.7144 78.1639 47.6515 78.3147C47.6138 78.3902 47.6013 78.4782 47.6013 78.5536V95.7866C47.6013 95.8745 47.6138 95.95 47.6515 96.0254C47.7144 96.1762 47.8401 96.3019 47.9909 96.3648C48.0663 96.4025 48.1543 96.415 48.2297 96.415H65.4627C65.5507 96.415 65.6261 96.4025 65.7016 96.3648C65.8524 96.3019 65.9781 96.1762 66.0409 96.0254C66.0786 95.95 66.0912 95.862 66.0912 95.7866V78.5536C66.0912 78.4656 66.0786 78.3902 66.0409 78.3147C65.9781 78.1639 65.8524 78.0382 65.7016 77.9754C65.6261 77.9377 65.5381 77.9251 65.4627 77.9251H57.481V72.8092H82.3815C83.6887 72.8092 84.7571 73.8777 84.7571 75.1849V77.9251H76.7754C76.6874 77.9251 76.612 77.9377 76.5366 77.9754C76.3858 78.0382 76.2601 78.1639 76.1972 78.3147C76.1595 78.3902 76.1469 78.4782 76.1469 78.5536V95.7866C76.1469 95.8745 76.1595 95.95 76.1972 96.0254C76.2601 96.1762 76.3858 96.3019 76.5366 96.3648C76.612 96.4025 76.7 96.415 76.7754 96.415H94.0084C94.0964 96.415 94.1718 96.4025 94.2472 96.3648C94.3981 96.3019 94.5238 96.1762 94.5866 96.0254C94.6243 95.95 94.6369 95.862 94.6369 95.7866V78.5536C94.6369 78.4656 94.6243 78.3902 94.5866 78.3147C94.5238 78.1639 94.3981 78.0382 94.2472 77.9754H94.1969ZM20.3126 80.0745L27.4144 87.1764L20.3126 94.2782V80.0871V80.0745ZM21.205 95.1581L28.3068 88.0562L35.4087 95.1581H21.2176H21.205ZM36.2886 94.2656L29.1867 87.1638L36.2886 80.0619V94.2531V94.2656ZM35.3961 79.1821L28.2943 86.2839L21.1924 79.1821H35.3836H35.3961ZM45.3136 43.8362C45.3136 41.6239 47.111 39.8139 49.3233 39.8139H64.3189C66.5311 39.8139 68.3286 41.6114 68.3286 43.8362V45.6337H45.301V43.8362H45.3136ZM48.8457 80.0619L55.9475 87.1638L48.8457 94.2656V80.0745V80.0619ZM49.7381 95.1455L56.8399 88.0437L63.9418 95.1455H49.7507H49.7381ZM64.8217 94.2531L57.7198 87.1512L64.8217 80.0494V94.2405V94.2531ZM63.9292 79.1695L56.8274 86.2713L49.7255 79.1695H63.9167H63.9292ZM49.3359 65.1669C47.1236 65.1669 45.3262 63.3694 45.3262 61.1446V46.8781H68.3537V61.1446C68.3537 63.3569 66.5563 65.1669 64.344 65.1669H49.3484H49.3359ZM92.4498 79.1695L85.3479 86.2713L78.2461 79.1695H92.4372H92.4498ZM77.3662 80.0619L84.468 87.1638L77.3662 94.2656V80.0745V80.0619ZM78.2586 95.1455L85.3605 88.0437L92.4623 95.1455H78.2712H78.2586ZM93.3422 94.2531L86.2404 87.1512L93.3422 80.0494V94.2405V94.2531ZM16.2777 22.3547C15.1716 22.3547 14.2666 23.2597 14.2666 24.3658C14.2666 25.4719 15.1716 26.377 16.2777 26.377C17.3838 26.377 18.2888 25.4719 18.2888 24.3658C18.2888 23.2597 17.3838 22.3547 16.2777 22.3547ZM16.2777 25.1326C15.8629 25.1326 15.5235 24.7932 15.5235 24.3784C15.5235 23.9636 15.8629 23.6242 16.2777 23.6242C16.6925 23.6242 17.0319 23.9636 17.0319 24.3784C17.0319 24.7932 16.6925 25.1326 16.2777 25.1326ZM23.5178 22.3547C22.4117 22.3547 21.5067 23.2597 21.5067 24.3658C21.5067 25.4719 22.4117 26.377 23.5178 26.377C24.6239 26.377 25.529 25.4719 25.529 24.3658C25.529 23.2597 24.6239 22.3547 23.5178 22.3547ZM23.5178 25.1326C23.103 25.1326 22.7636 24.7932 22.7636 24.3784C22.7636 23.9636 23.103 23.6242 23.5178 23.6242C23.9326 23.6242 24.272 23.9636 24.272 24.3784C24.272 24.7932 23.9326 25.1326 23.5178 25.1326Z"
        fill={color ?? 'url(#paint0_linear_937_1219)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_937_1219"
          x1="30.3557"
          y1="124.282"
          x2="96.4721"
          y2="9.74732"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
