import { Config, Utils } from './types'

const {
  SMART_CHAIN,
  SMART_CHAIN_TESTNET,
  ETHEREUM,
  P<PERSON><PERSON><PERSON><PERSON>,
  ARBITRUM_ONE,
  BASE,
  PHALCON_BSC,
  PHALCON_ETH,
  ANVIL_BSC,
  ANVIL_ETH,
} = Utils.Network

export const config: Config = {
  walletConnectProjectId: 'ff8ddbf4a743bdefef7bc7acbad40268',
  tensetToken: {
    [SMART_CHAIN]: '******************************************',
    [SMART_CHAIN_TESTNET]: '******************************************',
    [PHALCON_BSC]: '******************************************',
    [ANVIL_BSC]: '******************************************',
  },
  tensetX: {
    infinityVaultBridgeExchanger: {
      [SMART_CHAIN]: '******************************************',
      [SMART_CHAIN_TESTNET]: '******************************************',
    },
    infinityVaultBurnExchanger: {
      [SMART_CHAIN]: '******************************************',
      [SMART_CHAIN_TESTNET]: '******************************************',
    },
    token: {
      [SMART_CHAIN]: '******************************************',
      [SMART_CHAIN_TESTNET]: '******************************************',
    },
  },
  gemLaunchPlatform: {
    subscribe: {
      [SMART_CHAIN]: '******************************************',
      [PHALCON_BSC]: '******************************************',
      [ANVIL_BSC]: '******************************************',
    },
    membership: {
      [SMART_CHAIN]: '0x008C037BBD16F10682a4c03Cd43852fdE6c9cf16',
      [PHALCON_BSC]: '0x008C037BBD16F10682a4c03Cd43852fdE6c9cf16',
      [ANVIL_BSC]: '0x008C037BBD16F10682a4c03Cd43852fdE6c9cf16',
    },
    withdraw: {
      [SMART_CHAIN]: '0xEB7e613D8cF6dF9e3c8FE595e3622fb575d32b72',
      [PHALCON_BSC]: '0xEB7e613D8cF6dF9e3c8FE595e3622fb575d32b72',
      [ANVIL_BSC]: '0xEB7e613D8cF6dF9e3c8FE595e3622fb575d32b72',
    },
    extend: {
      [SMART_CHAIN]: '0xFf29E7106DBA06c015ba44A329c129f2a72Ca581',
      [PHALCON_BSC]: '0xFf29E7106DBA06c015ba44A329c129f2a72Ca581',
      [ANVIL_BSC]: '0xFf29E7106DBA06c015ba44A329c129f2a72Ca581',
    },
    upgrade: {
      [SMART_CHAIN]: '0xfE94d66F89ab88C1bAFC9962AF0820F182FAeE2b',
      [PHALCON_BSC]: '0xfE94d66F89ab88C1bAFC9962AF0820F182FAeE2b',
      [ANVIL_BSC]: '0xfE94d66F89ab88C1bAFC9962AF0820F182FAeE2b',
    },
    lifetimeMembership: {
      [SMART_CHAIN]: '******************************************',
      [PHALCON_BSC]: '******************************************',
      [ANVIL_BSC]: '******************************************',
    },
    oldWithdraw: {
      [SMART_CHAIN]: '******************************************',
      [PHALCON_BSC]: '******************************************',
      [ANVIL_BSC]: '******************************************',
    },
  },
  infinity: {
    vault: {
      [SMART_CHAIN]: '******************************************',
    },
    vaultBridge: {
      [SMART_CHAIN]: '******************************************',
    },
    nftVault: {
      [ETHEREUM]: '******************************************',
    },
    withdrawalFee: {
      [SMART_CHAIN]: '******************************************',
      [ETHEREUM]: '******************************************',
      [POLYGON]: '******************************************',
      [ARBITRUM_ONE]: '******************************************',
      [BASE]: '******************************************',
    },
    nftWithdrawalFee: {
      [ETHEREUM]: '******************************************',
      [POLYGON]: '******************************************',
    },
  },
  zealy: {
    rewards: {
      [SMART_CHAIN]: '******************************************',
      [SMART_CHAIN_TESTNET]: '******************************************',
    },
    nft: {
      [SMART_CHAIN]: '******************************************',
      [SMART_CHAIN_TESTNET]: '******************************************',
    },
    nftMinter: {
      [SMART_CHAIN]: '******************************************',
      [SMART_CHAIN_TESTNET]: '******************************************',
    },
  },
  utils: {
    erc721Multisender: {
      [SMART_CHAIN]: '******************************************',
      [ETHEREUM]: '******************************************',
      [PHALCON_BSC]: '******************************************',
      [PHALCON_ETH]: '******************************************',
      [ANVIL_BSC]: '******************************************',
      [ANVIL_ETH]: '******************************************',
    },
  },
  agreementsApiUrl: 'https://www.tenset.io/api/agreements',
}
