import { useTranslation } from 'react-i18next'

import ReadMoreButton from '~/components/read-more-button'
import { theLuckyOnesTiles } from '~/data/'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { H2, Tag, Tile } from '~/tenset-components'

export function LuckyOnesIncubator() {
  const { t } = useTranslation([Namespace.INCUBATOR])
  const localizePathname = useLocalizePathname()

  return (
    <section className="flex flex-col gap-8">
      <header className="flex flex-col gap-4">
        <H2 isBold>{t('the-lucky-ones.title')}</H2>
      </header>

      <main className="flex flex-col gap-8">
        {theLuckyOnesTiles.map(
          ({ title, description, news, video, tagColor, tagLabel }) => (
            <Tile
              key={title}
              title={t(title)}
              cover={video}
              tag={
                tagLabel ? <Tag color={tagColor}>{tagLabel}</Tag> : undefined
              }
              isVideo
              description={t(description)}
              showDescriptionOnMobile
              actions={<ReadMoreButton link={localizePathname(news)} />}
            />
          )
        )}
      </main>
    </section>
  )
}
