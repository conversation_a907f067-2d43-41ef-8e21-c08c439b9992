{"bonds": {"description": {"description": "Tenset Bonds are blockchain-powered digital bonds that allow investors to fund exciting projects while earning potential returns. Unlike traditional financial bonds, which rely on banks or intermediaries, Tenset Bonds operate on smart contracts, ensuring secure, automated, and transparent transactions.", "title": "What Are Tenset Bonds?"}, "ecosystem": {"blockchain-security": {"description": "Smart contracts ensure safe and automated transactions.", "title": "Blockchain Security"}, "exclusive-opportunities": {"description": "Access to early-stage blockchain projects.", "title": "Exclusive Opportunities"}, "higher-returns": {"description": "Competitive rates compared to traditional investments.", "title": "Higher Returns"}, "title": "Why Use Tenset Bonds?"}, "hero": {"action": "Create a Bond", "description": "A new way to invest in blockchain-based projects through secure and transparent digital bonds. Earn rewards while supporting innovation.", "title": "Tenset Bonds - The Future of Smart Investing"}, "meta": {"description": "A new way to invest in blockchain-based projects through secure and transparent digital bonds. Earn rewards while supporting innovation.", "title": "Tenset Bonds - The Future of Smart Investing"}, "start-investing": {"description": "Join the new era of investing with Tenset Bonds and grow your wealth securely with blockchain-backed opportunities.", "title": "Start Investing Today!"}, "stepper": {"action": "Create a Bond", "first-step": {"content": "Startups or blockchain companies create bonds to raise funds. They set terms like interest rate, duration, and total supply.", "title": "Projects Issue Bonds"}, "second-step": {"content": "Investors buy these bonds at a discounted price using cryptocurrency.", "title": "Investors Purchase Bonds"}, "third-step": {"content": "After a set period, the bond matures, and investors receive their initial investment + returns, either in stablecoins, project tokens, or other agreed assets.", "title": "<PERSON><PERSON><PERSON> at Maturity"}, "title": "How it works?"}}, "cyberstorm": {"big-cards": {"first": {"description": "Tenset Cyberstorm is the ultimate smart contract auditing package to create a blockchain fortress around your project. This will provide unmatched credibility and security in the market, while also proving the quality of your development.", "title": "Why choose cyberstorm?"}}, "description": {"description": "Our audit’s secret sauce combines the finest auditing tools available today with our exclusive tooling inspired by the latest academic research. It draws from technologies like Artificial Intelligence, Machine and deep learning, CNN (convolutional neural network), model based testing, static analysis, symbolic execution, flow analysis, and differential testing.", "title": "Cyberstorm’s revolutionary technology"}, "hero": {"description": "Cyberstorm is Tenset’s new proprietary, superior, cutting-edge auditing technology. Named so because it brings a massive storm upon an audited smart contract and exposes everything that stands in its way.", "title": "Our technology - Your safety"}, "meta": {"description": "Cyberstorm is Tenset’s new proprietary, superior, cutting-edge auditing technology. Named so because it brings a massive storm upon an audited smart contract and exposes everything that stands in its way.", "title": "Cyberstorm"}, "stepper": {"description": "Usually security audits utilize manual reviews with static code analysis (relatively simple automated tooling).", "first-step": {"content": "With our security audit, the code is first attacked by Cyberstorm (in 2 separate stages) from every direction and angle, to detect lots of vulnerabilities. The above mentioned practices like AI and machine learning are utilised to analyse patterns and data within the contracts."}, "second-step": {"content": "In the final stage, the code is reviewed manually by highly-skilled auditors. They scrutinize the results from the previous 2 stages to guide their reasoning about the code and reinforces and enhance the outcome of the audit."}, "third-step": {"content": "After each of the 3 stages, the client will receive a report. We are in regular contact and provide updates so that no project is left in the dark of the status of their audit."}, "title": "How does a Tenset Cyberstorm security audit differ from an ordinary one? "}}, "smart-contract-audits": {"auditing-system": {"ai": {"description": "We employ static code analysis as well as artificial intelligence programs to review all aspects of the code down to the tiniest detail.", "title": "Artificial intelligence"}, "model-training": {"description": "This groundbreaking approach allows us to randomly generate transactions on the real system and on a model. We can identify bugs by comparing the differences between the model and your smart contract.", "title": "Model based training"}, "processing": {"description": "Features a combination of both manual and automated processes to investigate your smart contract.", "title": "Processing"}, "title": "Intelligence built into the auditing system"}, "big-cards": {"cex": {"description": "After the audit, you will be able to integrate the badge “audited by Tenset” into your website. A secure smart contract is a requirement for all centralised exchanges to consider a listing.", "title": "CEX certificate proves you trustworthy"}, "media-coverage": {"description": "We will offer you an extended service package including marketing activities.", "title": "Media coverage builds the momentum"}, "security-report": {"description": "Tenset provides a report with observations on the quality of the contract. We flag all minor and major issues that have been identified. We recommend how these flaws can be resolved, and allow the project to address them. The final report will then be made public which will note the resolved issues.", "title": "Security Report allows you to fix the issues"}}, "description": {"description": "The Tenset team is made up of blockchain experts and cyber security specialists. They have vast experience in developing and testing battle-tested technology systems in the past so know how to investigate and verify smart contracts. You can leverage our expertise to ensure your technology is bulletproof to survive and thrive in this disruptive and rapidly changing tech industry.", "title": "Ensure your smart contracts are safe and secure for users"}, "hero": {"action": "Request a quote", "description": "Harness the power of an effective 3-stage audit methodology valued by industry leaders", "title": "Smart contract audit that builds your credibility"}, "meta": {"description": "Cutting-edge smart contract audits from cyber security experts to identify potential vulnerabilities in coding and ensure it is secure and fit for use.", "title": "Smart Contract Audits"}, "stepper": {"action": "Request a quote", "first-step": {"content": "You submit the required documentation and get the estimation of the process scope, timeline, and price.", "timeline": "1-3 days", "title": "Get a quote"}, "fourth-step": {"content": "You submit the required documentation and get the estimation of the process scope, timeline, and price.", "timeline": "1-3 days", "title": "Result"}, "second-step": {"content": "You submit the required documentation and get the estimation of the process scope, timeline, and price.", "timeline": "1-3 days", "title": "Next Step"}, "third-step": {"content": "You submit the required documentation and get the estimation of the process scope, timeline, and price.", "timeline": "1-3 days", "title": "Next Step"}, "title": "We deliver the Smart Contract audit in 4 steps"}, "trust-and-recognition": {"data": {"certificates": {"label": "Certificates produced"}, "issues": {"label": "Issues resolved"}, "social-media": {"label": "Social media interactions"}}, "title": "Your project gains trust and recognition"}}, "smart-contract-development": {"big-cards": {"control-economy": {"description": "Distributing tokens all at once can be disastrous to your token’s value. Use our vesting platform to release your tokens gradually over time. Vesting platform includes audited smart contracts deployed and connected to a beautiful web application for your community. Customize vesting schedule per groups of addresses so that you control exactly who gets how many tokens and when.", "title": "Control the economy of your project"}, "reach-audiences": {"description": "Don’t limit your project’s growth potential to one blockchain’s user base. With our cross-chain solutions you can make your smart contracts seamlessly work on many chains, without expensive 3rd party bridges.", "title": "Reach audiences on any blockchain"}, "shorten-time": {"description": "Earn today, not someday. By using our blockchain solutions, you speed up your project’s development and start growing in weeks, not months.", "title": "Shorten your time to market"}}, "description": {"description": "Get access to agile teams of experts who setup your project’s technological foundations using tried and tested blockchain solutions. Tokens, NFTs, vesting platforms, cross-chain solutions, or anything else you imagine.", "title": "Launch your project in weeks, not months."}, "ecosystem": {"cross-chain": {"description": "Make your vision a reality across blockchain ecosystems. Onboard customers on any blockchain.", "title": "Cross-chain solutions"}, "title": "Ecosystem of solutions", "tokens": {"description": "The heartbeat of your project's economy.", "title": "Fungible, semi, and non-fungible tokens"}, "vesting": {"description": "Release tokens when you want, how you want.", "title": "Vesting platforms"}}, "hero": {"action": "Request a quote", "description": "Development takes forever? Setup technological foundations of your project with our proven blockchain solutions.", "title": "Proven blockchain solutions that catapult your project into success"}, "meta": {"description": "Custom-made smart contracts tailored to your needs, built by blockchain experts who have developed countless contracts in the field of crypto tokens and NFTs.", "title": "Smart Contract Development"}, "stepper": {"action": "Request a quote", "first-step": {"content": "Our expert takes you through a battle-tested process that helps you communicate your vision clearly.", "timeline": "1-3 days", "title": "Discovering requirements"}, "fourth-step": {"content": "We deploy and hand over every piece of your ecosystem alongside extensive documentation. Your project is live.", "title": "Your vision becomes a reality"}, "second-step": {"content": "Our software architect takes your business requirements and architectures a technical solution.", "timeline": "5-8 days", "title": "Designing architecture"}, "third-step": {"content": "Our experienced engineers implement smart contracts, web services, and applications.", "timeline": "3-5 days", "title": "Development"}, "title": "We develop Smart Contracts in 4 steps"}, "technological-foundations": {"data": {"conversions": {"label": "Conversions"}, "interviews": {"label": "Interviews conducted"}, "time": {"label": "Av. time spent on page"}}, "title": "Set up the technological foundations of your project without delays and confusion"}}, "ui-ux": {"big-cards": {"first": {"description": "We've got the know-how to get people in crypto to do what you want them to. It's test-proven over multiple bull runs.", "title": "Doubling your conversion rate is the quickest way to grow your business"}, "second": {"description": "The world of crypto has its' own rules. We've mastered them for you so we can provide your projects with the visual assets that will help you stand out from the crowd.", "title": "Trustworthy image sets you ahead of other crypto projects"}, "third": {"description": "We design your website with a set of high fidelity re-usable assets that your team later implements. With this approach design will no longer be a bottleneck of your project.", "title": "Scalable design system is here to set you up for the long run"}}, "conversion": {"data": {"conversions": {"label": "Conversions"}, "time": {"label": "Av. time spent on page"}, "visited": {"label": "Av. pages visited"}}, "title": "Convert visitors into investors"}, "description": {"description": "The Tenset team is made up of blockchain experts who have been in the crypto space for a long time. They have seen many bull runs and bear markets and understand how users think and act. You can leverage our expertise in UI/UX from the traditional business world with companies like Revolut, and blockchain know-how to maximise your website's capabilities.", "title": "Our team has figured out what actually works in the world of crypto"}, "design": {"measure": {"description": "Collect the right data and leverage it to improve over time. We will set you up for high conversion rates with the ideas for bold experimentation.", "title": "Measure and optimize"}, "problem": {"description": "We rely on our experience and on research to discover what does - or might - prevent your prospects from converting into investors.", "title": "Understand the problem"}, "solutions": {"description": "Forget the best practices, we're talking a tailor-made sales engine. We know that in crypto sales follow trust and that's what we design into your project.", "title": "Design the right solutions"}, "title": "Data informed design outperforms guessing"}, "hero": {"action": "Request a quote", "description": "We combine our UI/UX and crypto expertize to provide you with a web design that catches the spotlight and builds trust.", "title": "Design trust into your crypto project"}, "meta": {"description": "User interface and experience design from experts with crypto know-how and who understand how users think and act. Maximise your website's capabilities.", "title": "UI/UX for Crypto"}, "stepper": {"action": "Request a quote", "first-step": {"content": "You submit the required documentation and get the estimation of the process scope, timeline, and price.", "timeline": "1-3 days", "title": "Get a quote"}, "fourth-step": {"content": "We advise you on how to measure the performance of your new crypto website against your business objectives.", "title": "Measure the results"}, "second-step": {"content": "We run research and iterations in continous cycles. It carries until we're sure what solutions will convert your visitors into investors.", "timeline": "5-8 days", "title": "Research & Development"}, "third-step": {"content": "You receive a set of design assets that come with a documentation. You can pick it up and start development immediately after.", "timeline": "3-5 days", "title": "Design the specs"}, "title": "We deliver UI/UX in 4 steps"}}}