import type { TFunction } from 'i18next'

import type { CardWithTile } from '~/components/cards-with-title'
import { IconName } from '~/tenset-components'

export const ecosystem = (t: TFunction): CardWithTile[] => [
  {
    title: t('bonds.ecosystem.higher-returns.title', 'Higher Returns'),
    description: t(
      'bonds.ecosystem.higher-returns.description',
      'Competitive rates compared to traditional investments.'
    ),
    image: IconName.HigherReturn,
  },
  {
    title: t(
      'bonds.ecosystem.blockchain-security.title',
      'Blockchain Security'
    ),
    description: t(
      'bonds.ecosystem.blockchain-security.description',
      'Smart contracts ensure safe and automated transactions.'
    ),
    image: IconName.BlockchainSecurity,
  },
  {
    title: t(
      'bonds.ecosystem.exclusive-opportunities.title',
      'Exclusive Opportunities'
    ),
    description: t(
      'bonds.ecosystem.exclusive-opportunities.description',
      'Access to early-stage blockchain projects.'
    ),
    image: IconName.ExclusiveOpportunity,
  },
]
