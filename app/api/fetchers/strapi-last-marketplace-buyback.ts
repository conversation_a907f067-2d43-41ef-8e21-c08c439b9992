import { adaptStrapiLastMarketplaceBuyback } from '~/api/adapters'
import type {
  StrapiLastMarketplaceBuybackApiData,
  StrapiLastMarketplaceBuybacksApiResponse,
} from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function fetchStrapiLastMarketplaceBuyback(): Promise<StrapiLastMarketplaceBuybackApiData> {
  const STRAPI_BUYBACKS_URL = `${getServerEnvironment().STRAPI_URL}/api/buybacks?`

  const latestMarketplaceBuybackSearchParameters = decodeURIComponent(
    new URLSearchParams({
      'filters[buyback_source][$eq]': 'marketplace',
      sort: 'createdAt:desc',
      'pagination[pageSize]': '1',
    }).toString()
  )

  return await fetch(
    STRAPI_BUYBACKS_URL + latestMarketplaceBuybackSearchParameters
  )
    .then<StrapiLastMarketplaceBuybacksApiResponse>(response => response.json())
    .then(({ data }) => adaptStrapiLastMarketplaceBuyback(data[0]))
}
