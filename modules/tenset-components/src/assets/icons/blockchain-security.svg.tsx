import { AssetProps } from '.'

export default function BlockchainSecurity({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '38'}
      height={size ?? '40'}
      viewBox="0 0 38 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g>
        <path
          d="M18.9935 39C40.694 30.3449 35.513 6.45683 35.513 6.45683C35.513 6.45683 27.5347 6.29197 18.9935 1C10.4689 6.29197 2.49051 6.45683 2.49051 6.45683C2.49051 6.45683 -2.70702 30.3449 19.01 39H18.9935Z"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
        />
        <path
          d="M12.9518 19.7278L17.5038 24.822L26.1608 15.1777"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}
