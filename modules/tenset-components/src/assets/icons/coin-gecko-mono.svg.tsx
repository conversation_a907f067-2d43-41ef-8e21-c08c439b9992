import { AssetProps } from './index'

export default function CoinGeckoMonoLogo({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_1072_8439)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9685 0.00013593C18.6108 0.00013593 23.9684 5.35778 23.9684 12.0313C23.9684 18.6109 18.5481 24.0312 11.8745 23.9999C5.42031 23.9372 0 18.6109 0 12C0 5.35778 5.32631 -0.0311953 11.9685 0.00013593ZM2.85114 18.2662C2.85114 18.3916 2.9138 18.4542 2.97647 18.5482C3.44644 19.2062 3.97907 19.8328 4.60569 20.3654C4.88767 20.6161 5.16965 20.8667 5.48297 21.0861C6.36024 21.65 7.26885 22.1827 8.27145 22.5273C9.08606 22.8093 9.932 23.0286 10.7779 23.1226C11.4359 23.1853 12.1252 23.2166 12.7831 23.1226C13.1591 23.0599 13.5664 23.0599 13.9424 22.9033C14.0051 22.9346 14.0364 22.8719 14.0677 22.8719C14.851 22.7466 15.603 22.496 16.3549 22.1827C18.7987 21.1174 20.6473 19.3942 21.8379 17.013C22.9031 14.9138 23.2478 12.6893 22.9031 10.3708C22.5898 8.14626 21.6499 6.20372 20.146 4.5745C18.1408 2.28732 15.5716 1.09673 12.5325 0.908742C11.1853 0.814749 9.83801 1.00274 8.55343 1.44137C6.45424 2.13066 4.73102 3.32125 3.32111 5.04447C1.72322 7.01833 0.939937 9.27418 0.845943 11.7807C0.814612 12.4073 0.877275 13.0026 0.971269 13.6292C1.15926 15.0391 1.62922 16.3864 2.34984 17.6083C2.53783 17.7963 2.66316 18.0469 2.85114 18.2662Z"
          fill={color ?? 'currentColor'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.9423 22.872C13.5664 23.06 13.1591 23.06 12.7831 23.0913C12.1251 23.1853 11.4358 23.154 10.7779 23.0913C9.93193 22.9973 9.08599 22.778 8.27138 22.496C7.26878 22.1514 6.36017 21.6187 5.4829 21.0548C5.16959 20.8354 4.8876 20.5848 4.60562 20.3341C3.979 19.8015 3.44637 19.1749 2.9764 18.5169C2.91374 18.4229 2.85107 18.3289 2.85107 18.235C3.60302 16.5431 3.885 14.7572 4.04166 12.94C4.10432 12.094 4.13565 11.2481 4.29231 10.4021C4.44897 9.43087 4.82494 8.5536 5.51423 7.80165C6.14085 7.14369 6.92414 6.70506 7.77008 6.39174C8.45937 6.10976 9.17999 5.85911 9.96327 5.82778C10.6839 5.79645 11.3732 5.89044 12.0625 6.0471C13.0964 6.29775 14.1303 6.61106 15.1016 7.08103C15.8849 7.39434 16.6995 7.70766 17.5141 7.95831C18.36 8.20896 19.206 8.49094 19.9266 9.02357C20.8352 9.71286 21.0859 10.5901 20.6785 11.6554C20.4279 12.3133 19.9579 12.8146 19.488 13.3159C18.642 14.1932 17.7334 15.0078 16.9188 15.9164C15.9789 17.013 15.1329 18.141 14.5376 19.4569C14.0363 20.5535 13.7543 21.6814 13.9423 22.872ZM19.9893 12.564C19.9579 12.564 19.9266 12.564 19.8953 12.5953C19.8639 12.5953 19.8326 12.6267 19.8326 12.658C19.8326 12.658 19.8326 12.658 19.8326 12.6893C19.8639 12.6893 19.8953 12.658 19.8953 12.6267L19.9893 12.564C20.0206 12.564 20.0519 12.564 19.9893 12.564C20.0206 12.5327 19.9893 12.564 19.9893 12.564ZM10.4019 7.11236C9.21132 7.11236 8.24005 8.08363 8.24005 9.24289C8.24005 10.4335 9.21132 11.3734 10.3706 11.4047C11.5612 11.4047 12.5011 10.4335 12.5324 9.24289C12.5324 8.0523 11.5925 7.11236 10.4019 7.11236ZM12.1565 13.6919C12.3444 13.9739 12.6891 14.2246 13.0964 14.3499C13.911 14.6319 14.7256 14.6319 15.5402 14.4752C16.9815 14.1932 18.3287 13.6919 19.5506 12.8773C19.6133 12.8146 19.7386 12.8146 19.7699 12.6893C19.7073 12.658 19.676 12.6893 19.6446 12.7207C19.2373 12.9713 18.7987 13.1593 18.36 13.3473C17.2321 13.8172 16.0415 14.1306 14.7883 14.1932C13.8797 14.2559 12.9711 14.1932 12.1565 13.6919ZM18.0781 11.8434C18.3287 11.8434 18.548 11.6241 18.548 11.3421C18.548 11.0914 18.3287 10.8721 18.0467 10.9034C17.7961 10.9034 17.6081 11.1228 17.6081 11.3734C17.6081 11.6554 17.7961 11.8434 18.0781 11.8434Z"
          fill={color ?? 'currentColor'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.0313 6.04727C11.342 5.92194 10.6527 5.79662 9.9321 5.82795C9.14882 5.85928 8.45953 6.10993 7.73891 6.39191C6.89297 6.70522 6.10969 7.14386 5.48306 7.80182C4.79378 8.52244 4.4178 9.39971 4.26115 10.4023C4.13582 11.2483 4.10449 12.0942 4.0105 12.9401C3.85384 14.7574 3.57186 16.5746 2.81991 18.2351C2.60059 18.0471 2.47526 17.7652 2.34994 17.5458C1.62932 16.3239 1.15935 14.9767 0.971366 13.5668C0.877372 12.9401 0.81471 12.3448 0.846041 11.7182C0.940034 9.21172 1.72332 6.95587 3.32121 4.98201C4.69978 3.25879 6.45433 2.0682 8.55353 1.37891C9.83811 0.971608 11.1853 0.783621 12.5326 0.846284C15.5404 1.03427 18.1096 2.25619 20.1461 4.48071C21.65 6.14126 22.5586 8.0838 22.9032 10.277C23.2479 12.5955 22.9032 14.82 21.838 16.9192C20.6474 19.3004 18.7988 21.0236 16.355 22.0889C15.6344 22.4022 14.8824 22.6528 14.0678 22.7782C14.0052 22.7782 13.9738 22.8721 13.9425 22.8095C13.7545 21.6189 14.0365 20.491 14.5065 19.3944C15.1018 18.0785 15.9477 16.9505 16.8876 15.8539C17.6709 14.9453 18.5795 14.0994 19.4568 13.2535C19.9581 12.7522 20.3967 12.2509 20.6474 11.5929C21.086 10.5276 20.8354 9.65036 19.8954 8.96107C19.1748 8.42844 18.3289 8.14646 17.4829 7.89581C16.6683 7.64516 15.8537 7.33185 15.0704 7.01854C14.9138 6.48591 14.5378 6.14126 14.0365 5.95327C13.3785 5.79662 12.7206 5.85928 12.0313 6.04727ZM22.2766 12.7208H22.3393C22.4646 12.2822 22.4646 11.8122 22.4646 11.3422C22.4646 10.8096 22.4019 10.277 22.3079 9.74435C21.9633 8.02114 21.18 6.51724 19.9581 5.23266C19.2061 4.44938 18.3602 3.79142 17.5143 3.10213C16.7623 2.47551 15.9477 2.00554 15.0078 1.75489C14.0992 1.53557 13.2219 1.41024 12.2819 1.5669C12.2193 1.5669 12.1566 1.5669 12.1566 1.62956C12.1566 1.69223 12.2193 1.69223 12.2819 1.69223C13.6605 1.94288 14.9764 2.38151 16.261 2.88281C17.5143 3.38411 18.5795 4.10473 19.3628 5.23266C19.8954 6.01594 20.3967 6.83055 20.804 7.70782C21.3367 8.89841 21.744 10.1203 21.9946 11.3736C22.12 11.8749 22.2139 12.2822 22.2766 12.7208Z"
          fill={color ?? '#15121A'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.4021 7.1123C11.5927 7.1123 12.5326 8.08357 12.5326 9.24283C12.5326 10.4334 11.5613 11.3734 10.3708 11.4047C9.18017 11.4047 8.24023 10.4334 8.24023 9.24283C8.24023 8.05224 9.2115 7.1123 10.4021 7.1123ZM11.906 9.27416C11.906 8.42821 11.248 7.77026 10.4021 7.77026C9.55615 7.77026 8.89819 8.42821 8.89819 9.27416C8.89819 10.0888 9.55615 10.7781 10.3708 10.7781H10.4021C11.2167 10.7781 11.8747 10.1201 11.906 9.27416Z"
          fill={color ?? '#15121A'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.1566 13.692C12.9712 14.1933 13.8798 14.2559 14.7884 14.2246C16.0416 14.1619 17.2322 13.88 18.3602 13.3787C18.7988 13.1907 19.2374 13.0027 19.6447 12.752C19.6761 12.7207 19.7074 12.6894 19.7701 12.7207C19.7387 12.8147 19.6447 12.846 19.5507 12.9087C18.3288 13.7233 16.9816 14.2246 15.5403 14.5066C14.7257 14.6632 13.8798 14.6632 13.0965 14.3813C12.6892 14.2246 12.3132 13.974 12.1566 13.692ZM12.0312 6.04716C12.6892 5.85917 13.3785 5.79651 14.0364 6.04716C14.5377 6.23514 14.9137 6.57979 15.0704 7.11242C14.1304 6.61112 13.0965 6.29781 12.0312 6.04716Z"
          fill={color ?? 'currentColor'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.0779 11.8433C17.8272 11.8433 17.6079 11.6239 17.6079 11.3733C17.6079 11.1226 17.7959 10.9033 18.0465 10.9033C18.2972 10.9033 18.5165 11.0913 18.5478 11.342C18.5165 11.6239 18.3285 11.8433 18.0779 11.8433Z"
          fill={color ?? '#15121A'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19.8952 12.6266C19.8952 12.658 19.8639 12.6893 19.8325 12.6893C19.8325 12.6266 19.8638 12.5953 19.8952 12.6266ZM19.9892 12.564L19.8952 12.6266L19.8639 12.5953C19.9265 12.564 19.9578 12.564 19.9892 12.564Z"
          fill={color ?? '#585A5B'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9057 9.274C11.9057 10.0886 11.2478 10.7779 10.4332 10.7779H10.4018C9.58724 10.7779 8.89795 10.1199 8.89795 9.274V9.24267C8.89795 8.39672 9.58724 7.73877 10.4018 7.73877C11.2165 7.73877 11.9057 8.42805 11.9057 9.274Z"
          fill={color ?? 'currentColor'}
        />
      </g>
      <defs>
        <clipPath id="clip0_1072_8439">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
