import { AssetProps } from './index'

export default function ConnectWallet({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M49.6931 19.8313C53.0075 19.8313 55.6944 17.1415 55.6944 13.8235C55.6944 10.5055 53.0075 7.81567 49.6931 7.81567C46.3787 7.81567 43.6919 10.5055 43.6919 13.8235C43.6919 17.1415 46.3787 19.8313 49.6931 19.8313Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M15.9433 53.4751C19.2577 53.4751 21.9446 50.7853 21.9446 47.4673C21.9446 44.1493 19.2577 41.4595 15.9433 41.4595C12.6289 41.4595 9.94208 44.1493 9.94208 47.4673C9.94208 50.7853 12.6289 53.4751 15.9433 53.4751Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M48.9291 82.2925L46.6777 84.5477C46.3909 84.8349 46.3909 85.2802 46.6777 85.5675C46.9645 85.8548 47.4091 85.8548 47.6959 85.5675L49.9472 83.3123C51.4815 81.7754 53.9909 81.7754 55.5253 83.3123C57.0596 84.8493 57.0596 87.363 55.5253 88.8999L47.7819 96.6565C46.2475 98.1934 43.7381 98.1934 42.2038 96.6565C41.5872 96.0388 41.2 95.2632 41.0853 94.4157C41.0279 94.0279 40.6694 93.755 40.2823 93.798C39.8951 93.8555 39.6226 94.2146 39.6657 94.6024C39.8234 95.7659 40.354 96.8288 41.1857 97.662C42.2325 98.7105 43.6091 99.242 44.9857 99.242C46.3623 99.242 47.7389 98.7105 48.7857 97.662L56.5291 89.9054C57.5472 88.8856 58.1064 87.5354 58.1064 86.099C58.1064 84.6626 57.5472 83.298 56.5291 82.2925C54.4355 80.1954 51.0226 80.1954 48.9147 82.2925H48.9291ZM20.4936 45.5495C20.4936 42.6623 18.6581 40.2061 16.1057 39.2437C20.1638 27.4078 29.4989 18.0856 41.3291 14.078C42.2755 16.6492 44.7275 18.5021 47.6241 18.5021C51.3381 18.5021 54.3638 15.4713 54.3638 11.7511C54.3638 8.0308 51.3381 5 47.6241 5C43.9102 5 40.8845 8.0308 40.8845 11.7511C40.8845 12.0671 40.9419 12.3687 40.9706 12.6704C28.6242 16.8216 18.8732 26.5316 14.686 38.899C14.3706 38.8559 14.0694 38.7984 13.7396 38.7984C10.0257 38.7984 7 41.8292 7 45.5495C7 49.2698 10.0257 52.3005 13.7396 52.3005C17.4536 52.3005 20.4792 49.2698 20.4792 45.5495H20.4936ZM47.6241 6.45076C50.5494 6.45076 52.9298 8.83518 52.9298 11.7654C52.9298 14.6957 50.5494 17.0801 47.6241 17.0801C45.3728 17.0801 43.4657 15.6581 42.6913 13.6758C44.3404 13.1875 46.0181 12.7565 47.7389 12.4836C48.126 12.4262 48.3985 12.0527 48.3268 11.6649C48.2694 11.2771 47.8966 11.0041 47.5094 11.076C45.76 11.3632 44.0536 11.7942 42.3758 12.2825C42.3615 12.1102 42.3185 11.9522 42.3185 11.7798C42.3185 8.84953 44.6989 6.46512 47.6241 6.46512V6.45076ZM13.754 50.8642C10.8287 50.8642 8.4483 48.4797 8.4483 45.5495C8.4483 42.6192 10.8287 40.2348 13.754 40.2348C13.9404 40.2348 14.0981 40.2635 14.2845 40.2923C13.7826 41.9729 13.3525 43.6678 13.0513 45.4346C12.9796 45.8224 13.2521 46.1959 13.6392 46.2677C13.6823 46.2677 13.7253 46.2677 13.754 46.2677C14.0981 46.2677 14.3992 46.0235 14.4566 45.6644C14.7434 43.9407 15.1736 42.2601 15.6755 40.6083C17.64 41.3839 19.0453 43.2943 19.0453 45.5495C19.0453 48.4797 16.6649 50.8642 13.7396 50.8642H13.754ZM113.902 25.7559H62.3509C58.4362 25.7559 55.2385 28.9447 55.2385 32.8805V66.9087C51.6392 65.3287 47.6815 64.4237 43.4943 64.4237C27.3766 64.4237 14.2558 77.5668 14.2558 93.7119C14.2558 109.857 27.3766 123 43.4943 123C59.6121 123 72.7328 109.857 72.7328 93.7119C72.7328 91.2556 72.403 88.8999 71.8294 86.6161H113.888C117.802 86.6161 121 83.4273 121 79.4915V32.8661C121 28.9447 117.817 25.7416 113.888 25.7416L113.902 25.7559ZM43.5087 121.564C28.1796 121.564 15.7042 109.067 15.7042 93.7119C15.7042 78.3568 28.1796 65.8601 43.5087 65.8601C58.8377 65.8601 71.3132 78.3568 71.3132 93.7119C71.3132 109.067 58.8377 121.564 43.5087 121.564ZM119.58 79.4915C119.58 82.6229 117.042 85.1797 113.902 85.1797H71.4709C70.8257 83.0538 69.9223 81.0429 68.8325 79.1468V76.1878C68.8325 75.7856 68.517 75.4696 68.1155 75.4696C67.714 75.4696 67.3985 75.7856 67.3985 76.1878V76.8917C64.6453 72.9847 60.96 69.7815 56.6725 67.5982V59.4825H76.2747C77.2642 59.4825 78.1819 60.014 78.5977 60.8184C80.4475 64.4094 84.0898 66.6358 88.1192 66.6358C92.1487 66.6358 95.7909 64.4094 97.6408 60.8184C98.0566 59.9996 98.9743 59.4825 99.9781 59.4825H119.58V79.5059V79.4915ZM119.58 58.0318H99.9781C98.4294 58.0318 97.0528 58.8362 96.3789 60.1433C94.7728 63.2459 91.6181 65.1707 88.1336 65.1707C84.649 65.1707 81.4943 63.2459 79.8883 60.1433C79.2143 58.8362 77.8377 58.0318 76.2891 58.0318H56.6868V52.3005C56.6868 49.1692 59.2249 46.6124 62.3653 46.6124H113.916C117.042 46.6124 119.595 49.1692 119.595 52.3005V58.0318H119.58ZM119.58 48.0632C118.29 46.3251 116.239 45.1904 113.902 45.1904H62.3509C60.0279 45.1904 57.9774 46.3251 56.6725 48.0632V42.5905C56.6725 39.4592 59.2106 36.9024 62.3509 36.9024H113.902C117.028 36.9024 119.58 39.4592 119.58 42.5905V48.0632ZM119.58 38.3388C118.29 36.6007 116.239 35.466 113.902 35.466H62.3509C60.0279 35.466 57.9774 36.6007 56.6725 38.3388V32.8661C56.6725 29.7348 59.2106 27.178 62.3509 27.178H113.902C117.028 27.178 119.58 29.7348 119.58 32.8661V38.3388ZM39.3215 101.828L37.0702 104.083C35.5789 105.577 32.9834 105.577 31.4921 104.083C30.7464 103.336 30.3306 102.345 30.3306 101.282C30.3306 100.219 30.7464 99.2276 31.4921 98.4807L39.2355 90.7242C40.7698 89.1872 43.2792 89.1872 44.8136 90.7242C45.4302 91.3418 45.8174 92.1175 45.9321 92.9649C45.9894 93.3528 46.3479 93.6257 46.7351 93.5826C47.1223 93.5251 47.3947 93.166 47.3517 92.7782C47.194 91.6147 46.6634 90.5518 45.8317 89.7187C43.7381 87.6216 40.3253 87.6216 38.2174 89.7187L30.474 97.4752C29.4558 98.4951 28.8966 99.8453 28.8966 101.282C28.8966 102.718 29.4558 104.068 30.474 105.088C31.4921 106.108 32.84 106.668 34.274 106.668C35.7079 106.668 37.0558 106.108 38.074 105.088L40.3253 102.833C40.6121 102.546 40.6121 102.1 40.3253 101.813C40.0385 101.526 39.594 101.526 39.3072 101.813L39.3215 101.828ZM73.12 75.4409C72.7185 75.4409 72.403 75.7569 72.403 76.1591V80.5976C72.403 80.9998 72.7185 81.3158 73.12 81.3158C73.5215 81.3158 73.837 80.9998 73.837 80.5976V76.1591C73.837 75.7569 73.5215 75.4409 73.12 75.4409ZM93.1238 75.4409C92.7223 75.4409 92.4068 75.7569 92.4068 76.1591V80.5976C92.4068 80.9998 92.7223 81.3158 93.1238 81.3158C93.5253 81.3158 93.8408 80.9998 93.8408 80.5976V76.1591C93.8408 75.7569 93.5253 75.4409 93.1238 75.4409ZM108.123 81.3158C108.525 81.3158 108.84 80.9998 108.84 80.5976V76.1591C108.84 75.7569 108.525 75.4409 108.123 75.4409C107.722 75.4409 107.406 75.7569 107.406 76.1591V80.5976C107.406 80.9998 107.722 81.3158 108.123 81.3158ZM113.128 81.3158C113.529 81.3158 113.845 80.9998 113.845 80.5976V76.1591C113.845 75.7569 113.529 75.4409 113.128 75.4409C112.726 75.4409 112.411 75.7569 112.411 76.1591V80.5976C112.411 80.9998 112.726 81.3158 113.128 81.3158ZM103.118 81.3158C103.52 81.3158 103.835 80.9998 103.835 80.5976V76.1591C103.835 75.7569 103.52 75.4409 103.118 75.4409C102.717 75.4409 102.402 75.7569 102.402 76.1591V80.5976C102.402 80.9998 102.717 81.3158 103.118 81.3158ZM78.1102 75.4409C77.7087 75.4409 77.3932 75.7569 77.3932 76.1591V80.5976C77.3932 80.9998 77.7087 81.3158 78.1102 81.3158C78.5117 81.3158 78.8272 80.9998 78.8272 80.5976V76.1591C78.8272 75.7569 78.5117 75.4409 78.1102 75.4409ZM83.1147 75.4409C82.7132 75.4409 82.3977 75.7569 82.3977 76.1591V80.5976C82.3977 80.9998 82.7132 81.3158 83.1147 81.3158C83.5162 81.3158 83.8317 80.9998 83.8317 80.5976V76.1591C83.8317 75.7569 83.5162 75.4409 83.1147 75.4409ZM98.114 75.4409C97.7124 75.4409 97.397 75.7569 97.397 76.1591V80.5976C97.397 80.9998 97.7124 81.3158 98.114 81.3158C98.5155 81.3158 98.8309 80.9998 98.8309 80.5976V76.1591C98.8309 75.7569 98.5155 75.4409 98.114 75.4409ZM88.1049 75.4409C87.7034 75.4409 87.3879 75.7569 87.3879 76.1591V80.5976C87.3879 80.9998 87.7034 81.3158 88.1049 81.3158C88.5064 81.3158 88.8219 80.9998 88.8219 80.5976V76.1591C88.8219 75.7569 88.5064 75.4409 88.1049 75.4409Z"
        fill={color ?? 'url(#paint0_linear_1060_1147)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1060_1147"
          x1="30.8181"
          y1="120.17"
          x2="93.0381"
          y2="12.575"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
