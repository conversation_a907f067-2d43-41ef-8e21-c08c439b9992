import { AssetProps } from './index'

export default function KakaoTalkMonoLogo({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_1072_8458)">
        <path
          d="M24 22.125C24 23.1606 23.1606 24 22.125 24H1.875C0.839438 24 0 23.1606 0 22.125V1.875C0 0.839438 0.839438 0 1.875 0H22.125C23.1606 0 24 0.839438 24 1.875V22.125Z"
          fill={color ?? 'currentColor'}
        />
        <path
          d="M12 3.375C6.61519 3.375 2.25 6.81684 2.25 11.0625C2.25 13.8074 4.07494 16.2159 6.82013 17.576C6.67078 18.091 5.86041 20.8895 5.82816 21.1093C5.82816 21.1093 5.80875 21.2745 5.91572 21.3375C6.02269 21.4005 6.1485 21.3516 6.1485 21.3516C6.45525 21.3087 9.70566 19.0255 10.2682 18.6291C10.8303 18.7087 11.409 18.75 12 18.75C17.3848 18.75 21.75 15.3082 21.75 11.0625C21.75 6.81684 17.3848 3.375 12 3.375Z"
          fill={color ?? '#15121A'}
        />
        <path
          d="M6.60938 13.7461C6.29916 13.7461 6.04688 13.5052 6.04688 13.2089V9.86719H5.16919C4.86478 9.86719 4.61719 9.62006 4.61719 9.31641C4.61719 9.01275 4.86488 8.76562 5.16919 8.76562H8.04956C8.35397 8.76562 8.60156 9.01275 8.60156 9.31641C8.60156 9.62006 8.35387 9.86719 8.04956 9.86719H7.17188V13.2089C7.17188 13.5052 6.91959 13.7461 6.60938 13.7461ZM11.5417 13.7388C11.3072 13.7388 11.1278 13.6435 11.0737 13.4903L10.7951 12.7612L9.07987 12.7611L8.80116 13.4907C8.74725 13.6436 8.56791 13.7388 8.33334 13.7388C8.20996 13.7389 8.088 13.7124 7.97578 13.6612C7.82072 13.5896 7.67166 13.3929 7.84247 12.8624L9.18797 9.32091C9.28275 9.05156 9.57066 8.77406 9.93703 8.76572C10.3044 8.77397 10.5923 9.05156 10.6873 9.32147L12.0322 12.8614C12.2034 13.3931 12.0544 13.5899 11.8993 13.6613C11.7871 13.7124 11.6651 13.7389 11.5417 13.7388C11.5417 13.7388 11.5417 13.7388 11.5417 13.7388ZM10.4993 11.7648L9.9375 10.1687L9.37566 11.7648H10.4993ZM12.9375 13.6641C12.6402 13.6641 12.3984 13.4327 12.3984 13.1484V9.32812C12.3984 9.01791 12.6561 8.76562 12.9727 8.76562C13.2893 8.76562 13.5469 9.01791 13.5469 9.32812V12.6328H14.7422C15.0395 12.6328 15.2812 12.8642 15.2812 13.1484C15.2812 13.4327 15.0395 13.6641 14.7422 13.6641H12.9375ZM16.0626 13.7388C15.7523 13.7388 15.5001 13.4865 15.5001 13.1763V9.32812C15.5001 9.01791 15.7523 8.76562 16.0626 8.76562C16.3728 8.76562 16.6251 9.01791 16.6251 9.32812V10.5371L18.1944 8.96775C18.2752 8.88703 18.3861 8.84259 18.5064 8.84259C18.6469 8.84259 18.7879 8.90316 18.8935 9.00872C18.9921 9.10716 19.0508 9.23381 19.0589 9.36534C19.0671 9.498 19.0229 9.61959 18.9348 9.70781L17.6529 10.9895L19.0375 12.8238C19.0822 12.8826 19.1148 12.9498 19.1333 13.0213C19.1519 13.0928 19.156 13.1673 19.1455 13.2405C19.1355 13.3137 19.111 13.3842 19.0736 13.4479C19.0361 13.5115 18.9864 13.5671 18.9273 13.6115C18.83 13.6854 18.7111 13.7253 18.5889 13.725C18.5018 13.7254 18.4157 13.7054 18.3377 13.6666C18.2597 13.6277 18.1918 13.5712 18.1396 13.5014L16.8204 11.7535L16.6252 11.9487V13.176C16.6251 13.3252 16.5657 13.4682 16.4603 13.5737C16.3548 13.6792 16.2118 13.7386 16.0626 13.7388Z"
          fill={color ?? 'currentColor'}
        />
      </g>
      <defs>
        <clipPath id="clip0_1072_8458">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
