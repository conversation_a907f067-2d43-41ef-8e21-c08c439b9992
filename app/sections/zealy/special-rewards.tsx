import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'

import { useZealyNftMinterContract } from 'modules/tenset-web3/lib/hooks/zealy'
import { H2, But<PERSON>, TextL } from '~/tenset-components'
import { NftInfoComponent } from '~/components/zealy-rewards/nft-info'
import { NftTableComponent } from '~/components/zealy-rewards/nft-table'
import { Namespace } from '~/i18n'
import { zealyRewards } from '~/assets/images'
import { useWallet } from '~/tenset-web3'

export function SpecialRewards({
  handleClaim,
  isDataLoading,
  tokenId,
  isNftClaimed,
}: {
  handleClaim: () => Promise<void>
  isDataLoading: boolean
  tokenId: string | null
  isNftClaimed: boolean
}) {
  const { t } = useTranslation([Namespace.ZEALY, Namespace.COMMON])
  const { wallet } = useWallet()

  const { zealyNftMinterContract } = useZealyNftMinterContract()
  const [isClaimed, setIsClaimed] = useState(false)
  useEffect(() => {
    if (!wallet || !zealyNftMinterContract || !tokenId) return
    wallet
      .interact(zealyNftMinterContract)
      .ownerOf(tokenId)
      .then(() => {
        setIsClaimed(true)
      })
      .catch(error => {
        console.error('Error while checking if claimed', error)
        setIsClaimed(false)
      })
  }, [wallet, zealyNftMinterContract, wallet?.account, tokenId])

  const timeToClaim = new Date('2023-11-11T11:00:00+02:00')
  const isToLateToClaim = timeToClaim.getTime() < Date.now()
  return (
    <div className="grid gap-6">
      <H2 isBold>{t('special-rewards.title')}</H2>

      {tokenId ? (
        <>
          <NftInfoComponent
            shortName="Zealy NFT"
            longName="Tenset Zealy Reward"
            img={zealyRewards}
          />
          <NftTableComponent
            isDataLoading={false}
            rewardName={
              isClaimed || isNftClaimed
                ? 'CLAIMED Tenset Zealy Reward'
                : 'Tenset Zealy Reward'
            }
            timeToClaim={isClaimed || isNftClaimed ? null : timeToClaim}
          />
        </>
      ) : (
        <TextL className="text-blue-500">No rewards to claim</TextL>
      )}
      <div>
        <Button
          onClick={handleClaim}
          disabled={
            isToLateToClaim ||
            isDataLoading ||
            isClaimed ||
            !tokenId ||
            isNftClaimed
          }
          loading={isDataLoading}
        >
          {t('special-rewards.button')}
        </Button>
      </div>
    </div>
  )
}
