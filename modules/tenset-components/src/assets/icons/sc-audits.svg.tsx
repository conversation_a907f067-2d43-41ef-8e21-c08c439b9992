import { AssetProps } from './index'

export default function ScAudits({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_742_762)">
        <path
          d="M111.525 97.3069L110.504 98.3278C113.292 95.1863 115.007 91.05 115.007 86.508C115.007 76.6646 107.022 68.68 97.179 68.68C96.891 68.68 96.6031 68.7062 96.3151 68.7193V63.4704H123.973C125.636 63.4704 126.984 62.1222 126.984 60.4598C126.984 59.321 126.356 58.3524 125.426 57.8288C125.465 57.5408 125.518 57.2398 125.518 56.9387C125.518 53.8103 123.319 51.2055 120.387 50.5641C120.347 45.577 116.303 41.5454 111.303 41.5454C108.213 41.5454 105.491 43.1031 103.842 45.4592C102.716 44.844 101.433 44.4644 100.072 44.4644C98.7105 44.4644 97.4408 44.8047 96.3151 45.4068V33.1419H113.75C113.75 28.325 109.85 24.4243 105.033 24.4243H42.7003C37.8834 24.4243 33.9827 28.325 33.9827 33.1419V33.6655C33.3544 33.4561 32.6868 33.3383 31.9931 33.3383C30.9066 33.3383 29.9118 33.6393 29.0218 34.1236C27.7259 32.2649 25.5792 31.0476 23.1445 31.0476C19.2046 31.0476 16.0107 34.2284 15.9846 38.1552C13.6677 38.6657 11.9399 40.7077 11.9399 43.1816C11.9399 43.4172 11.9792 43.6528 12.0053 43.8753C11.2723 44.2811 10.7749 45.0534 10.7749 45.9435C10.7749 47.2524 11.8352 48.3258 13.1572 48.3258H33.9827V106.43H16.5474C16.5474 111.247 20.4481 115.148 25.2651 115.148H87.5975C92.4144 115.148 96.3151 111.247 96.3151 106.43V104.297C96.6031 104.31 96.891 104.336 97.179 104.336C101.721 104.336 105.844 102.621 108.999 99.8331L107.978 100.854L127.193 120.07L130.741 116.522L111.525 97.3069Z"
          fill={color ?? '#1A1A1A'}
        />
        <path
          d="M54.7427 99.7677H42.5171C42.1506 99.7677 41.8626 100.056 41.8626 100.422C41.8626 100.789 42.1506 101.077 42.5171 101.077H54.7427C55.1092 101.077 55.3972 100.789 55.3972 100.422C55.3972 100.056 55.1092 99.7677 54.7427 99.7677ZM106.028 83.8115C105.674 83.7592 105.334 84.0209 105.295 84.3744C105.137 85.5655 104.797 86.7305 104.287 87.83C104.13 88.1572 104.287 88.5499 104.601 88.6939C104.692 88.7332 104.784 88.7594 104.876 88.7594C105.124 88.7594 105.36 88.6154 105.465 88.3798C106.028 87.1624 106.407 85.8797 106.577 84.5576C106.63 84.2042 106.368 83.8639 106.014 83.8246L106.028 83.8115ZM127.337 112.294L108.41 93.3669C110.53 90.3563 111.8 86.6912 111.8 82.7382C111.8 72.5414 103.514 64.2557 93.3176 64.2557C93.2521 64.2557 93.1736 64.2557 93.1081 64.2557V60.3551H120.112C122.141 60.3551 123.777 58.7058 123.777 56.69C123.777 55.525 123.214 54.4255 122.272 53.7448C122.298 53.5616 122.311 53.3783 122.311 53.182C122.311 50.0012 120.177 47.187 117.154 46.2969C116.853 41.192 112.598 37.1342 107.428 37.1342C104.444 37.1342 101.616 38.5217 99.7707 40.8647C97.6109 39.8568 95.2025 39.8176 93.095 40.6291V30.0266H109.876C110.242 30.0266 110.53 29.7386 110.53 29.3721C110.53 24.2017 106.329 20 101.158 20H38.8389C33.7733 20 29.637 24.0447 29.4799 29.0711C28.1186 28.7962 26.6787 28.9271 25.3436 29.5161C23.8645 27.6836 21.6392 26.6102 19.27 26.6102C15.1599 26.6102 11.7697 29.8172 11.4818 33.8618C9.09947 34.6079 7.41092 36.8593 7.41092 39.3856C7.41092 39.5034 7.41092 39.6343 7.43709 39.7652C6.70408 40.3411 6.25904 41.2181 6.25904 42.1475C6.25904 43.823 7.62035 45.1843 9.29581 45.1843H29.4668V101.98H12.8823C12.8823 101.98 12.8169 101.993 12.7776 102.006C12.7384 102.006 12.7122 101.98 12.6729 101.98C12.3064 101.98 12.0184 102.268 12.0184 102.634C12.0184 107.805 16.2202 112.006 21.3905 112.006H83.736C88.9064 112.006 93.1081 107.805 93.1081 102.634V101.194C93.1736 101.194 93.2521 101.194 93.3176 101.194C97.2706 101.194 100.936 99.9378 103.946 97.8042L122.874 116.732C123.005 116.863 123.175 116.928 123.332 116.928C123.489 116.928 123.672 116.863 123.79 116.732L127.337 113.184C127.599 112.923 127.599 112.517 127.337 112.255V112.294ZM99.666 42.2653C99.954 42.4224 100.32 42.3438 100.517 42.069C102.101 39.7914 104.692 38.4432 107.441 38.4432C112.049 38.4432 115.832 42.1999 115.871 46.8205C115.871 47.1215 116.093 47.3964 116.381 47.4488C119.052 48.0378 121.002 50.4463 121.002 53.182C121.002 53.4045 120.976 53.627 120.937 53.8365V53.9804C120.884 54.2422 121.015 54.504 121.251 54.6349C122.01 55.0538 122.468 55.8392 122.468 56.69C122.468 57.9989 121.408 59.0461 120.112 59.0461H93.1081V42.0559C95.1239 41.1003 97.5455 41.1134 99.6791 42.2653H99.666ZM101.158 21.309C105.386 21.309 108.855 24.5813 109.182 28.7176H48.1717C47.9492 25.5631 46.169 22.8404 43.5904 21.309H101.171H101.158ZM9.29581 43.8884C8.34027 43.8884 7.56799 43.1161 7.56799 42.1606C7.56799 41.5454 7.90832 40.9694 8.45808 40.6684C8.69369 40.5375 8.8115 40.1579 8.77223 39.8961C8.74605 39.739 8.71987 39.5689 8.71987 39.3987C8.71987 37.3044 10.199 35.4587 12.254 35.0137C12.5551 34.9483 12.7645 34.6865 12.7645 34.3854C12.7907 30.8251 15.7097 27.9192 19.27 27.9192C21.3905 27.9192 23.3932 28.9663 24.6106 30.7203C24.8069 30.9952 25.1734 31.0868 25.4614 30.9167C26.7573 30.2098 28.1578 30.0789 29.4668 30.4062V43.8884H9.29581ZM13.3797 103.302H29.4406C29.1003 107.438 25.6316 110.711 21.4167 110.711C17.2019 110.711 13.7201 107.438 13.3928 103.302H13.3797ZM91.7992 102.647C91.7992 107.098 88.1865 110.711 83.736 110.711H26.1551C28.917 109.074 30.7757 106.077 30.7757 102.647V29.3721C30.7757 24.9217 34.3885 21.309 38.8389 21.309C43.2893 21.309 46.9021 24.9217 46.9021 29.3721C46.9021 29.7386 47.19 30.0266 47.5565 30.0266H91.7992V64.3343C84.2334 64.9495 77.9374 70.146 75.7121 77.1358C75.7121 77.1489 75.7121 77.162 75.6991 77.1751C75.5551 77.6071 75.4503 78.0652 75.3456 78.5102H42.5302C42.1636 78.5102 41.8757 78.7982 41.8757 79.1647C41.8757 79.5312 42.1636 79.8192 42.5302 79.8192H75.0838C74.9268 80.7747 74.8351 81.7434 74.8351 82.7382C74.8351 83.6806 74.9268 84.5969 75.0708 85.5132H42.5302C42.1636 85.5132 41.8757 85.8011 41.8757 86.1676C41.8757 86.5341 42.1636 86.8221 42.5302 86.8221H75.3064C75.4111 87.2802 75.5158 87.7384 75.6598 88.1834C77.8457 95.2649 84.1811 100.527 91.7992 101.155V102.66V102.647ZM94.5742 99.8462C94.1553 99.8724 93.7495 99.9116 93.3176 99.9116C85.8958 99.9116 79.5866 95.1732 77.1913 88.5761H82.1784C82.5449 88.5761 82.8328 88.2881 82.8328 87.9216C82.8328 87.5551 82.5449 87.2672 82.1784 87.2672H76.7724C76.3797 85.8273 76.1441 84.3089 76.1441 82.7382C76.1441 81.1674 76.3797 79.5443 76.8117 78.0521H82.6234C82.9899 78.0521 83.2779 77.7641 83.2779 77.3976C83.2779 77.0311 82.9899 76.7432 82.6234 76.7432H77.2567C79.6914 70.2246 85.9743 65.5647 93.3307 65.5647C93.7495 65.5647 94.1684 65.604 94.5873 65.6301V99.8462H94.5742ZM95.8831 99.7022V65.7741C104.143 67.0176 110.491 74.1383 110.491 82.7382C110.491 87.1493 108.802 91.1547 106.067 94.2046L104.784 95.4874C102.336 97.6864 99.2733 99.1917 95.8831 99.7022ZM123.345 115.383L105.046 97.0843L105.726 96.4036C106.172 95.9979 106.59 95.579 106.996 95.134L107.677 94.4533L125.976 112.752L123.358 115.37L123.345 115.383ZM12.3064 86.4556V84.1911C15.0945 83.3272 17.0448 81.3507 17.0448 79.0731C17.0448 75.7745 12.9871 73.8635 9.19109 73.4839C6.59936 73.1959 3.82438 72.0571 3.82438 69.7141C3.82438 67.6328 6.20668 65.9443 9.12565 65.9443C11.2331 65.9443 13.1441 66.8475 13.995 68.235C14.1782 68.5491 14.584 68.6407 14.8981 68.4575C15.2123 68.2742 15.3039 67.8685 15.1207 67.5543C14.5185 66.5726 13.5237 65.7872 12.3195 65.2767V62.8552C12.3195 62.4886 12.0315 62.2007 11.665 62.2007C11.2985 62.2007 11.0105 62.4886 11.0105 62.8552V64.8448C10.4084 64.7139 9.78012 64.6353 9.12565 64.6353C8.75914 64.6353 8.40572 64.6746 8.0523 64.7139V62.8421C8.0523 62.4756 7.76433 62.1876 7.39783 62.1876C7.03132 62.1876 6.74335 62.4756 6.74335 62.8421V64.9757C4.28251 65.7087 2.51543 67.5412 2.51543 69.701C2.51543 72.3843 5.01553 74.3216 9.07329 74.7666C12.3849 75.107 15.7228 76.6777 15.7228 79.06C15.7228 81.4423 12.7645 83.3403 9.13873 83.3403C6.62554 83.3403 4.28251 82.3717 3.18299 80.8795C2.97356 80.5915 2.5547 80.526 2.26673 80.7355C1.97876 80.9449 1.91331 81.3638 2.12274 81.6517C3.10446 82.9869 4.76683 83.9424 6.73026 84.3875V86.4425C6.73026 86.809 7.01823 87.097 7.38474 87.097C7.75124 87.097 8.03921 86.809 8.03921 86.4425V84.61C8.39263 84.6492 8.75914 84.6623 9.13873 84.6623C9.78012 84.6623 10.3953 84.5969 10.9974 84.5053V86.4556C10.9974 86.8221 11.2854 87.1101 11.6519 87.1101C12.0184 87.1101 12.3064 86.8221 12.3064 86.4556ZM48.1456 60.6561H55.2794C55.6459 60.6561 55.9338 60.3681 55.9338 60.0016V58.5618L65.0442 61.664V67.7507C65.0442 68.1172 65.3321 68.4051 65.6986 68.4051H75.4765C75.843 68.4051 76.131 68.1172 76.131 67.7507V57.9728C76.131 57.6063 75.843 57.3183 75.4765 57.3183H69.2066L66.5364 50.1845H69.2721C69.6386 50.1845 69.9266 49.8965 69.9266 49.53V37.7887C69.9266 37.4222 69.6386 37.1342 69.2721 37.1342H57.5308C57.1643 37.1342 56.8763 37.4222 56.8763 37.7887V49.53C56.8763 49.6216 56.8894 49.7002 56.9286 49.7787L54.6903 52.2265H48.1587C47.7921 52.2265 47.5042 52.5144 47.5042 52.8809V60.0147C47.5042 60.3812 47.7921 60.6692 48.1587 60.6692L48.1456 60.6561ZM68.7616 58.6141H74.8221V67.0831H66.3531V58.6141H68.7616ZM58.1852 38.4301H68.6176V48.8624H58.1852V38.4301ZM58.3292 50.1714H65.1489L67.8191 57.3052H65.6986C65.3321 57.3052 65.0442 57.5932 65.0442 57.9597V60.2765L55.9338 57.1743V52.8678C55.9338 52.8678 55.9338 52.8286 55.9208 52.8024L58.3292 50.1714ZM48.8131 53.5223H54.638V59.3472H48.8131V53.5223ZM54.7558 93.1574H42.5302C42.1636 93.1574 41.8757 93.4454 41.8757 93.8119C41.8757 94.1784 42.1636 94.4664 42.5302 94.4664H54.7558C55.1223 94.4664 55.4103 94.1784 55.4103 93.8119C55.4103 93.4454 55.1223 93.1574 54.7558 93.1574ZM105.36 79.1778C105.425 79.1778 105.504 79.1778 105.582 79.1385C105.923 79.0207 106.106 78.6411 105.988 78.3008C104.705 74.6227 101.878 71.6906 98.2523 70.2638C97.912 70.1329 97.5324 70.29 97.4015 70.6303C97.2706 70.9707 97.4277 71.3503 97.768 71.4812C101.04 72.777 103.58 75.4211 104.745 78.7328C104.836 79.0077 105.098 79.1647 105.36 79.1647V79.1778Z"
          fill="url(#paint0_linear_742_762)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_742_762"
          x1="39.4672"
          y1="123.905"
          x2="100.517"
          y2="18.1806"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
        <clipPath id="clip0_742_762">
          <rect width="130" height="130" rx="10" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
