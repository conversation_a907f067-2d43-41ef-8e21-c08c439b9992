import { Meta } from '@storybook/react'

import { DividerColor } from '../Divider'

import { ListTableItem } from './item'

import { ListTable } from '.'

type ListTableType = typeof ListTable

export default {
  title: 'Components/ListTable/ListTable',
  component: ListTable,
  args: {
    elements: [
      <ListTableItem key={0}>
        <div>1</div>
        <div>2</div>
      </ListTableItem>,
      <ListTableItem key={1}>
        <div>3</div>
        <div>4</div>
      </ListTableItem>,
    ],
    className: '',
    dividerColor: DividerColor.NEUTRAL400,
    withHeaders: false,
  },
} as Meta<ListTableType>

export const Preview = {}
