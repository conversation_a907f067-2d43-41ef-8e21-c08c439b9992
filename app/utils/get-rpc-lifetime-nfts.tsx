import { BigNumber } from 'ethers'

import type { GlpNft } from '~/api/types'
import { regularNft } from '~/assets/videos'
import { BSC_NETWORK } from '~/config'
import type { GlpLifetimeMembershipContract } from '~/tenset-web3'

interface GetRpcLifetimeNfts {
  walletAddress: string
  glpLifetimeMembershipContract: GlpLifetimeMembershipContract
  lifetimeMembershipBalance: BigNumber
}

export const getRpcLifetimeNfts = async ({
  walletAddress,
  glpLifetimeMembershipContract,
  lifetimeMembershipBalance,
}: GetRpcLifetimeNfts): Promise<GlpNft[]> => {
  return new Promise(async resolve => {
    const tokenIndexCalls = []
    for (
      let tokenId = BigNumber.from(0);
      tokenId.lt(lifetimeMembershipBalance);
      tokenId = tokenId.add(1)
    ) {
      tokenIndexCalls.push(
        glpLifetimeMembershipContract.tokenOfOwnerByIndex(
          walletAddress,
          tokenId
        )
      )
    }

    const tokenIds = await Promise.all(tokenIndexCalls)

    resolve(
      tokenIds.map(tokenId => ({
        id: tokenId.toNumber(),
        chainId: BSC_NETWORK,
        collection: 'Regular NFT',
        contractAddress: glpLifetimeMembershipContract.address,
        tokenName: 'Regular NFT',
        image: regularNft,
        isImageVideo: true,
      }))
    )
  })
}
