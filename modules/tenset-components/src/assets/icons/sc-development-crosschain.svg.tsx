import { AssetProps } from './index'

export default function ScDevelopmentCrosschain({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M108.557 78.0848L105.408 86.1779L118.398 91.2164L121.547 83.1232L108.557 78.0848Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M88.0217 104.324L80.0634 107.824L85.6805 120.557L93.6388 117.058L88.0217 104.324Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M46.7952 105.242L41.7488 118.212L49.8546 121.356L54.901 108.386L46.7952 105.242Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M25.1084 79.9307L12.3547 85.539L15.8597 93.4849L28.6134 87.8766L25.1084 79.9307Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M14.7031 41.6706L11.5541 49.7637L24.5442 54.8022L27.6932 46.709L14.7031 41.6706Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M47.4391 12.3352L39.4808 15.8348L45.0979 28.5686L53.0562 25.069L47.4391 12.3352Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M83.254 11.5346L78.2076 24.5044L86.3135 27.6484L91.3598 14.6786L83.254 11.5346Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M117.254 39.4116L104.5 45.0199L108.005 52.9658L120.759 47.3575L117.254 39.4116Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M66.5568 28.5958C45.6096 28.5958 28.6388 45.5524 28.6388 66.4545C28.6388 87.3565 45.622 104.313 66.5568 104.313C87.4915 104.313 104.475 87.3565 104.475 66.4545C104.475 45.5524 87.4915 28.5958 66.5568 28.5958ZM66.5568 99.0073C48.5578 99.0073 33.9654 84.4377 33.9654 66.4669C33.9654 48.496 48.5578 33.9264 66.5568 33.9264C84.5557 33.9264 99.1481 48.496 99.1481 66.4669C99.1481 84.4377 84.5557 99.0073 66.5568 99.0073Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M84.2956 13.952C83.9735 13.8283 83.6143 13.9891 83.4904 14.3107L81.0501 20.5936C80.9262 20.9152 81.0873 21.2739 81.4093 21.3976C81.4837 21.4223 81.558 21.4347 81.6323 21.4347C81.8801 21.4347 82.1154 21.2863 82.2145 21.0389L84.6548 14.7559C84.7787 14.4343 84.6177 14.0757 84.2956 13.952ZM107.609 46.2327C107.695 46.2327 107.782 46.2203 107.857 46.1832L114.038 43.4622C114.348 43.3262 114.496 42.9551 114.36 42.6459C114.224 42.3367 113.852 42.1883 113.542 42.3244L107.361 45.0453C107.051 45.1814 106.903 45.5524 107.039 45.8616C107.138 46.0966 107.373 46.2327 107.609 46.2327ZM43.5409 15.1888C43.4047 14.8796 43.0331 14.7312 42.7234 14.8672C42.4137 15.0033 42.265 15.3743 42.4013 15.6835L45.1265 21.8552C45.2256 22.0902 45.461 22.2262 45.6964 22.2262C45.7831 22.2262 45.8698 22.2139 45.9441 22.1768C46.2538 22.0407 46.4024 21.6697 46.2662 21.3605L43.5409 15.1888ZM120.182 80.9499C120.12 80.8014 119.996 80.6778 119.835 80.6283L106.841 75.5821C106.519 75.4584 106.159 75.6192 106.036 75.9408L104.685 79.4162L100.746 77.8949C102.295 73.7269 103.149 69.2373 103.149 64.5374C103.149 59.2439 102.072 54.1977 100.127 49.5968L103.992 47.9024L105.491 51.3036C105.59 51.5386 105.825 51.6746 106.06 51.6746C106.147 51.6746 106.234 51.6623 106.308 51.6251L119.067 46.01C119.216 45.9482 119.34 45.8245 119.389 45.6637C119.451 45.5153 119.439 45.3422 119.389 45.1937L115.884 37.2534C115.747 36.9442 115.376 36.7958 115.066 36.9319L102.307 42.547C102.158 42.6088 102.034 42.7325 101.985 42.8933C101.923 43.0417 101.935 43.2149 101.985 43.3633L103.484 46.7645L99.6065 48.4713C95.5186 39.6405 88.1853 32.6031 79.1301 28.905L80.6661 24.972L84.147 26.3201C84.2213 26.3448 84.2956 26.3572 84.3699 26.3572C84.4566 26.3572 84.5434 26.3448 84.6177 26.3077C84.7663 26.2459 84.8902 26.1222 84.9398 25.9614L89.9938 12.9873C90.1177 12.6657 89.9567 12.307 89.6346 12.1834L81.5332 9.04187C81.3846 8.98003 81.2111 8.9924 81.0625 9.04187C80.9138 9.10371 80.79 9.22739 80.7404 9.38817L75.6863 22.3623C75.5625 22.6839 75.7235 23.0425 76.0456 23.1662L79.5265 24.5143L78.0028 28.4474C73.8282 26.9014 69.3316 26.048 64.6244 26.048C59.3225 26.048 54.2685 27.124 49.6603 29.0658L47.9633 25.207L51.3698 23.7104C51.5185 23.6486 51.6423 23.5249 51.6919 23.3641C51.7538 23.2157 51.7414 23.0425 51.6919 22.8941L46.068 10.155C45.9317 9.84579 45.5601 9.69738 45.2504 9.83343L37.2977 13.3336C37.149 13.3954 37.0252 13.5191 36.9756 13.6799C36.9137 13.8283 36.9261 14.0015 36.9756 14.1499L42.5995 26.889C42.6986 27.124 42.934 27.26 43.1693 27.26C43.256 27.26 43.3427 27.2477 43.4171 27.2106L46.8236 25.714L48.5331 29.5852C39.6885 33.6667 32.64 40.9886 28.9362 50.0297L24.997 48.496L26.3472 45.0206C26.4091 44.8722 26.3967 44.699 26.3472 44.5506C26.2852 44.4022 26.1614 44.2785 26.0003 44.229L13.0059 39.1829C12.6838 39.0592 12.3246 39.22 12.2007 39.5415L9.04194 47.6179C8.98 47.7663 8.99239 47.9395 9.04194 48.0879C9.10387 48.2363 9.22775 48.36 9.38878 48.4095L22.3832 53.4556C22.4575 53.4804 22.5319 53.4927 22.6062 53.4927C22.8539 53.4927 23.0893 53.3443 23.1884 53.0969L24.5386 49.6215L28.4778 51.1428C26.9294 55.3108 26.0747 59.8004 26.0747 64.5003C26.0747 69.7938 27.1524 74.84 29.0972 79.4409L25.2323 81.1354L23.7334 77.7342C23.6715 77.5857 23.5476 77.4621 23.3866 77.4126C23.2379 77.3507 23.0645 77.3631 22.9159 77.4126L10.1568 83.0277C9.84712 83.1637 9.69847 83.5348 9.83473 83.844L13.3404 91.7843C13.4023 91.9327 13.5262 92.0564 13.6872 92.1059C13.7615 92.1306 13.8359 92.143 13.9102 92.143C13.9969 92.143 14.0836 92.1306 14.1579 92.0935L26.917 86.4784C27.2267 86.3423 27.3753 85.9713 27.2391 85.6621L25.7402 82.2609L29.6175 80.5541C33.7053 89.3849 41.0387 96.4223 50.0939 100.12L48.5579 104.053L45.077 102.705C44.7549 102.582 44.3957 102.742 44.2718 103.064L39.2177 116.038C39.1558 116.186 39.1682 116.36 39.2177 116.508C39.2797 116.656 39.4035 116.78 39.5646 116.83L47.666 119.971C47.7403 119.996 47.8146 120.008 47.8889 120.008C48.1367 120.008 48.372 119.86 48.4711 119.612L53.5252 106.638C53.5872 106.49 53.5748 106.317 53.5252 106.168C53.4633 106.02 53.3394 105.896 53.1784 105.847L49.6975 104.499L51.2212 100.566C55.3957 102.112 59.8924 102.965 64.5996 102.965C69.9014 102.965 74.9555 101.889 79.5636 99.9472L81.2607 103.806L77.8541 105.303C77.7055 105.364 77.5816 105.488 77.5321 105.649C77.4701 105.797 77.4825 105.97 77.5321 106.119L83.156 118.858C83.2551 119.093 83.4904 119.229 83.7258 119.229C83.8125 119.229 83.8992 119.217 83.9735 119.18L91.9263 115.679C92.236 115.543 92.3846 115.172 92.2483 114.863L86.6245 102.124C86.4882 101.815 86.1166 101.666 85.8069 101.802L82.3879 103.299L80.6785 99.4278C89.5231 95.3463 96.5716 88.0244 100.275 78.9833L104.215 80.517L102.864 83.9924C102.741 84.314 102.902 84.6726 103.224 84.7963L116.218 89.8425C116.292 89.8672 116.367 89.8796 116.441 89.8796C116.528 89.8796 116.614 89.8672 116.689 89.8301C116.837 89.7683 116.961 89.6446 117.011 89.4838L120.157 81.3951C120.219 81.2467 120.207 81.0735 120.157 80.9251L120.182 80.9499ZM103.372 43.4127L114.992 38.3047L118.002 45.1195L106.382 50.2276L103.372 43.4127ZM77.0737 22.2386L81.6695 10.4147L88.6188 13.111L84.0231 24.9349L77.0737 22.2386ZM46.8856 24.3288L43.479 25.8254L38.363 14.2241L45.1885 11.2186L50.3045 22.8199L46.8979 24.3165L46.8856 24.3288ZM22.2593 52.0951L10.4169 47.5066L13.1174 40.5681L24.9598 45.1566L22.2593 52.0951ZM25.8517 85.6374L14.2323 90.7454L11.2221 83.9306L22.8415 78.8225L24.3404 82.2238L25.8393 85.625L25.8517 85.6374ZM52.1502 106.812L47.5545 118.635L40.6051 115.939L45.2009 104.115L52.1502 106.812ZM82.3384 104.721L85.7573 103.225L90.8733 114.826L84.0479 117.831L78.9319 106.23L82.3384 104.734V104.721ZM64.612 101.765C44.0488 101.765 27.3134 85.0561 27.3134 64.5251C27.3134 43.9941 44.0488 27.2848 64.612 27.2848C85.1751 27.2848 101.911 43.9941 101.911 64.5251C101.911 85.0561 85.1751 101.765 64.612 101.765ZM116.107 88.4944L104.264 83.9058L106.965 76.9673L118.807 81.5559L116.107 88.4944ZM95.2585 51.7488C90.2292 39.7889 78.3868 31.3786 64.612 31.3786C59.9791 31.3786 55.5691 32.331 51.5556 34.0501C51.5432 34.0501 51.5185 34.0501 51.5061 34.0625C51.5061 34.0625 51.4937 34.0749 51.4813 34.0749C39.6761 39.1705 31.4013 50.8954 31.4013 64.5374C31.4013 69.6331 32.5657 74.469 34.6344 78.7854C34.6468 78.8225 34.6592 78.8597 34.6839 78.8844C38.1524 86.0702 44.1479 91.8214 51.5185 95C51.5432 95.0124 51.568 95.0247 51.6052 95.0371C55.6063 96.7439 60.0162 97.6962 64.6367 97.6962C69.5174 97.6962 74.1627 96.6326 78.3373 94.7279C78.3373 94.7279 78.3496 94.7279 78.362 94.7155C86.2404 91.1288 92.4713 84.5613 95.6053 76.4726C95.6053 76.4726 95.6053 76.4726 95.6177 76.4602C95.6177 76.4479 95.6177 76.4355 95.6177 76.4108C97.0423 72.7251 97.8351 68.7178 97.8351 64.5374C97.8351 60.0107 96.9184 55.6942 95.2709 51.7612L95.2585 51.7488ZM93.8835 51.6994C88.6188 55.5582 83.8868 61.1609 80.7404 67.2337C73.4071 61.9649 66.7922 55.4593 61.0444 47.8653C67.5478 44.5506 72.7629 40.0486 76.1942 34.7675C84.0974 37.8471 90.4769 43.9693 93.8835 51.687V51.6994ZM64.612 32.6031C68.2415 32.6031 71.7348 33.2215 74.9926 34.3346C71.648 39.3808 66.594 43.6848 60.2888 46.8634C57.5387 43.1283 54.9745 39.1087 52.6705 34.9159C56.3619 33.4317 60.3879 32.6031 64.5996 32.6031H64.612ZM51.5308 35.4106C53.8349 39.6281 56.3991 43.6601 59.1615 47.42C51.4194 51.0686 42.8596 52.5775 35.39 51.6128C38.586 44.4269 44.3585 38.6387 51.5432 35.4106H51.5308ZM36.22 79.1936C39.7752 79.2678 43.2436 80.6901 46.0432 83.2998C48.979 86.0331 50.7628 89.6941 51.0973 93.4663C44.7054 90.4733 39.4655 85.4395 36.22 79.1936ZM64.612 96.4594C60.2764 96.4594 56.1514 95.5937 52.3732 94.0229C52.1874 89.7188 50.2178 85.5013 46.8979 82.3969C43.702 79.4162 39.7132 77.8578 35.613 77.9568C33.7053 73.8753 32.64 69.3239 32.64 64.5374C32.64 60.3941 33.4452 56.4363 34.8945 52.8001C36.5173 53.0228 38.202 53.1588 39.9114 53.1588C46.4644 53.1588 53.4881 51.5138 59.9295 48.4466C65.8136 56.2755 72.6266 62.9667 80.183 68.3592C76.2314 76.5716 75.2899 85.3281 77.4454 93.7755C73.5185 95.4947 69.1829 96.4594 64.6244 96.4594H64.612ZM78.5726 93.2437C76.5782 85.2168 77.4825 76.8808 81.1988 69.0641C85.3857 71.9706 89.7709 74.469 94.2427 76.4973C91.2697 83.8192 85.6458 89.793 78.5726 93.2314V93.2437ZM94.6887 75.3471C90.2416 73.3188 85.8936 70.8328 81.7438 67.951C84.754 62.0762 89.3001 56.6466 94.3666 52.8743C95.7912 56.4858 96.5839 60.4189 96.5839 64.5251C96.5839 68.3221 95.915 71.9706 94.6887 75.3471ZM85.683 113.861C85.7821 114.096 86.0175 114.232 86.2528 114.232C86.3395 114.232 86.4263 114.22 86.5006 114.183C86.8103 114.047 86.9589 113.676 86.8226 113.367L84.0974 107.207C83.9612 106.898 83.5895 106.75 83.2798 106.886C82.9702 107.022 82.8215 107.393 82.9578 107.702L85.683 113.861ZM108.154 82.0877L114.447 84.5242C114.521 84.549 114.595 84.5613 114.67 84.5613C114.917 84.5613 115.153 84.4129 115.252 84.1656C115.376 83.844 115.215 83.4853 114.893 83.3616L108.6 80.9251C108.278 80.8014 107.918 80.9622 107.795 81.2838C107.671 81.6054 107.832 81.964 108.154 82.0877ZM44.916 115.098C44.9903 115.123 45.0646 115.135 45.1389 115.135C45.3867 115.135 45.622 114.987 45.7211 114.739L48.1615 108.456C48.2853 108.135 48.1243 107.776 47.8022 107.653C47.4802 107.529 47.1209 107.69 46.997 108.011L44.5567 114.294C44.4328 114.616 44.5939 114.974 44.916 115.098ZM14.3314 45.6761L20.6242 48.1126C20.6985 48.1373 20.7728 48.1497 20.8472 48.1497C21.0949 48.1497 21.3303 48.0013 21.4294 47.7539C21.5532 47.4324 21.3922 47.0737 21.0701 46.95L14.7773 44.5135C14.4552 44.3898 14.096 44.5506 13.9721 44.8722C13.8483 45.1938 14.0093 45.5524 14.3314 45.6761ZM21.3922 82.8545L15.2233 85.5755C14.9136 85.7116 14.7649 86.0826 14.9012 86.3918C15.0003 86.6268 15.2357 86.7628 15.471 86.7628C15.5577 86.7628 15.6444 86.7505 15.7188 86.7134L21.8877 83.9924C22.1974 83.8564 22.346 83.4853 22.2098 83.1761C22.0735 82.8669 21.7019 82.7309 21.3922 82.8545Z"
        fill={color ?? 'url(#paint0_linear_940_1101)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_940_1101"
          x1="35.6625"
          y1="114.603"
          x2="93.4504"
          y2="14.3561"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
