import truncateText from './truncate-text'

export default function findLinkAndAddTag(text: string) {
  const linkRegex = /(?:(?:https?|ftp):\/\/)?[\w%./=?\\-]+\.[\w%./=?\\-]+/g
  const link = text.match(linkRegex)
  if (link) {
    const splittedText = text.split(link[0])
    return (
      <>
        {splittedText[0]}
        <a href={link[0]} target="_blank" rel="noreferrer">
          {truncateText(link[0], 30)}
        </a>
        {splittedText[1]}
      </>
    )
  }
  return text
}
