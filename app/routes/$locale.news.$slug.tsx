import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { Link, useLoaderData } from '@remix-run/react'
import { useTranslation } from 'react-i18next'
import { useEffect, useState } from 'react'

import {
  withCache,
  sanitizeDescriptionToRawString,
  renderMarkdown,
  getPostImage,
} from '~/utils'
import {
  Button,
  H2,
  H3,
  Post,
  Text,
  TextS,
  TextSNumeric,
} from '~/tenset-components'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { Namespace } from '~/i18n'
import { blankCover } from '~/assets/images'
import { fetchPost, getPostsList } from '~/api/fetchers'
import type { PostDataType } from '~/api/types'
import { PostImageSize } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function loader({ params }: LoaderFunctionArgs) {
  const post = await fetchPost({ slug: params.slug! })

  if (!post) {
    throw new Response('Not Found', {
      status: 404,
    })
  }

  const postsList = await getPostsList(
    getServerEnvironment().STRAPI_URL || '',
    1,
    4
  )

  const currentPostTitle = post.attributes.title

  const lastPosts =
    postsList === undefined
      ? [] // last 3 posts
      : postsList.data
          .filter(
            (post: PostDataType) => post.attributes.title !== currentPostTitle
          ) // skip current post
          .slice(0, 3)

  return withCache(
    json({
      lastPosts,
      postData: post.attributes,
    })
  )
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  if (data === undefined || !data.postData) return []

  const {
    postData: { title, body, cover },
  } = data

  return [
    { title },
    {
      name: 'description',
      content: sanitizeDescriptionToRawString(renderMarkdown(body)),
    },
    { property: 'og:title', content: `${title} | Tenset` },
    {
      property: 'og:image',
      content: getPostImage(cover, PostImageSize.LARGE).url,
    },
  ]
}

export const handle = {
  i18n: Namespace.NEWS,
}

export default function PostSlug() {
  const { postData, lastPosts } = useLoaderData<typeof loader>()

  const { t } = useTranslation(Namespace.NEWS)

  const localizePathname = useLocalizePathname()

  const { cover, title, date, body } = postData ?? {
    cover: 'Image cover',
    title: 'Try later',
    date: new Date().toLocaleDateString('en-US'),
    body: '',
  }

  const postImage =
    typeof cover === 'string'
      ? blankCover
      : getPostImage(cover, PostImageSize.LARGE).url

  const [lightboxImage, setLightboxImage] = useState('')

  useEffect(() => {
    const imagesInPostBody: NodeListOf<HTMLImageElement> =
      document.querySelectorAll('.prose img')

    for (const image of imagesInPostBody) {
      image.addEventListener('click', () => {
        setLightboxImage(image.src)
      })
    }
  }, [body])

  return (
    <div className="container">
      <div className="mx-auto flex max-w-full flex-col md:max-w-[692px]">
        <img
          className="mb-8 aspect-[2/1] w-full rounded-[10px]"
          src={postImage}
          alt={title}
          key={postImage}
        />

        <TextSNumeric className="mb-4 text-neutral-100 sm:text-base">
          {date}
        </TextSNumeric>

        <H2 isBold>{title}</H2>

        <div
          className="prose mt-8 flex max-w-none flex-col break-all text-base text-white prose-headings:text-2xl prose-headings:font-medium prose-headings:text-white prose-p:mt-0 prose-p:break-normal prose-p:text-white prose-a:break-words prose-a:text-white prose-strong:break-normal prose-strong:text-white prose-em:break-normal prose-em:text-white prose-li:break-normal prose-li:text-white prose-th:mt-0 prose-th:break-normal prose-th:text-center prose-th:text-base prose-th:text-white prose-img:cursor-pointer md:prose-headings:text-4xl md:prose-p:text-base md:prose-a:text-base md:prose-strong:text-base md:prose-em:text-base md:prose-li:text-base md:prose-th:text-base"
          dangerouslySetInnerHTML={{
            __html: renderMarkdown(body) as string,
          }}
        />

        <hr className="my-12 text-neutral-400" />
        {lastPosts.length > 0 && (
          <>
            <H3 isBold>{t('more-from-tenset')}</H3>

            <div className="mt-8 flex flex-col gap-8">
              {lastPosts.map(post => {
                const { date, title, cover, slug } = post.attributes

                return (
                  <Link key={post.id} to={localizePathname(`/news/${slug}`)}>
                    <Post
                      date={new Date(date)}
                      title={title}
                      image={getPostImage(cover, PostImageSize.SMALL).url}
                    />
                  </Link>
                )
              })}
            </div>
          </>
        )}
        <div className="mt-16 flex flex-col gap-8">
          <div className="flex flex-col gap-4">
            <H3 isBold>{t('gems-title')}</H3>

            <Text>{t('gems-description')}</Text>
          </div>

          <div>
            <Button to={localizePathname(`/gem-launch-platform`)}>
              <TextS isBold>{t('common:discover-gems')}</TextS>
            </Button>
          </div>
        </div>
      </div>

      {lightboxImage && (
        <div
          role="presentation"
          onClick={() => setLightboxImage('')}
          className="hide fixed top-0 right-0 bottom-0 left-0 z-[100] flex items-center justify-center bg-neutral-900 bg-opacity-75"
        >
          <img
            src={lightboxImage}
            alt="Lightbox"
            className="max-h-[90%] max-w-[90%]"
          />
        </div>
      )}
    </div>
  )
}
