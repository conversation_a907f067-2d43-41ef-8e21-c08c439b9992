import type { ReactElement } from 'react'
import clsx from 'clsx'

import type { IconName } from '~/tenset-components'
import { Icon, Card, H2 } from '~/tenset-components'

export interface CardWithTile {
  title: string | ReactElement
  description: string | ReactElement
  image: IconName
  action?: ReactElement
}

interface CardsWithTitleProps {
  title?: string
  cards: CardWithTile[]
  cardIconSize?: 'small' | 'medium' | 'large'
}

export function CardsWithTitle({
  title,
  cards,
  cardIconSize = 'large',
}: CardsWithTitleProps) {
  return (
    <div className="flex flex-col gap-12">
      {title && <H2 isBold>{title}</H2>}

      <div
        className={clsx(
          'grid grid-cols-1 gap-8 md:flex-row lg:gap-12',
          cards.length > 2 ? 'md:grid-cols-3' : 'md:grid-cols-2'
        )}
      >
        {cards.map(({ title, description, image, action }, index) => (
          <Card
            title={title}
            key={index}
            image={<Icon className="h-full w-full" name={image} />}
            action={action}
            iconSize={cardIconSize}
          >
            {description}
          </Card>
        ))}
      </div>
    </div>
  )
}
