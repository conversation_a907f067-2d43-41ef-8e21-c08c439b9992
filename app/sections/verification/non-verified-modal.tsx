import { Trans, useTranslation } from 'react-i18next'

import { InfoCircleIcon } from 'modules/tenset-components/src/assets/icons'
import { Namespace } from '~/i18n'
import {
  Button,
  ButtonVariant,
  Modal,
  ModalContent,
  ModalDescription,
  ModalFooter,
  ModalHeader,
  ModalTitle,
} from '~/tenset-components'

interface NonVerifiedModalProps {
  isOpen: boolean
  onDismiss: () => void
  verifiedPhrase: string
  onClickHowTo: () => void
}

export function NonVerifiedModal({
  isOpen,
  onDismiss,
  verifiedPhrase,
  onClickHowTo,
}: NonVerifiedModalProps) {
  const { t } = useTranslation([Namespace.VERIFICATION])

  return (
    <Modal open={isOpen} onClose={onDismiss}>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{t('non-verified-modal.header')}</ModalTitle>

          <ModalDescription>
            <Trans
              t={t}
              i18nKey={'non-verified-modal.description'}
              values={{
                value: `${verifiedPhrase}`,
              }}
              components={{ bold: <strong /> }}
            />
          </ModalDescription>
        </ModalHeader>

        <ModalFooter>
          <Button onClick={onDismiss}>{t('common:got-it')}</Button>

          <Button
            className="ml-4"
            onClick={onClickHowTo}
            variant={ButtonVariant.Secondary}
          >
            {t('non-verified-modal.how-to')}
            <InfoCircleIcon />
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
