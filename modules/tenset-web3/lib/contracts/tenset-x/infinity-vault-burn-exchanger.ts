import { ContractTransaction } from 'ethers'

import { tensetXInfinityVaultBurnExchangerAbi } from '../../assets'
import { config } from '../../config'
import { Utils } from '../../types'
import { Contract } from '../base'

export class TensetXInfinityVaultBurnExchangerContract extends Contract {
  constructor(chain: Utils.Network) {
    super(
      config.tensetX.infinityVaultBurnExchanger[chain]!,
      chain,
      tensetXInfinityVaultBurnExchangerAbi
    )
  }

  public async exchange(
    depositId: number,
    overrides: Utils.ContractMethodOverrides = {}
  ) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.exchange(
        depositId,
        overrides
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
