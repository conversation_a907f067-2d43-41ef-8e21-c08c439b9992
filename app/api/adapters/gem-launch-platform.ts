import type {
  GlpAssignWalletMessage,
  GlpLock,
  GlpNft,
  GlpReferralLockMessage,
} from '../types/gem-launch-platform'

export const adaptLock = (data: Record<string, never>): GlpLock => ({
  id: Number.parseInt(data.id),
  amount: `${data.amount}`,
  txHash: data.txHash || '',
  timestamps: {
    unlock: Number.parseInt(data.unlockTime),
    withdraw: undefined,
  },
  proofs: data.proofs,
})

export const adaptNft = (data: Record<string, never>): GlpNft => ({
  id: Number.parseInt(data.tokenId),
  chainId: Number.parseInt(data.chainId),
  collection: `${data.collection}`,
  contractAddress: `${data.contractAddress}`,
  tokenName: `${data.tokenName}`,
  image: `${data.image}`,
})

export const adaptAssignWalletData = (
  {
    subscriptionAddress,
    assignedAddress,
    date,
    launchName,
  }: GlpAssignWalletMessage,
  remove = false
) => {
  const typeName = remove ? 'UndelegateWallet' : 'DelegateWallet'

  return {
    types: {
      [typeName]: [
        {
          name: 'message',
          type: 'string',
        },
        {
          name: 'IDOName',
          type: 'string',
        },
        {
          name: 'subscriptionAddress',
          type: 'address',
        },
        {
          name: 'assignedAddress',
          type: 'address',
        },
        {
          name: 'date',
          type: 'string',
        },
      ],
    },
    value: {
      message: remove
        ? `I want to remove assignedAddress from ${launchName}`
        : `I want assignedAddress to use my allocation associated with subscriptionAddress for ${launchName}`,
      IDOName: launchName,
      subscriptionAddress,
      assignedAddress: assignedAddress,
      date: date + ' UTC',
    },
  }
}

export const adaptReferralLockData = ({
  subscriptionAddress,
  lockId,
  unlockDate,
  date,
}: GlpReferralLockMessage) => ({
  types: {
    ReferralLock: [
      {
        name: 'message',
        type: 'string',
      },
      {
        name: 'subscriptionAddress',
        type: 'address',
      },
      {
        name: 'lockId',
        type: 'uint256',
      },
      {
        name: 'date',
        type: 'string',
      },
    ],
  },
  value: {
    message: `I want to add a referral to my lock with id ${lockId}.\n\n I want to receive a 25% boost to my allocation in each launch for the duration of my lock, by giving up 5% of my locked 10SET. I understand that after my lock period ends (${unlockDate}) I will be able to withdraw 5% 10SET tokens less than I locked.`,
    subscriptionAddress,
    lockId,
    date: date + ' UTC',
  },
})
