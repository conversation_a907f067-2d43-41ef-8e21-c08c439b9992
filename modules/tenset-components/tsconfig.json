{"include": ["src/**/*.ts", "src/**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2021"], "module": "esnext", "jsx": "react-jsx", "sourceMap": true, "strict": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": "./src", "forceConsistentCasingInFileNames": true, "allowJs": true, "noEmit": true, "declaration": true, "declarationDir": "src/types", "isolatedModules": true, "paths": {"~/*": ["src/*"]}, "typeRoots": ["src/types"]}, "exclude": ["node_modules", "**/*stories"]}