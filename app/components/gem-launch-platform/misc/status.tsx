import type { SerializeFrom } from '@remix-run/node'
import { Await, useOutletContext } from '@remix-run/react'
import { formatEther } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import { Suspense, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { GetMoreAllocationsButton } from '../buttons/lock-more'

import type { GlpLock, GlpNft } from '~/api/types'
import { tiersPrivileges } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import type { GlpOutletData } from '~/routes/$locale.gem-launch-platform.panel'
import type { GlpPanelLoaderData } from '~/routes/$locale.gem-launch-platform.panel._index'
import {
  Button,
  ButtonVariant,
  DataPoint,
  H2,
  H3,
  Tag,
  TagColor,
} from '~/tenset-components'
import { cutDecimals } from '~/utils'

function getAllocationWeight(locks: GlpLock[], nfts: GlpNft[], t: TFunction) {
  const tiers = tiersPrivileges(t)

  const tensetTiers = tiersPrivileges(t).filter(
    ({ entry }) => typeof entry === 'number'
  )

  const { tier1, tier2, tier3 } = locks.reduce(
    (accumulator, { membershipAttributes }) => {
      const { tierId, bonusNumerator, bonusDenominator } = membershipAttributes!

      const { poolWeight } = tensetTiers[tierId - 1]

      const bonus = (bonusNumerator / bonusDenominator) * poolWeight

      // @ts-expect-error TODO
      accumulator[`tier${tierId}`] += poolWeight + bonus

      return accumulator
    },
    {
      tier1: 0,
      tier2: 0,
      tier3: 0,
    }
  )

  const { tier4, tier5 } = nfts.reduce(
    (accumulator, { collection }) => {
      const tier = ['TGLP Genesis', 'TGLP Sumeragi'].includes(collection)
        ? 5
        : 4

      const index = tier - 1

      accumulator[`tier${tier}`] += tiers[index].poolWeight

      return accumulator
    },
    {
      tier4: 0,
      tier5: 0,
    }
  )

  return [
    {
      tier: t('gem-launch-platform:tiers-privileges.tier', { tier: 1 }),
      value: tier1,
    },
    {
      tier: t('gem-launch-platform:tiers-privileges.tier', { tier: 2 }),
      value: tier2,
    },
    {
      tier: t('gem-launch-platform:tiers-privileges.tier', { tier: '3-5' }),
      value: tier3 + tier4 + tier5,
    },
  ]
}

type TGLPStatusProps = Omit<
  SerializeFrom<GlpPanelLoaderData>,
  | 'referralLocks'
  | 'isAddressApprovedForReferral'
  | 'extendAllowance'
  | 'oldReleasedLocks'
> &
  Pick<GlpOutletData, 'tensetBalance'>

export function TGLPStatus({
  tensetBalance,
  assignedWallet,
  nfts,
  delegatedLocks,
  rpcLocks,
  delegatedRpcLocks,
  delegatedNfts,
  assigningAddresses,
}: TGLPStatusProps) {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
    Namespace.GEM_LAUNCH_PLATFORM,
  ])

  const localizePathname = useLocalizePathname()

  // https://github.com/remix-run/react-router/discussions/10421#discussioncomment-8737871
  const locksPromise = useMemo(
    () => Promise.all([rpcLocks, delegatedRpcLocks]),
    [rpcLocks]
  )

  const { blockTimestamp } = useOutletContext<GlpOutletData>()

  return (
    <Suspense
      fallback={
        <div className="flex flex-col gap-6 md:gap-12">
          <div className="flex flex-col gap-4">
            <div className="flex flex-wrap place-content-between gap-2">
              <div className="h-[32px] md:h-[45px] w-full max-w-[320px] sm:max-w-[484px] animate-pulse rounded-md bg-neutral-600"></div>

              <GetMoreAllocationsButton />
            </div>

            <div className="h-[30px] w-[130px] animate-pulse rounded-md bg-neutral-600"></div>
          </div>

          <div className="flex flex-col gap-6 md:w-1/2">
            <div className="h-[32px] w-full max-w-[440px] animate-pulse rounded-md bg-neutral-600"></div>

            <div className="grid grid-cols-3 gap-4">
              <div className="h-[84px] w-full animate-pulse rounded-md bg-neutral-600"></div>
              <div className="h-[84px] w-full animate-pulse rounded-md bg-neutral-600"></div>
              <div className="h-[84px] w-full animate-pulse rounded-md bg-neutral-600"></div>
            </div>
          </div>
        </div>
      }
    >
      <Await resolve={locksPromise}>
        {([rpcLocks, delegatedRpcLocks]) => {
          // const allNfts = [...nfts, ...delegatedNfts]

          const {
            activeLocks,
            activeDelegatedTglpLocks,
            activeDelegatedTplLocks,
          } = getFilteredLocks(
            [...rpcLocks],
            [...delegatedLocks, ...delegatedRpcLocks],
            blockTimestamp
          )

          const allActiveLocks = [
            ...activeLocks,
            ...activeDelegatedTglpLocks,
            ...activeDelegatedTplLocks,
          ]

          const allNfts = [...nfts, ...delegatedNfts]

          const newestTokensLock = [
            ...rpcLocks,
            ...delegatedLocks,
            ...delegatedRpcLocks,
          ]?.sort((a, b) => b.timestamps.unlock - a.timestamps.unlock)?.[0]

          const timeDifference = Math.ceil(
            (newestTokensLock?.timestamps.unlock - blockTimestamp) /
              1000 /
              60 /
              60 /
              24
          )

          const remainingDays = timeDifference > 0 ? timeDifference : 0

          const isSubscriptionActive = remainingDays > 0 || allNfts.length > 0

          // const areSomeDelegated = delegatedLocks.length > 0 || delegatedNfts.length > 0
          //
          // const tglpDelegatedAllocationsNumber =
          //   activeDelegatedTglpLocks.length + delegatedNfts.length
          //
          // const TglpAllAllocationsNumber = allActiveTglpLocks.length + allNfts.length
          //
          // const TglpDelegatedAllocationsNumber =
          //   activeDelegatedTplLocks.length +
          //   2 * (activeDelegatedTglpLocks.length + delegatedNfts.length)
          //
          // const TplAllAllocationsNumber =
          //   allActiveTplLocks.length + 2 * (allActiveTglpLocks.length + allNfts.length)

          const allocationWeight = getAllocationWeight(
            allActiveLocks,
            allNfts,
            t
          ).filter(({ value }) => value > 0)

          const GLP_TRANSFER_URL = localizePathname(
            '/gem-launch-platform/panel/transfer'
          )

          const canTransferSubscription =
            activeLocks.length > 0 || nfts.length > 0

          return (
            <div className="flex flex-col gap-6 md:gap-12">
              <div className="flex flex-col gap-2">
                <div className="flex flex-wrap place-content-between gap-2">
                  <H2 isBold>
                    {t(
                      isSubscriptionActive
                        ? 'status.active.title'
                        : 'status.inactive.title'
                    )}
                  </H2>

                  <div className="flex gap-2 flex-wrap">
                    {canTransferSubscription && (
                      <Button
                        variant={ButtonVariant.Secondary}
                        to={GLP_TRANSFER_URL}
                      >
                        {t('transfer.transfer.button')}
                      </Button>
                    )}

                    <GetMoreAllocationsButton />
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 md:w-1/2">
                  {assignedWallet && (
                    <Tag color={TagColor.YELLOW}>
                      {t('assign-wallet.assigned-wallet')}: {assignedWallet}
                    </Tag>
                  )}

                  <Tag
                    color={
                      tensetBalance && tensetBalance.gt(0)
                        ? TagColor.GREEN
                        : TagColor.NEUTRAL
                    }
                  >
                    {cutDecimals(formatEther(tensetBalance))} 10set
                  </Tag>
                </div>
              </div>

              {assigningAddresses.length > 0 && (
                <DataPoint
                  label={t('status.delegating-addresses')}
                  className="w-fit"
                >
                  <span className="flex gap-2 flex-wrap">
                    {assigningAddresses.map(address => (
                      <Tag key={address} color={TagColor.GREEN}>
                        {address}
                      </Tag>
                    ))}
                  </span>
                </DataPoint>
              )}

              {!assignedWallet && allocationWeight.length > 0 && (
                <div className="flex flex-col gap-6 md:w-1/2">
                  <H3 isBold>{t('allocation-weight')}</H3>

                  <div className="grid grid-cols-3 gap-4">
                    {allocationWeight.map(({ tier, value }) => (
                      <DataPoint label={tier} key={tier}>
                        {value}x
                      </DataPoint>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )
        }}
      </Await>
    </Suspense>
  )
}

const locksFilter = (locks: GlpLock[], blockTimestamp: number) =>
  locks?.filter(
    lock => lock.timestamps.unlock > blockTimestamp && !lock.timestamps.withdraw
  )

const getFilteredLocks = (
  locks: GlpLock[],
  delegatedLocks: GlpLock[],
  blockTimestamp: number
) => {
  const activeDelegatedTglpLocks = locksFilter(delegatedLocks, blockTimestamp)
  const activeDelegatedTplLocks = locksFilter(delegatedLocks, blockTimestamp)

  const activeLocks = locksFilter(locks, blockTimestamp)

  return {
    activeDelegatedTglpLocks,
    activeDelegatedTplLocks,
    activeLocks,
  }
}
