import { H2, <PERSON><PERSON>, But<PERSON> } from '~/tenset-components'

interface ActionStepperProps {
  title: string
  steps: {
    title: string
    timeline?: string
    content: JSX.Element
  }[]
  action: {
    label: string
    to: string
  }
}

export function ActionStepper({ title, steps, action }: ActionStepperProps) {
  return (
    <>
      <H2 isBold>{title}</H2>

      <Stepper steps={steps} />

      <div className="flex flex-col md:flex-row">
        <Button to={action.to}>{action.label}</Button>
      </div>
    </>
  )
}
