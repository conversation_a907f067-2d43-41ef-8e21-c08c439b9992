import type { TFunction } from 'i18next'

import { IconName } from '~/tenset-components'
import type { CardWithTile } from '~/components/cards-with-title'

export const ecosystem = (t: TFunction): CardWithTile[] => [
  {
    title: t('smart-contract-development.ecosystem.tokens.title'),
    description: t('smart-contract-development.ecosystem.tokens.description'),
    image: IconName.ScDevelopmentTokens,
  },
  {
    title: t('smart-contract-development.ecosystem.vesting.title'),
    description: t('smart-contract-development.ecosystem.vesting.description'),
    image: IconName.ScDevelopmentVesting,
  },
  {
    title: t('smart-contract-development.ecosystem.cross-chain.title'),
    description: t(
      'smart-contract-development.ecosystem.cross-chain.description'
    ),
    image: IconName.ScDevelopmentCrosschain,
  },
]
