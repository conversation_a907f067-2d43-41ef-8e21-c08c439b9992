import { Subject } from 'rxjs'
import { filter } from 'rxjs/operators'

import type { Event } from './event'

export class Dispatcher {
  protected stream: Subject<Event> = new Subject<Event>()

  emit(event: Event): void {
    this.stream.next(event)
  }

  listen(...events: Event[]) {
    return this.stream.pipe(
      filter((item: Event) => {
        for (let index = 0, x = events.length; index < x; index++) {
          const event = events[index]

          if (
            item === event ||
            item.type === event.type ||
            (event instanceof Function && item instanceof event)
          ) {
            return true
          }
        }

        return false
      })
    )
  }
}
