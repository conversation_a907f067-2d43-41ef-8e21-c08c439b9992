import type { ActionFunction, LoaderFunctionArgs } from '@remix-run/node'
import { json, useFetcher, useLoaderData } from '@remix-run/react'
import { verifyTypedData } from 'ethers/lib/utils'
import type { ReactElement } from 'react'
import { useRef } from 'react'
import { Bot } from 'grammy'
import { ethers } from 'ethers'

import { fetchGlpSubscriptionInfo, hasLockedPackage } from '~/api/fetchers'
import { ConnectWallet } from '~/components/connect-wallet'
import { telegramBotDb } from '~/database'
import { detectLocale, i18next, Namespace } from '~/i18n'
import { getSession } from '~/sessions.server'
import { Button, Card, H2, Text } from '~/tenset-components'
import {
  chains,
  config,
  GlpLifetimeMembershipContract,
  GlpMembershipContract,
  useWallet,
} from '~/tenset-web3'
import {
  formDataToObject,
  getRpcLifetimeNfts,
  getRpcLocks,
  isFormOk,
  schema,
  translateValidations,
  validate,
  validateExistanceAndNoEmptiness,
  validateIfEthAddress,
} from '~/utils'
import { getServerEnvironment } from '~/environment/environment.server'
import { BSC_NETWORK } from '~/config'

interface JoinTelegramData {
  address: string
  signature: string
  message: string
}

interface JoinTelegramActionData {
  inviteLink?: string
  ok?: boolean
  validations?: Record<string, string[]>
}

const joinTelegramFormSchema = schema({
  address: [validateIfEthAddress],
  signature: [validateExistanceAndNoEmptiness],
  message: [validateExistanceAndNoEmptiness],
})

const isEligableToJoinTelegram = async (walletAddress: string) => {
  const { gemLaunchPlatform } = config
  const { rpcUrls } = chains[BSC_NETWORK]

  const glpMembershipContract = new GlpMembershipContract(
    gemLaunchPlatform.membership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const glpLifetimeMembershipContract = new GlpLifetimeMembershipContract(
    gemLaunchPlatform.lifetimeMembership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const [membershipBalance, lifetimeMembershipBalance, { nfts: nftsApi }] =
    await Promise.all([
      glpMembershipContract.balanceOf(walletAddress),
      glpLifetimeMembershipContract.balanceOf(walletAddress),
      fetchGlpSubscriptionInfo(walletAddress),
    ])

  const jsonRpcProvider = new ethers.providers.JsonRpcProvider({
    url: rpcUrls[0],
    skipFetchSetup: true,
  })

  const [lifetimeMembershipNfts, locksResponse, { timestamp: blockTimestamp }] =
    await Promise.all([
      getRpcLifetimeNfts({
        walletAddress,
        glpLifetimeMembershipContract,
        lifetimeMembershipBalance,
      }),
      getRpcLocks({
        walletAddress,
        glpMembershipContract,
        membershipBalance,
      }),
      jsonRpcProvider.getBlock('latest'),
    ])

  const locks = locksResponse.filter(
    ({ timestamps }) => timestamps.unlock > blockTimestamp * 1000
  )

  const nfts = [...nftsApi, ...lifetimeMembershipNfts]

  const hasDesiredPackage = await hasLockedPackage(walletAddress as string)

  return nfts?.length > 0 || hasDesiredPackage || locks?.length > 0
}

export const action: ActionFunction = async ({ request }) => {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.FORM_VALIDATORS)

  const data: JoinTelegramData = formDataToObject(
    await request.formData(),
    Object.keys(joinTelegramFormSchema)
  )

  const validations = validate(data, joinTelegramFormSchema)
  const formOk = isFormOk(validations)

  if (!formOk) {
    return json({
      validations: translateValidations(t, validations),
      ok: false,
    })
  }

  const { address, message, signature } = data

  const typedData = JSON.parse(message)

  const signingAddress = verifyTypedData(
    typedData.domain,
    typedData.types,
    typedData.message,
    signature
  )

  const isEligable = await isEligableToJoinTelegram(signingAddress)

  if (!isEligable) {
    throw new Response('Not eligible', {
      status: 400,
    })
  }

  if (signingAddress.toLowerCase() !== address.toLowerCase()) {
    throw new Response('Signing address mismatch', {
      status: 400,
    })
  }

  let rows = []
  try {
    const result = await telegramBotDb.query(
      `SELECT id FROM "user" WHERE LOWER(wallet) = $1`,
      [address.toLowerCase()]
    )
    rows = result.rows
  } catch (error) {
    console.error('Error querying user database:', error)
    throw new Response('Database error', {
      status: 500,
    })
  }

  if (rows.length > 0) {
    return json({
      inviteLink: rows[0].link,
    })
  }

  const bot = new Bot(getServerEnvironment().TELEGRAM_BOT_TOKEN)

  const generateChatInviteLink = async (chatId: string) => {
    try {
      const inviteLink = await bot.api.createChatInviteLink(chatId, {
        member_limit: 1,
      })
      return inviteLink.invite_link
    } catch (error) {
      console.error('Error creating chat invite link:', error)
      throw new Error('Failed to create invite link')
    }
  }

  const inviteLink = await generateChatInviteLink(
    getServerEnvironment().TELEGRAM_BOT_CHAT_ID
  )

  // connect to telegram bot and generate link

  const signatureString = JSON.stringify({
    signature,
    typedData,
  })

  try {
    await telegramBotDb.query(
      `INSERT INTO "user" (id, username, link, wallet, signature) 
       VALUES ($1, $2, $3, $4, $5) RETURNING *`,
      [
        typedData.message.id,
        typedData.message.username,
        inviteLink,
        address,
        signatureString,
      ]
    )
  } catch (error) {
    console.error('Error inserting user data:', error)
    throw new Response('Database error', {
      status: 500,
    })
  }

  return json({ inviteLink })
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  if (!walletAddress) {
    return json({
      isSigned: false,
      isEligable: false,
    })
  }

  let isSigned = false

  try {
    const { rows } = await telegramBotDb.query(
      `SELECT id FROM "user" WHERE LOWER(wallet) = $1`,
      [walletAddress.toLowerCase()]
    )
    // If username does not start with unconfirmed-000000 it is signed
    isSigned =
      rows.length > 0 && !rows[0].username.startsWith('unconfirmed-000000')
  } catch (error) {
    console.error('Error querying user database in loader:', error)
    // Continue with isSigned = false instead of throwing an error
  }

  const isEligable = await isEligableToJoinTelegram(walletAddress)

  return json({
    isSigned,
    isEligable,
  })
}

export default function JoinTelegramPage() {
  const timestampReference = useRef(Date.now().toString())
  const { isSigned, isEligable } = useLoaderData<typeof loader>()
  const { wallet } = useWallet()
  const fetcher = useFetcher<JoinTelegramActionData>()
  const { data } = fetcher

  const onSubmit = async () => {
    if (!wallet?.isConnected) return

    const signature = await wallet?.signTypedData(
      typedData.domain,
      typedData.types,
      typedData.message
    )

    if (!signature) return

    fetcher.submit(
      {
        address: wallet?.account,
        message: JSON.stringify(typedData),
        signature,
      },
      {
        method: 'post',
        preventScrollReset: true,
      }
    )
  }

  const id = `000000${timestampReference.current}`

  const username = `unconfirmed-${id}`

  const typedData = {
    domain: {
      name: 'MiniApp',
      version: '1',
    },
    types: {
      Data: [
        { name: 'address', type: 'address' },
        { name: 'id', type: 'string' },
        { name: 'username', type: 'string' },
        { name: 'timestamp', type: 'string' },
      ],
    },
    primaryType: 'Data',
    message: {
      address: wallet?.account,
      id: id.toString(),
      username,
      timestamp: timestampReference.current,
    },
  }

  if (!wallet?.isConnected) {
    return (
      <Container>
        <div>
          <ConnectWallet buttonConnectLabel={'Connect Wallet'} />
        </div>
      </Container>
    )
  }

  if (!isEligable) {
    return (
      <Container>
        <Text>You are not eligible for joining the secret group.</Text>
      </Container>
    )
  }

  if (isSigned && !data?.inviteLink) {
    return (
      <Container>
        <Text>You have already applied. Thank you!</Text>
      </Container>
    )
  }

  return (
    <Container>
      <>
        {!data?.inviteLink && (
          <>
            <Text>
              Hello! Please verify if you can join to our secret group!
            </Text>
            <div className="mt-16">
              <Card title={'Verify your wallet'}>
                <div className="flex flex-col gap-4 mt-2">
                  <Text>
                    Please sign the message below to prove you own your wallet.
                  </Text>
                  <div>
                    <Button onClick={onSubmit}>Sign</Button>
                  </div>
                </div>
              </Card>
            </div>
          </>
        )}
        {data?.inviteLink && (
          <div className="mt-16 flex flex-col gap-4 items-start">
            <Text>
              You have been invited to our secret Telegram group for TGLP
              subscribers.
            </Text>
            <Button to={data.inviteLink}>Join Telegram</Button>
          </div>
        )}
      </>
    </Container>
  )
}

function Container({ children }: { children: ReactElement }) {
  return (
    <div className="container min-h-[50vh]">
      <div className="flex flex-col gap-4 max-w-[600px]">
        <H2>Telegram secret group for Tenset TGLP subscribers</H2>
        {children}
      </div>
    </div>
  )
}
