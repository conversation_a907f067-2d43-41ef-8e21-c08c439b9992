import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  Button,
  ButtonVariant,
  CardsSection,
  Icon,
  IconName,
} from '~/tenset-components'

export function ContactCards() {
  const { t } = useTranslation([Namespace.CONTACT_PAGE])

  return (
    <CardsSection
      cards={[
        {
          title: t('cards.general'),
          image: <Icon className="h-full w-full" name={IconName.General} />,
          action: (
            <Button
              className="w-full md:w-auto"
              variant={ButtonVariant.Secondary}
              to="mailto:<EMAIL>"
            >
              <Icon name={IconName.Mail} />
              <EMAIL>
            </Button>
          ),
        },
        {
          title: t('cards.business'),
          image: <Icon className="h-full w-full" name={IconName.Business} />,
          action: (
            <Button
              className="w-full md:w-auto"
              variant={ButtonVariant.Secondary}
              to="mailto:<EMAIL>"
            >
              <Icon name={IconName.Mail} />
              <EMAIL>
            </Button>
          ),
        },
        {
          title: t('cards.jobs'),
          image: <Icon className="h-full w-full" name={IconName.Job} />,
          action: (
            <Button
              className="w-full md:w-auto"
              variant={ButtonVariant.Secondary}
              to="mailto:<EMAIL>"
            >
              <Icon name={IconName.Mail} />
              <EMAIL>
            </Button>
          ),
        },
      ]}
    />
  )
}
