import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'
import { useState } from 'react'

import { detectLocale } from '~/i18n/detect-locale'
import { i18next, Namespace } from '~/i18n'
import { withCache } from '~/utils'
import { governanceIllustration } from '~/assets/images'
import { DataPoint, H2, Hero, TextL } from '~/tenset-components'
import {
  GovernanceNoRightsToVoteModal,
  GovernanceSocials,
  GovernanceVoting,
  VotingStatus,
  useVotings,
} from '~/components/governance'
import { ConnectWallet } from '~/components/connect-wallet'
import { LoadingIndicator } from '~/components/utils'
import { endedVotingsData } from '~/data'
import DataPointsContainer from '~/components/containers/data-points-container'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.GOVERNANCE)

  const title = t('meta.title')
  const description = t('meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.GOVERNANCE, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function Governance() {
  const { t } = useTranslation([Namespace.GOVERNANCE, Namespace.COMMON])

  const { votings, loading } = useVotings()

  const activeVotings = votings.filter(voting => voting.endsAt > new Date())
  const endedVotings = [...votings, ...endedVotingsData].filter(
    voting => voting.endsAt <= new Date()
  )

  const [noRightsToVotesModal, setNoRightsToVoteModal] = useState(false)

  return (
    <>
      <div className="container gap-16 lg:gap-32 lg:px-[72px]">
        <Hero
          title={t('hero.title')}
          description={t('hero.description')}
          leftContent={
            <div className="flex flex-col items-start gap-4">
              <ConnectWallet buttonConnectLabel={t('hero.action')} />
            </div>
          }
          rightContent={
            <img src={governanceIllustration} alt={t('hero.title')} />
          }
        />

        <div className="flex flex-col gap-8 lg:gap-12">
          <H2 isBold>Voting weight</H2>

          <DataPointsContainer className="lg:grid-cols-5">
            <DataPoint label="Tier 5 NFT holders">6 votes</DataPoint>
            <DataPoint label="Tier 4 NFT holders">5 votes</DataPoint>
            <DataPoint label="Tier 3 subscribers">4 votes</DataPoint>
            <DataPoint label="Tier 2 subscribers">3 votes</DataPoint>
            <DataPoint label="Tier 1 subscribers">2 votes</DataPoint>
            <DataPoint label="Minimum 100 10SET on wallet">1 vote</DataPoint>
            <DataPoint label="Transaction volume on wallet (ETH/BNB chains)">
              1 vote
            </DataPoint>
          </DataPointsContainer>
        </div>

        <div className="flex flex-col xl:flex-row">
          <div className="flex flex-col gap-8 lg:gap-32 xl:mr-12 xl:flex-1 xl:border-r-[1px] xl:border-neutral-400 xl:pr-16">
            <div className="flex max-w-[692px] flex-col gap-8 lg:gap-12">
              <H2 isBold>{t('active-poll')}</H2>

              <VotingsWrapper isLoading={loading}>
                <>
                  {activeVotings.length > 0 ? (
                    activeVotings.map(voting => (
                      <GovernanceVoting
                        key={voting.title}
                        voting={voting}
                        status={VotingStatus.ACTIVE}
                        setNoRightsToVoteModal={setNoRightsToVoteModal}
                      />
                    ))
                  ) : (
                    <TextL>{t('last-active-poll')}</TextL>
                  )}
                </>
              </VotingsWrapper>
            </div>

            <div className="border-t border-b border-neutral-400 py-8 xl:hidden">
              <GovernanceSocials />
            </div>

            <div className="flex max-w-[692px] flex-col gap-8 lg:gap-12">
              <H2 isBold>{t('previous-polls')}</H2>

              <VotingsWrapper isLoading={false}>
                <>
                  {endedVotings.map(voting => (
                    <GovernanceVoting
                      key={voting.title}
                      voting={voting}
                      status={VotingStatus.ENDED}
                      setNoRightsToVoteModal={setNoRightsToVoteModal}
                    />
                  ))}
                </>
              </VotingsWrapper>
            </div>
          </div>

          <div className="hidden xl:block">
            <GovernanceSocials svgsIdPrefix="mobile" />
          </div>
        </div>
      </div>

      {activeVotings.length > 0 && (
        <GovernanceNoRightsToVoteModal
          voting={activeVotings[0]}
          isOpen={noRightsToVotesModal}
          setIsOpen={setNoRightsToVoteModal!}
        />
      )}
    </>
  )
}

interface VotingsWrapperProps {
  children: JSX.Element
  isLoading?: boolean
}

function VotingsWrapper({ children, isLoading }: VotingsWrapperProps) {
  return isLoading ? <LoadingIndicator /> : children
}
