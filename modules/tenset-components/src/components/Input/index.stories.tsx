import { Meta } from '@storybook/react'

import { Button } from '../Button'

import { Input } from './index'

type InputType = typeof Input

export default {
  title: 'Components/Input',
  component: Input,
} as Meta<InputType>

export const Default = {
  args: {
    name: 'default',
  },
}

export const WithPlaceholder = {
  args: {
    placeholder: 'Placeholder',
    name: 'withPlaceholder',
  },
}

export const WithPlaceholderAndError = {
  args: {
    name: 'withPlaceholderAndError',
    label: 'Label',
    placeholder: 'Placeholder',
    errorMessage: 'Disclaimer',
  },
}

export const WithButton = {
  args: {
    name: 'WithButton',
    label: 'Label',
    placeholder: 'Placeholder',
    postInput: <Button className="h-10 rounded-l-none">Button</Button>,
  },
}

export const Disabled = {
  args: {
    name: 'disabled',
    label: 'Label',
    placeholder: 'Placeholder',
    disabled: true,
    value: 0,
  },
}

export const WithInfo = {
  args: {
    name: 'withInfo',
    label: 'Label',
    info: 'Info',
    placeholder: 'Placeholder',
  },
}

export const SizeMedium = {
  args: {
    name: 'sizeMedium',
    placeholder: 'Placeholder',
    inputSize: 'medium',
  },
}
