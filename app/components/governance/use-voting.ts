import { useState, useEffect } from 'react'
import { filter } from 'rxjs/operators'

import type { Voting } from './voting'

import { store, VoteHasBeenCreated } from '~/state/governance'

export const useVoting = (voting: Voting) => {
  const [entity, setEntity] = useState(voting)
  const [votedAt, setVotedAt] = useState(0)

  useEffect(() => {
    setEntity(voting)

    const subscription = store.dispatcher
      .listen(VoteHasBeenCreated)
      .pipe(
        filter(
          event => (event as VoteHasBeenCreated).voting.id === voting?.uuid
        )
      )
      .subscribe(event => {
        const { vote } = event as VoteHasBeenCreated

        voting.consume(vote)

        setVotedAt(vote.createdAt)
      })
    return () => subscription.unsubscribe()
  }, [voting])

  return { voting: entity, lastVoteAt: votedAt }
}
