import { Button, ButtonVariant } from '../Button'
import { Text } from '../Typo/Text'

export const steps = [
  {
    title: 'Get a quote',
    timeline: '1-3 days',
    content: (
      <>
        <Text>
          You submit the required documentation and get the estimation of the
          process scope, timeline, and price.
        </Text>
        <Button className="mt-10" variant={ButtonVariant.Primary}>
          Request a quote
        </Button>
      </>
    ),
  },
  {
    title: 'Next Step',
    timeline: '1-3 days',
    content: (
      <>
        <Text>
          You submit the required documentation and get the estimation of the
          process scope, timeline, and price.
        </Text>
      </>
    ),
  },
  {
    title: 'Next Step',
    timeline: '1-3 days',
    content: (
      <>
        <Text>
          You submit the required documentation and get the estimation of the
          process scope, timeline, and price.
        </Text>
      </>
    ),
  },
  {
    title: 'Result',
    content: (
      <>
        <Text>
          You submit the required documentation and get the estimation of the
          process scope, timeline, and price.
        </Text>
      </>
    ),
  },
]
