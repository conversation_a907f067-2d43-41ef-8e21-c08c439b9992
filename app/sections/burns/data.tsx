import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import { CurrencyFormatter, H3, NumberFormatter } from '~/tenset-components'

interface BurnsData {
  label: string
  value: string | JSX.Element
}

interface BurnsDataProps {
  priceIncreasedPercentage: number
  presalePrice: number
  tensetPrice: number
}

export function BurnsData({
  priceIncreasedPercentage,
  presalePrice,
  tensetPrice,
}: BurnsDataProps) {
  const { t } = useTranslation([Namespace.BURNS])

  const burnsData: BurnsData[] = [
    {
      label: t('data.price-increased'),
      value: (
        <NumberFormatter
          value={priceIncreasedPercentage}
          precision={[0, 2]}
          prefix="+ "
          suffix="%"
        />
      ),
    },
    {
      label: t('data.presale-price'),
      value: <CurrencyFormatter value={presalePrice} />,
    },
    {
      label: t('data.current-price'),
      value: <CurrencyFormatter value={tensetPrice} />,
    },
  ]

  return (
    <div className="flex flex-col md:grid md:grid-cols-3 gap-y-8 gap-x-4">
      {burnsData.map(({ label, value }) => (
        <span key={label} className="flex flex-col gap-3">
          <span className="text-2xl lg:text-3xl font-semibold">{value}</span>

          <H3 isBold>{label}</H3>
        </span>
      ))}
    </div>
  )
}
