import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import { H2, TextL } from '~/tenset-components'

export function MattersIncubator() {
  const { t } = useTranslation([Namespace.INCUBATOR, Namespace.COMMON])

  return (
    <div className="flex max-w-[800px] flex-col gap-4">
      <H2 isBold>{t('incubator-matters.title')}</H2>

      <TextL>{t('incubator-matters.description')}</TextL>
    </div>
  )
}
