import type { TFunction } from 'i18next'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { SwitchNetworkButton } from '../switch-network/button'

import { TglpNewsletter } from '~/components/gem-launch-platform'
import { LoadingIndicator } from '~/components/utils'
import { BSC_NETWORK } from '~/config'
// import { nftExchangesButtons } from '~/data/homepage/tokens-available-buttons'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import {
  Button,
  ButtonVariant,
  Card,
  // Icon,
  Modal,
  ModalContent,
  ModalMain,
  Text,
} from '~/tenset-components'
import type { Wallet } from '~/tenset-web3'
import {
  chains,
  getNetworkName,
  useGlpContracts,
  useWallet,
} from '~/tenset-web3'

interface PresaleParticipateModalProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}

export function PresaleParticipateModal({
  isOpen,
  setIsOpen,
}: PresaleParticipateModalProps) {
  const { t } = useTranslation([
    Namespace.COMMON,
    Namespace.GEM_LAUNCH_PLATFORM,
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
  ])

  const { wallet } = useWallet()
  const localizePathname = useLocalizePathname()

  const { glpSubscribeContract } = useGlpContracts(BSC_NETWORK)

  const [isOnCorrectNetwork, setIsOnCorrectNetwork] = useState(false)

  const [hasSubscription, setHasSubscription] = useState<boolean | undefined>(
    undefined
  )

  useEffect(() => {
    if (!wallet || !wallet.isConnected || !glpSubscribeContract?.chain) return

    const isOnTglpSubChain = wallet?.isOnChain(glpSubscribeContract.chain)
    setIsOnCorrectNetwork(isOnTglpSubChain)
  }, [glpSubscribeContract, wallet?.chain])

  const fetchSubscriptionData = async () => {
    if (
      !wallet ||
      !wallet.isConnected ||
      !isOnCorrectNetwork ||
      !glpSubscribeContract
    )
      return

    const tglpLocksSc = []
    // await fetchTglpLocksSc(
    //   wallet,
    //   subscribeContract,
    //   wallet.account!
    // )

    const subscriptionInfo = { locks: [], nfts: [] } //await fetchTglpSubscriptionInfoApi(wallet.account!)

    const hasLock = tglpLocksSc.length > 0 || subscriptionInfo.locks?.length > 0
    const hasNft = subscriptionInfo.nfts?.length > 0

    setHasSubscription(hasLock || hasNft)
  }

  useEffect(() => {
    if (
      !wallet ||
      !wallet.isConnected ||
      !isOnCorrectNetwork ||
      !glpSubscribeContract
    )
      return setHasSubscription(undefined)

    fetchSubscriptionData()
  }, [wallet?.account, isOnCorrectNetwork, glpSubscribeContract])

  useEffect(() => {
    setHasSubscription(undefined)
  }, [wallet?.account])

  const title = getTitle({
    isOnCorrectNetwork,
    isDataLoading: hasSubscription === undefined,
    hasSubscription,
    t,
  })

  return (
    <Modal open={isOpen} onClose={() => setIsOpen(false)}>
      <ModalContent>
        <ModalMain>
          <Card title={title} limitWidth={false}>
            <PresaleParticipateModalContent
              wallet={wallet!}
              correctNetwork={BSC_NETWORK}
              isOnCorrectNetwork={isOnCorrectNetwork}
              isDataLoading={hasSubscription === undefined}
              hasSubscription={hasSubscription}
              t={t}
              localizePathname={localizePathname}
            />
          </Card>
        </ModalMain>
      </ModalContent>
    </Modal>
  )
}

interface GetTitle {
  isOnCorrectNetwork: boolean
  isDataLoading: boolean
  hasSubscription?: boolean
  t: TFunction
}

const getTitle = ({
  isOnCorrectNetwork,
  isDataLoading,
  hasSubscription,
  t,
}: GetTitle) => {
  const { name } = chains[BSC_NETWORK]

  if (!isOnCorrectNetwork)
    return t('gem-launch-platform-subscription:switch-network.title', {
      network: name,
    })

  if (isDataLoading) return ''

  return hasSubscription
    ? t('gem-launch-platform:modal.starts-title', {
        date: 'March 18',
      })
    : t('gem-launch-platform:modal.cannot-participate-title')
}

interface PresaleParticipateModalContentProps {
  wallet: Wallet
  correctNetwork: number
  isOnCorrectNetwork: boolean
  isDataLoading: boolean
  hasSubscription?: boolean
  t: TFunction
  localizePathname: (pathname: string) => string
}

function PresaleParticipateModalContent({
  wallet,
  correctNetwork,
  isOnCorrectNetwork,
  isDataLoading,
  hasSubscription,
  t,
  localizePathname,
}: PresaleParticipateModalContentProps) {
  if (!isOnCorrectNetwork)
    return (
      <SwitchNetworkButton
        name={getNetworkName(correctNetwork)}
        id={correctNetwork}
      />
    )

  if (isDataLoading) return <LoadingIndicator />

  return hasSubscription ? (
    <TglpNewsletter
      header={t('gem-launch-platform:newsletter.header-remind')}
      description={t('gem-launch-platform:newsletter.description')}
    />
  ) : (
    <div className="flex flex-col gap-4">
      <Text>{t('gem-launch-platform:modal.get-access')}</Text>

      {/*<div className="flex gap-4">*/}
      {/*  {nftExchangesButtons.map(({ label, link, icon }) => (*/}
      {/*    <Button*/}
      {/*      variant={ButtonVariant.Secondary}*/}
      {/*      key={label}*/}
      {/*      to={link}*/}
      {/*      reverseChildren*/}
      {/*    >*/}
      {/*      {label}*/}

      {/*      <Icon name={icon} size={24} />*/}
      {/*    </Button>*/}
      {/*  ))}*/}
      {/*</div>*/}

      <div className="flex flex-col items-start gap-4">
        <Text>
          {t('gem-launch-platform:modal.yearly-access', {
            amount: 5000,
          })}
        </Text>

        <Button
          to={localizePathname('subscription')}
          variant={ButtonVariant.Secondary}
        >
          {t('gem-launch-platform:modal.lock-tokens')}
        </Button>
      </div>
    </div>
  )
}
