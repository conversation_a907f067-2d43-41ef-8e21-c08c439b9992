import { useEffect, useState } from 'react'

import type {
  CountriesInflationCountryData,
  CountriesInflationCountryScrappedData,
} from '~/data'

export interface UseCountriesInflation {
  scrappedData: CountriesInflationCountryScrappedData[]
  secondsSinceBaseTime: number
}

interface CountriesInflation extends CountriesInflationCountryData {
  value: number
  inflationPercentage: number
}

export function useCountriesInflation({
  scrappedData,
  secondsSinceBaseTime,
}: UseCountriesInflation): CountriesInflation[] {
  const [countriesInflation, setCountriesInflation] = useState<
    CountriesInflation[]
  >(
    scrappedData.map(
      ({ country, baseValue, addPerSecond, inflationPercentage }) => {
        return {
          country,
          addPerSecond,
          value: baseValue + secondsSinceBaseTime * addPerSecond,
          inflationPercentage,
        }
      }
    )
  )

  useEffect(() => {
    const simulateDebt = () => {
      setCountriesInflation(previousCountriesInflation => {
        return previousCountriesInflation.map(
          ({ country, addPerSecond, value, inflationPercentage }) => {
            return {
              country,
              addPerSecond,
              value: value + addPerSecond,
              inflationPercentage,
            }
          }
        )
      })
    }

    simulateDebt()

    const interval = setInterval(simulateDebt, 1000)
    return () => clearInterval(interval)
  }, [])

  return countriesInflation
}
