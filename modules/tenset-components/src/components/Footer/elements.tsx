import { Icon, IconName } from '../Icon'

import { Element } from '.'

export const communityElements: Element[] = [
  {
    name: 'Twitter',
    to: '',
    icon: <Icon name={IconName.TwitterMono} />,
  },
  {
    name: 'Telegram',
    to: '',
    icon: <Icon name={IconName.TelegramMono} />,
  },
  {
    name: 'LinkedIn',
    to: '',
    icon: <Icon name={IconName.LinkedinMono} />,
  },
  {
    name: 'CoinGecko',
    to: '',
    icon: <Icon name={IconName.CoinGeckoMono} />,
  },
  {
    name: 'Line',
    to: '',
    icon: <Icon name={IconName.LineMono} />,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    to: '',
    icon: <Icon name={IconName.KakaoTalkMono} />,
  },
  {
    name: 'Youtube',
    to: '',
    icon: <Icon name={IconName.YoutubeMono} />,
  },
]

export const applyElements: Element[] = [
  {
    name: 'Apply to Infinity',
    to: '',
  },
  {
    name: 'Apply to TGLP',
    to: '',
  },
  {
    name: 'Careers',
    to: '',
  },
]

export const legalElements: Element[] = [
  {
    name: 'Legal Information',
    to: '',
  },
  {
    name: 'Data Protection Policy',
    to: '',
  },
  {
    name: 'Terms of Use',
    to: '',
  },
]
