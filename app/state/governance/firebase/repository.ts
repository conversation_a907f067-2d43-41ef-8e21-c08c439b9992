import type { QueryConstraint, WhereFilterOp } from 'firebase/firestore'
import { where } from 'firebase/firestore'

import { Repository as RepositoryPort } from '../repository'
import type { Query } from '../query'
import type { QueryBinding } from '../query.binding'
import { QueryWhereBinding } from '../query.binding'

import type { FirebaseClient } from './client'

export abstract class Repository<T> extends RepositoryPort<T> {
  constructor(protected client: FirebaseClient) {
    super()
  }

  abstract get(query: Query): Promise<T[]>

  protected queryToConstraints(query: Query) {
    const constraints: QueryConstraint[] = []

    for (let index = 0, x = query.bindings.length; index < x; index++) {
      constraints.push(...this.bindingToConstraint(query.bindings[index]))
    }

    return constraints
  }

  protected bindingToConstraint(binding: QueryBinding, key?: string) {
    const constraints: QueryConstraint[] = []

    if (binding instanceof QueryWhereBinding) {
      const { operatorOrValue, value } = binding

      if (value === null) {
        constraints.push(where(key ?? binding.key, '==', operatorOrValue))

        return constraints
      }

      constraints.push(
        where(key ?? binding.key, operatorOrValue as WhereFilterOp, value)
      )

      return constraints
    }

    return constraints
  }
}
