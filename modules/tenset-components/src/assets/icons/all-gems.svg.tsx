import { AssetProps } from '.'

export default function AllGems({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1848_51)">
        <path
          d="M96.01 17.79L75 47.34V67.58L96.01 81.82L117.03 67.58V47.34L96.01 17.79Z"
          fill="#15121A"
        />
        <path
          d="M49.49 59.78L59.75 49.52V21.26L49.49 10.99H26.77L16.51 21.25V49.52L26.77 59.78H49.49Z"
          fill="#15121A"
        />
        <path
          d="M91.99 96.81C91.99 101.19 88.84 104.34 84.46 104.34C88.84 104.34 91.99 107.49 91.99 111.87C91.99 107.49 95.14 104.34 99.52 104.34C95.14 104.34 91.99 101.19 91.99 96.81Z"
          fill="#15121A"
        />
        <path
          d="M52.38 76.59H33.81L24.52 80.13L14.99 87.95L43.09 121.43L71.19 87.95L61.66 80.13L52.38 76.59Z"
          fill="#15121A"
        />
        <path
          d="M24.92 58.64C24.97 58.69 25.02 58.72 25.08 58.75C25.14 58.78 25.2 58.79 25.27 58.79H47.99C48.06 58.79 48.12 58.78 48.18 58.75C48.24 58.72 48.3 58.69 48.34 58.64L58.6 48.38C58.69 48.29 58.75 48.16 58.75 48.03V19.76C58.75 19.63 58.7 19.5 58.6 19.41L48.35 9.15C48.26 9.06 48.13 9 48 9H25.28C25.15 9 25.02 9.05 24.93 9.15L14.67 19.4C14.58 19.5 14.52 19.62 14.52 19.76V48.03C14.52 48.16 14.57 48.29 14.67 48.38L24.92 58.63V58.64ZM25.1 57.41L15.9 48.21L23.73 44.97L28.34 49.58L25.09 57.41H25.1ZM24.35 44.17V23.62L29.14 18.83H44.12L48.92 23.63V44.16L44.12 48.96H29.14L24.35 44.17ZM26.02 57.79L29.27 49.96H44L47.25 57.79H26.03H26.02ZM48.16 57.41L44.91 49.58L49.53 44.96L57.36 48.2L48.15 57.41H48.16ZM57.75 47.28L49.92 44.04V23.76L57.75 20.52V47.28ZM48.17 10.38L57.38 19.59L49.55 22.83L44.93 18.21L48.17 10.38ZM47.25 10L44.01 17.83H29.28L26.04 10H47.26H47.25ZM25.11 10.38L28.35 18.21L23.74 22.82L15.91 19.58L25.1 10.38H25.11ZM15.52 20.51L23.35 23.75V44.04L15.52 47.28V20.51ZM98.03 102.36C93.96 102.36 91 99.4 91 95.33C91 95.05 90.78 94.83 90.5 94.83C90.22 94.83 90 95.05 90 95.33C90 99.4 87.04 102.36 82.97 102.36C82.69 102.36 82.47 102.58 82.47 102.86C82.47 103.14 82.69 103.36 82.97 103.36C87.04 103.36 90 106.32 90 110.39C90 110.67 90.22 110.89 90.5 110.89C90.78 110.89 91 110.67 91 110.39C91 106.32 93.96 103.36 98.03 103.36C98.31 103.36 98.53 103.14 98.53 102.86C98.53 102.58 98.31 102.36 98.03 102.36ZM90.5 107.44C89.72 105.28 88.08 103.64 85.92 102.86C88.08 102.08 89.72 100.44 90.5 98.28C91.28 100.44 92.92 102.08 95.08 102.86C92.92 103.64 91.28 105.28 90.5 107.44ZM78.89 94.23H77.3C77.02 94.23 76.8 94.45 76.8 94.73C76.8 95.01 77.02 95.23 77.3 95.23H78.89C79.17 95.23 79.39 95.01 79.39 94.73C79.39 94.45 79.17 94.23 78.89 94.23ZM116.03 45.83C116.03 45.83 116.03 45.75 116.02 45.71C116.01 45.67 115.98 45.64 115.96 45.61C115.96 45.61 115.96 45.59 115.95 45.58L94.93 16.02C94.93 16.02 94.91 16.01 94.91 16C94.82 15.89 94.68 15.81 94.53 15.81C94.38 15.81 94.24 15.89 94.15 16C94.15 16 94.13 16.01 94.13 16.02L73.11 45.58C73.11 45.58 73.11 45.6 73.1 45.61C73.08 45.64 73.05 45.68 73.04 45.71C73.03 45.75 73.03 45.79 73.03 45.83C73.03 45.84 73.02 45.85 73.02 45.86V66.1C73.02 66.1 73.02 66.11 73.02 66.12C73.02 66.19 73.04 66.27 73.07 66.34C73.11 66.41 73.16 66.47 73.23 66.52L94.25 80.76C94.25 80.76 94.27 80.76 94.28 80.76C94.35 80.8 94.44 80.84 94.53 80.84C94.62 80.84 94.71 80.81 94.78 80.76C94.78 80.76 94.8 80.76 94.81 80.76L115.83 66.52C115.9 66.47 115.95 66.41 115.99 66.34C116.03 66.27 116.04 66.2 116.04 66.12C116.04 66.12 116.04 66.11 116.04 66.1V45.86C116.04 45.86 116.04 45.84 116.03 45.83ZM95.02 17.87L114.73 45.59L106.77 48.13L95.01 31.6V17.87H95.02ZM94.02 17.87V31.6L82.26 48.13L74.3 45.59L94.01 17.87H94.02ZM74 46.55L81.96 49.09V61.05L74 65.27V46.55ZM94.02 79.4L74.47 66.15L82.43 61.93L94.02 69.79V79.4ZM94.52 68.92L82.96 61.09V48.89L94.52 32.63L106.08 48.89V61.09L94.52 68.92ZM95.02 79.4V69.79L106.61 61.93L114.57 66.15L95.02 79.4ZM115.04 65.27L107.08 61.05V49.09L115.04 46.55V65.27ZM74 15.08C74 14.8 73.78 14.58 73.5 14.58H71.2V12.28C71.2 12 70.98 11.78 70.7 11.78C70.42 11.78 70.2 12 70.2 12.28V14.58H67.9C67.62 14.58 67.4 14.8 67.4 15.08C67.4 15.36 67.62 15.58 67.9 15.58H70.2V17.88C70.2 18.16 70.42 18.38 70.7 18.38C70.98 18.38 71.2 18.16 71.2 17.88V15.58H73.5C73.78 15.58 74 15.36 74 15.08ZM70.15 86.27C70.15 86.27 70.13 86.24 70.13 86.23C70.11 86.2 70.1 86.16 70.07 86.13C70.06 86.12 70.04 86.11 70.03 86.1C70.03 86.1 70.02 86.08 70.01 86.07L60.48 78.25C60.48 78.25 60.42 78.23 60.39 78.21C60.37 78.2 60.36 78.18 60.34 78.17L51.05 74.63C50.99 74.61 50.93 74.6 50.87 74.6H32.3C32.24 74.6 32.18 74.61 32.12 74.63L22.83 78.17C22.83 78.17 22.8 78.2 22.78 78.21C22.75 78.23 22.72 78.23 22.69 78.25L13.18 86.07C13.18 86.07 13.17 86.09 13.16 86.1C13.15 86.11 13.13 86.12 13.12 86.13C13.09 86.16 13.08 86.2 13.06 86.23C13.06 86.24 13.04 86.26 13.04 86.27C13.01 86.34 13 86.41 13 86.48C13 86.48 13 86.48 13 86.49C13 86.5 13 86.51 13.01 86.53C13.01 86.58 13.03 86.63 13.06 86.68C13.07 86.7 13.08 86.72 13.09 86.74C13.09 86.75 13.1 86.77 13.11 86.78L41.21 120.26C41.21 120.26 41.24 120.28 41.25 120.29C41.27 120.31 41.28 120.33 41.3 120.34H41.32C41.33 120.34 41.35 120.36 41.36 120.37C41.43 120.41 41.51 120.43 41.58 120.43C41.66 120.43 41.73 120.41 41.8 120.37C41.82 120.37 41.83 120.35 41.84 120.34H41.86C41.88 120.33 41.89 120.3 41.91 120.29C41.92 120.28 41.94 120.27 41.95 120.26L70.05 86.78C70.05 86.78 70.07 86.75 70.07 86.74C70.08 86.72 70.09 86.7 70.1 86.68C70.12 86.63 70.14 86.58 70.15 86.53C70.15 86.52 70.16 86.51 70.16 86.49C70.16 86.49 70.16 86.49 70.16 86.48C70.16 86.41 70.16 86.34 70.12 86.27H70.15ZM32.4 75.6H50.79L58.74 78.63L51.4 81.32H31.79L24.45 78.63L32.4 75.6ZM51.97 83.96L56.67 93.65H26.51L31.21 83.96L32.01 82.32H51.18L51.98 83.96H51.97ZM23.12 79.21L31.01 82.1L25.51 93.44L14.35 86.41L23.12 79.21ZM15.93 88.59L17.74 89.73L25.35 94.52L37.59 114.38L15.94 88.59H15.93ZM41.59 118.98L34.89 108.1L26.61 94.65H56.58L48.3 108.1L41.6 118.98H41.59ZM45.6 114.38L57.84 94.52L65.45 89.73L67.26 88.59L45.61 114.38H45.6ZM57.68 93.43L52.18 82.09L60.07 79.2L68.84 86.4L57.68 93.43ZM80.99 96.31C80.71 96.31 80.49 96.53 80.49 96.81V98.4C80.49 98.68 80.71 98.9 80.99 98.9C81.27 98.9 81.49 98.68 81.49 98.4V96.81C81.49 96.53 81.27 96.31 80.99 96.31ZM83.09 95.21H84.68C84.96 95.21 85.18 94.99 85.18 94.71C85.18 94.43 84.96 94.21 84.68 94.21H83.09C82.81 94.21 82.59 94.43 82.59 94.71C82.59 94.99 82.81 95.21 83.09 95.21ZM80.99 90.51C80.71 90.51 80.49 90.73 80.49 91.01V92.6C80.49 92.88 80.71 93.1 80.99 93.1C81.27 93.1 81.49 92.88 81.49 92.6V91.01C81.49 90.73 81.27 90.51 80.99 90.51Z"
          fill="url(#paint0_linear_1848_51)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_1848_51"
          x1="27.16"
          y1="112.17"
          x2="85.76"
          y2="10.67"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_1848_51">
          <rect
            width="104.03"
            height="112.43"
            fill="white"
            transform="translate(13 9)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
