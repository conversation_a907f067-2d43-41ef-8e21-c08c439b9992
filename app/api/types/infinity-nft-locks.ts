export const InfinityNftCollection = new Map([
  ['0x28ce223853d123b52c74439b10b43366d73fd3b5', 'TGLP Genesis'],
  ['0x6f6c7dba6aa620e5b0bfa379271244c5e6869e33', 'TGLP Sumeragi'],
])

export interface InfinityNftLockResponse {
  id: number
  network: number
  vaultAddress: string
  account: string
  nftAddress: string
  nftId: number
  createdAt: string
  txHash: string
  unlockTime: string
  isConfirmed: boolean
  withdrawnAt?: string | null
  unlockavailable: boolean
  withdrawed: boolean
}

export interface InfinityNftLock {
  collection: string
  nftId: number
  nftAddress: string
  releaseDate: string
  isWithdrawn?: boolean
  isUnlocked: boolean
  network: number
}

export interface InfinityNftLocksData {
  lockedNftLocks: InfinityNftLock[]
  withdrawnNftLocks: InfinityNftLock[]
}
