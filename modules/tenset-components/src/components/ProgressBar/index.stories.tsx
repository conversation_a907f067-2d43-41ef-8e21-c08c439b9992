import { Meta } from '@storybook/react'

import { ProgressBar } from './index'

type ProgressBarType = typeof ProgressBar

export default {
  title: 'Components/ProgressBar',
  component: ProgressBar,
  args: {
    label: 'Subscribers',
    numerator: 7277,
    denominator: 10_000,
    ofLabel: 'of',
    valueLeftLabel: 'subscriptions left',
  },
} as Meta<ProgressBarType>

export const Preview = {}

export const Raw = {
  args: {
    label: undefined,
    ofLabel: undefined,
    valueLeftLabel: undefined,
  },
}
