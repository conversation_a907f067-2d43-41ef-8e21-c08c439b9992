import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  AnimatedNumber,
  CryptoCurrencyFormatter,
  H2,
  H3,
  IconName,
  NumberFormatter,
  ProgressCircle,
} from '~/tenset-components'
import {
  CardsWithTitle,
  type CardWithTile,
} from '~/components/cards-with-title'

interface BurnsSupplyReductionProps {
  initialTotalSupply: number
  currentSupply: number
  totalBurned: number
}

export function BurnsSupplyReduction({
  initialTotalSupply,
  currentSupply,
  totalBurned,
}: BurnsSupplyReductionProps) {
  const { t } = useTranslation([Namespace.BURNS])

  const reductionInSupplyPercentage =
    -((initialTotalSupply - currentSupply) / initialTotalSupply) * 100

  const supplyReduction: CardWithTile[] = [
    {
      title: (
        <div className="flex gap-6 items-center">
          <div className="bg-pink-500 rounded-full w-6 h-6"></div>

          <span>{t('supply-reduction.current-supply')}</span>
        </div>
      ),
      description: (
        <H3 isBold>
          <AnimatedNumber
            value={currentSupply}
            formatter={({ value }) => (
              <CryptoCurrencyFormatter
                value={value}
                precision={[2, 2]}
                currency="tokens"
              />
            )}
          />
        </H3>
      ),
      image: IconName.Supply,
    },
    {
      title: (
        <div className="flex gap-6 items-center">
          <div className="bg-neutral-700 rounded-full w-6 h-6"></div>

          <span>
            {t('supply-reduction.burned-as-of', {
              date: new Date().toLocaleDateString(),
            })}
          </span>
        </div>
      ),
      description: (
        <H3 isBold>
          <AnimatedNumber
            value={totalBurned}
            formatter={({ value }) => (
              <CryptoCurrencyFormatter
                value={value}
                precision={[2, 2]}
                currency="tokens"
              />
            )}
          />
        </H3>
      ),
      image: IconName.BurnTokens,
    },
  ]

  return (
    <div className="flex flex-col gap-8 lg:gap-12">
      <H2 isBold>{t('supply-reduction.title')}</H2>

      <div className="flex items-center flex-col xl:flex-row gap-16 lg:gap-x-24">
        <div className="w-64 sm:w-80 xl:w-96 h-auto">
          <ProgressCircle
            numerator={currentSupply}
            denominator={initialTotalSupply}
          >
            <div className="flex flex-col-reverse items-center justify-center gap-2 text-center">
              <H3 isBold>{t('supply-reduction.reduction-in-supply')}</H3>

              <span className="text-2xl xl:text-3xl font-semibold">
                <NumberFormatter
                  value={reductionInSupplyPercentage}
                  precision={[0, 2]}
                  suffix="%"
                />
              </span>
            </div>
          </ProgressCircle>
        </div>

        <CardsWithTitle cards={supplyReduction} />
      </div>
    </div>
  )
}
