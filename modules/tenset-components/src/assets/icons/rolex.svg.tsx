import { AssetProps } from './index'

export default function Rolex({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? 130}
      height={size ?? 130}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M87.7591 45.5803L83.3808 26.6886C82.9016 24.641 81.101 23.2116 78.9767 23.2116H77.6813L75.2979 18.7043C74.4171 17.0431 72.6943 16 70.8031 16H59.1969C57.3057 16 55.5829 17.0302 54.7021 18.7043L52.3187 23.2116H51.0233C48.9119 23.2116 47.0984 24.641 46.6192 26.6886L42.2409 45.5803C37.7332 50.7958 35 57.5824 35 64.9742C35 72.3661 37.7202 79.1398 42.228 84.3553L46.6192 103.311C47.0984 105.359 48.899 106.788 51.0233 106.788H52.3187L54.7021 111.296C55.5829 112.957 57.3057 114 59.1969 114H70.8031C72.6943 114 74.4171 112.97 75.2979 111.296L77.6813 106.788H78.9767C81.0881 106.788 82.9016 105.359 83.3808 103.311L87.772 84.3553C92.2668 79.1398 95 72.3661 95 64.9742C95 57.5824 92.2668 50.7958 87.7591 45.5803ZM55.829 19.3096C56.4896 18.0733 57.772 17.3007 59.1839 17.3007H70.7902C72.2021 17.3007 73.4845 18.0733 74.1451 19.3096L76.2047 23.2116H53.7565L55.8161 19.3096H55.829ZM47.8627 26.9848C48.1995 25.5296 49.4948 24.4993 50.9974 24.4993H56.2306V29.3543C56.2306 29.7149 56.5155 29.9982 56.8782 29.9982C57.2409 29.9982 57.5259 29.7149 57.5259 29.3543V24.4993H72.4352V29.3543C72.4352 29.7149 72.7202 29.9982 73.0829 29.9982C73.4456 29.9982 73.7306 29.7149 73.7306 29.3543V24.4993H78.9637C80.4793 24.4993 81.7617 25.5167 82.0984 26.9848L85.9715 43.7001C80.557 38.4202 73.1347 35.1493 64.9741 35.1493C56.8135 35.1493 49.3912 38.4202 43.9767 43.7001L47.8497 26.9848H47.8627ZM74.1451 110.703C73.4845 111.94 72.2021 112.712 70.7902 112.712H59.1839C57.772 112.712 56.4896 111.94 55.829 110.703L53.7565 106.801H76.2047L74.1321 110.703H74.1451ZM82.1114 103.028C81.7746 104.483 80.4793 105.514 78.9767 105.514H73.7435V100.659C73.7435 100.298 73.4585 100.015 73.0959 100.015C72.7332 100.015 72.4482 100.298 72.4482 100.659V105.514H57.5389V100.659C57.5389 100.298 57.2539 100.015 56.8912 100.015C56.5285 100.015 56.2435 100.298 56.2435 100.659V105.514H51.0104C49.4948 105.514 48.2124 104.496 47.8757 103.028L43.9767 86.2355C49.3912 91.5282 56.8135 94.7992 64.987 94.7992C73.1606 94.7992 80.5829 91.5282 85.9974 86.2355L82.0984 103.028H82.1114ZM64.987 93.5243C49.158 93.5243 36.2824 80.7238 36.2824 64.9871C36.2824 49.2504 49.158 36.4499 64.987 36.4499C80.8161 36.4499 93.6917 49.2504 93.6917 64.9871C93.6917 80.7238 80.8161 93.5243 64.987 93.5243ZM81.5674 48.5937C81.5674 48.5937 81.5415 48.5551 81.5285 48.5293C81.5155 48.5035 81.4896 48.5035 81.4637 48.4907C77.228 44.3054 71.4119 41.7169 64.987 41.7169C58.5622 41.7169 52.7461 44.3054 48.5104 48.4907C48.4974 48.5035 48.4715 48.5164 48.4456 48.5293C48.4197 48.5422 48.4197 48.5679 48.4067 48.5937C44.1969 52.8047 41.5933 58.5869 41.5933 64.9742C41.5933 71.3616 44.1969 77.1438 48.4067 81.3548C48.4197 81.3677 48.4326 81.3934 48.4456 81.4192C48.4585 81.4449 48.4845 81.4449 48.5104 81.4578C52.7461 85.6431 58.5622 88.2315 64.987 88.2315C71.4119 88.2315 77.228 85.6431 81.4637 81.4578C81.4767 81.4449 81.5026 81.4321 81.5285 81.4192C81.5544 81.4063 81.5544 81.3805 81.5674 81.3548C85.7772 77.1438 88.3808 71.3616 88.3808 64.9742C88.3808 58.5869 85.7772 52.8047 81.5674 48.5937ZM81.0363 80.0284L77.1114 76.1264C76.8523 75.8689 76.4508 75.8689 76.1917 76.1264C75.9326 76.384 75.9326 76.7832 76.1917 77.0407L80.1166 80.9427C76.3083 84.5098 71.2306 86.7506 65.6347 86.918V81.3805C65.6347 81.02 65.3497 80.7367 64.987 80.7367C64.6244 80.7367 64.3394 81.02 64.3394 81.3805V86.918C58.7435 86.7506 53.6658 84.5227 49.8575 80.9427L53.7824 77.0407C54.0415 76.7832 54.0415 76.384 53.7824 76.1264C53.5233 75.8689 53.1218 75.8689 52.8627 76.1264L48.9378 80.0284C45.3368 76.2423 43.0959 71.1942 42.9275 65.631H48.4974C48.8601 65.631 49.1451 65.3477 49.1451 64.9871C49.1451 64.6265 48.8601 64.3432 48.4974 64.3432H42.9275C43.0959 58.78 45.3368 53.7319 48.9378 49.9459L52.8627 53.8478C52.9922 53.9766 53.1606 54.041 53.3161 54.041C53.4715 54.041 53.6529 53.9766 53.7694 53.8478C54.0285 53.5903 54.0285 53.1911 53.7694 52.9335L49.8446 49.0315C53.6529 45.4644 58.7306 43.2237 64.3264 43.0562V48.5937C64.3264 48.9543 64.6114 49.2376 64.9741 49.2376C65.3368 49.2376 65.6218 48.9543 65.6218 48.5937V43.0562C71.2176 43.2237 76.2953 45.4515 80.1036 49.0315L76.1788 52.9335C75.9197 53.1911 75.9197 53.5903 76.1788 53.8478C76.3083 53.9766 76.4767 54.041 76.6321 54.041C76.7876 54.041 76.9689 53.9766 77.0855 53.8478L81.0104 49.9459C84.6114 53.7319 86.8523 58.78 87.0207 64.3432H81.4508C81.0881 64.3432 80.8031 64.6265 80.8031 64.9871C80.8031 65.3477 81.0881 65.631 81.4508 65.631H87.0207C86.8523 71.1942 84.6114 76.2423 81.0104 80.0284H81.0363ZM70.4145 60.1966L65.0259 64.1501L56.1788 55.9855C55.9197 55.7409 55.5052 55.7537 55.2591 56.0242C55.013 56.2817 55.0259 56.6938 55.2979 56.9385L64.5466 65.4636C64.6632 65.5795 64.8316 65.631 64.987 65.631C65.1166 65.631 65.2591 65.5924 65.3756 65.5022L71.1917 61.2397C71.4767 61.0336 71.5414 60.6215 71.3342 60.3382C71.1269 60.0549 70.7124 59.9905 70.4275 60.1966H70.4145Z"
        fill="url(#paint0_linear_2185_61)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_61"
          x1="41.8523"
          y1="104.844"
          x2="87.7168"
          y2="24.9366"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
