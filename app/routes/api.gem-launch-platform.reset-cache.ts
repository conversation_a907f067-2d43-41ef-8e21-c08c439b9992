import type { ActionFunctionArgs } from '@remix-run/node'

import { fetchGlpSubscriptionInfoForMany } from '~/api/fetchers'

export async function action({ request }: ActionFunctionArgs) {
  const url = new URL(request.url)
  const walletAddress = url.searchParams.get('walletAddress')?.toLowerCase()

  await fetchGlpSubscriptionInfoForMany(walletAddress!, true)

  return new Response('', {
    status: 200,
  })
}
