import { useTranslation } from 'react-i18next'

import { tokensAvailableButtons } from '~/data/homepage/tokens-available-buttons'
import { Namespace } from '~/i18n'
import type { H3 } from '~/tenset-components'
import { Button, ButtonVariant, H2, Icon, Text } from '~/tenset-components'

interface TokensAvailableOnExchangesBlockProps {
  HeaderLevel?: typeof H2 | typeof H3
  description?: string
}

export default function TokensAvailableOnExchangesBlock({
  HeaderLevel = H2,
  description,
}: TokensAvailableOnExchangesBlockProps) {
  const { t } = useTranslation(Namespace.COMMON)

  return (
    <div className="flex flex-col gap-6">
      <header>
        <HeaderLevel isBold>
          {t('tokens-available-on-exchanges.title')}
        </HeaderLevel>

        {description && <Text>{description}</Text>}
      </header>

      <main className="flex flex-col gap-4 md:flex-row flex-wrap">
        <TokensAvailableOnExchanges />
      </main>
    </div>
  )
}

export function TokensAvailableOnExchanges() {
  const { t } = useTranslation(Namespace.COMMON)

  return (
    <>
      {tokensAvailableButtons.map(({ label, icon, link }) => (
        <Button to={link} variant={ButtonVariant.Secondary} key={label}>
          <Icon name={icon} />
          {t(label)}
        </Button>
      ))}
    </>
  )
}
