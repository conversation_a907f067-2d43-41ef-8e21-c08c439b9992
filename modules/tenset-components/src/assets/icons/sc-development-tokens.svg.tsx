import { AssetProps } from './index'

export default function ScDevelopmentTokens({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M113.886 104.642C111.082 104.797 106.236 105.092 101.234 105.402C101.981 105.134 102.615 104.909 103.066 104.769C105.475 104.009 106.574 101.055 107.997 97.904C109.42 94.753 109.857 93.7965 111.153 90.9268C112.449 88.0572 106.926 84.4702 104.306 90.2798C101.685 96.0894 100.149 98.6214 96.7117 100.056C95.8664 100.408 94.5279 100.942 92.9782 101.533L92.6964 101.491C93.8235 99.8874 94.1334 97.3976 94.5843 94.753C95.176 91.3488 95.3592 90.322 95.8946 87.2132C96.4299 84.1044 90.1886 82.0085 89.1037 88.2823C88.0189 94.5561 87.1454 97.3835 84.1726 99.6342C83.8345 99.8874 83.75 99.9437 83.2568 100.309L82.9328 100.464C82.6933 100.211 82.4115 100.014 82.1016 99.8874C79.3683 98.6636 71.2813 95.9065 61.8418 93.3885C52.4023 90.8846 48.7251 95.4845 44.0335 94.0075C39.3419 92.5304 29.5219 89.5342 29.5219 89.5342C27.2395 89.1263 24.0414 96.3426 22.3789 105.641C22.0548 107.484 21.8012 109.27 21.6463 110.944C21.6463 110.944 45.8087 115.459 52.7263 117.626C56.2626 118.737 64.0396 119.046 70.3515 118.231C76.6633 117.415 111.294 112.562 116.422 112.055C121.55 111.549 121.55 104.206 113.9 104.628L113.886 104.642Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M46.3722 61.5834L66.7306 73.3152C69.2666 74.7781 72.3802 74.7781 74.9021 73.3152L95.2605 61.5834C97.7965 60.1205 99.3463 57.4337 99.3463 54.5078V31.0443C99.3463 28.1325 97.7824 25.4316 95.2605 23.9687L74.9021 12.237C72.3661 10.774 69.2525 10.774 66.7306 12.237L46.3722 23.9687C43.8362 25.4316 42.2864 28.1184 42.2864 31.0443V54.5078C42.2864 57.4196 43.8503 60.1205 46.3722 61.5834Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M14.4046 114.897C14.94 114.897 15.3768 114.461 15.3768 113.926C15.3768 113.392 14.94 112.955 14.4046 112.955C13.8692 112.955 13.4325 113.392 13.4325 113.926C13.4325 114.461 13.8692 114.897 14.4046 114.897ZM14.4046 113.49C14.6441 113.49 14.8414 113.687 14.8414 113.926C14.8414 114.165 14.6441 114.362 14.4046 114.362C14.1651 114.362 13.9679 114.165 13.9679 113.926C13.9679 113.687 14.1651 113.49 14.4046 113.49ZM111.66 101.73C109.589 101.843 106.363 102.04 102.798 102.265C104.221 101.027 105.123 98.9871 106.081 96.863L109.617 89.0278C110.068 88.0291 109.927 86.9178 109.237 85.9612C108.405 84.8218 106.983 84.1607 105.574 84.3013C104.601 84.3998 102.756 84.9906 101.488 87.804C98.8251 93.7121 97.4021 95.9065 94.2603 97.2147C93.6826 97.4538 92.8373 97.7914 91.8792 98.1712C92.4005 96.8349 92.6823 95.2594 92.95 93.5995L94.4293 85.1454C94.7393 83.387 93.4008 81.7412 91.3861 81.3895C89.2869 81.0378 86.8918 82.3039 86.2437 85.9753C85.1307 92.3616 84.2994 94.8374 81.5098 96.9474C81.2844 97.1162 81.1576 97.2147 80.8477 97.4257C80.6504 97.271 80.4391 97.1444 80.2278 97.0459C77.2409 95.6955 68.8017 92.8962 59.8553 90.5189C54.2338 89.0278 50.5707 89.9844 47.6261 90.744C45.5269 91.2926 43.8785 91.7146 42.0751 91.1519C37.3835 89.6749 27.5495 86.6786 27.5495 86.6786C27.5495 86.6786 27.5495 86.6786 27.5354 86.6786L21.5899 85.061C21.2095 84.9625 20.8291 85.1735 20.7305 85.5533C20.6319 85.9331 20.8432 86.3129 21.2236 86.4114L25.3658 87.5367C22.9284 89.8859 20.7305 96.5536 19.5189 103.334C19.1948 105.106 18.9553 106.893 18.7863 108.623C18.7863 108.623 18.7863 108.637 18.7863 108.651C18.3213 113.434 18.4763 117.752 19.4907 120.031L11.2065 118.737C10.8261 118.681 10.4597 118.934 10.4034 119.328C10.347 119.722 10.6006 120.073 10.9951 120.13L21.2377 121.719C21.2377 121.719 21.2941 121.719 21.3222 121.719C21.3927 121.719 21.4631 121.733 21.5336 121.733C24.5204 121.733 26.9155 114.278 27.8172 111.056C35.4675 112.576 46.1609 114.77 50.3453 116.078C52.6277 116.796 56.4599 117.176 60.5456 117.176C63.138 117.176 65.8149 117.021 68.2804 116.711C70.3797 116.444 75.6207 115.713 81.8621 114.869C94.1194 113.181 110.913 110.888 114.323 110.55C117.253 110.255 118.676 108.004 118.521 106.035C118.338 103.714 116.168 101.477 111.702 101.716L111.66 101.73ZM94.7815 98.5089C98.5574 96.9334 100.178 94.0637 102.756 88.3667C103.488 86.749 104.531 85.8065 105.686 85.6799C106.588 85.5814 107.532 86.0175 108.067 86.763C108.293 87.0866 108.631 87.7196 108.307 88.437L104.771 96.2722C103.573 98.9731 102.545 101.294 100.657 101.885C100.22 102.026 99.5999 102.237 98.8955 102.49C92.0906 102.912 85.018 103.376 82.3552 103.545C82.3974 103.432 82.4397 103.32 82.482 103.193C86.2014 101.815 92.4005 99.4935 94.7815 98.4948V98.5089ZM82.4115 98.0024C85.6801 95.5267 86.5396 92.3616 87.6103 86.2004C88.1175 83.3026 89.7659 82.5149 91.1184 82.754C92.1751 82.9369 93.2036 83.809 93.0204 84.8781L91.5411 93.3463C91.1748 95.5267 90.8367 97.5945 89.9209 98.8746C89.9209 98.8746 89.9209 98.9027 89.9068 98.9027C87.4413 99.8452 84.6376 100.886 82.7074 101.604C82.7074 100.45 82.3693 99.3247 81.7916 98.4526C82.0311 98.2838 82.1579 98.1853 82.4115 97.9884V98.0024ZM21.4209 120.341C20.3219 120.144 19.533 116.05 20.0683 109.594C21.1954 109.805 23.4919 110.241 26.3801 110.817C24.4922 117.569 22.3507 120.495 21.4068 120.341H21.4209ZM114.14 109.172C110.716 109.509 93.908 111.816 81.6226 113.504C75.3812 114.362 70.1401 115.08 68.0409 115.347C61.9968 116.135 54.2197 115.881 50.7116 114.77C44.3716 112.787 24.1118 108.932 20.1951 108.187C20.3501 106.738 20.5615 105.205 20.8573 103.573C22.6184 93.7543 25.7743 87.804 27.1691 88.0431C27.8595 88.2541 37.1018 91.0816 41.6102 92.4883C43.794 93.1775 45.7101 92.6711 47.9361 92.0944C50.8666 91.3348 54.1916 90.4767 59.4467 91.8693C69.2244 94.4717 77.1141 97.2006 79.5938 98.326C80.7209 98.8324 81.4676 100.647 81.214 102.293C81.0731 103.249 80.4673 104.895 78.1003 105.191C76.142 105.43 74.4654 105.106 72.338 104.67C68.2663 103.854 62.7012 102.729 50.937 105.064C50.5566 105.134 50.303 105.514 50.3876 105.894C50.458 106.274 50.8384 106.527 51.2188 106.443C62.7012 104.15 68.1113 105.247 72.0703 106.049C73.7187 106.386 75.184 106.682 76.776 106.682C77.2691 106.682 77.7763 106.654 78.2976 106.583C79.6219 106.414 80.7068 105.852 81.4676 105.008C83.1864 104.895 104.447 103.531 111.73 103.123C115.253 102.926 116.943 104.501 117.07 106.147C117.169 107.483 116.197 108.961 114.14 109.157V109.172ZM78.8189 20.4239L89.7377 26.7117C90.6394 27.2322 91.203 28.2028 91.203 29.2578V33.928C91.203 34.3219 91.5129 34.6314 91.9074 34.6314C92.3019 34.6314 92.6119 34.3219 92.6119 33.928V29.2578C92.6119 27.7105 91.7806 26.2757 90.4422 25.502L79.5233 19.2141C79.1852 19.0172 78.7625 19.1297 78.5653 19.4673C78.368 19.8049 78.4807 20.2269 78.8189 20.4239ZM112.125 12.7715C112.519 12.7715 112.829 12.462 112.829 12.0682V9.94406C112.829 9.55019 112.519 9.24072 112.125 9.24072C111.73 9.24072 111.42 9.55019 111.42 9.94406V12.0682C111.42 12.462 111.73 12.7715 112.125 12.7715ZM59.968 41.1724C59.968 41.1724 59.968 41.2146 59.9821 41.2287L68.0127 54.8313C68.1395 55.0423 68.3649 55.183 68.6185 55.183C68.8721 55.183 69.0976 55.0564 69.2244 54.8313L77.255 41.2287C77.255 41.2287 77.255 41.1865 77.2691 41.1724C77.3114 41.088 77.3255 41.0177 77.3395 40.9192C77.3395 40.8911 77.3395 40.863 77.3395 40.8348C77.3395 40.7364 77.3114 40.6238 77.2691 40.5254L69.2385 26.9227C68.9849 26.5007 68.2804 26.5007 68.0268 26.9227L59.9962 40.5254C59.9398 40.6238 59.9257 40.7223 59.9257 40.8348C59.9257 40.863 59.9257 40.8911 59.9257 40.9192C59.9257 41.0036 59.9539 41.088 59.9962 41.1724H59.968ZM68.6185 53.087L62.4617 42.6635L68.2804 45.7442C68.379 45.8004 68.4917 45.8286 68.6045 45.8286C68.7172 45.8286 68.8299 45.8004 68.9285 45.7442L74.7472 42.6635L68.5904 53.087H68.6185ZM68.6185 28.653L75.663 40.5957L68.6185 44.3234L61.5741 40.5957L68.6185 28.653ZM112.125 20.5083C112.519 20.5083 112.829 20.1988 112.829 19.8049V17.6808C112.829 17.287 112.519 16.9775 112.125 16.9775C111.73 16.9775 111.42 17.287 111.42 17.6808V19.8049C111.42 20.1988 111.73 20.5083 112.125 20.5083ZM114.943 15.5708H117.07C117.465 15.5708 117.775 15.2613 117.775 14.8675C117.775 14.4736 117.465 14.1641 117.07 14.1641H114.943C114.548 14.1641 114.238 14.4736 114.238 14.8675C114.238 15.2613 114.548 15.5708 114.943 15.5708ZM107.18 15.5708H109.307C109.702 15.5708 110.012 15.2613 110.012 14.8675C110.012 14.4736 109.702 14.1641 109.307 14.1641H107.18C106.785 14.1641 106.475 14.4736 106.475 14.8675C106.475 15.2613 106.785 15.5708 107.18 15.5708ZM43.8222 59.9939L64.1806 71.7256C65.5472 72.5133 67.0829 72.9072 68.6185 72.9072C70.1542 72.9072 71.6899 72.5133 73.0565 71.7256L93.4149 59.9939C96.1482 58.4184 97.8529 55.4784 97.8529 52.3134V28.8499C97.8529 25.6849 96.1482 22.7449 93.4149 21.1694L73.0565 9.43766C70.3233 7.86217 66.9138 7.86217 64.1806 9.43766L43.8222 21.1694C41.0889 22.7449 39.3842 25.6849 39.3842 28.8499V52.3134C39.3842 55.4643 41.0889 58.4043 43.8222 59.9939ZM40.7931 28.8499C40.7931 26.1913 42.2301 23.7155 44.5266 22.3932L64.885 10.6615C66.0403 10.0003 67.3224 9.66273 68.6185 9.66273C69.9147 9.66273 71.1968 10.0003 72.3521 10.6615L92.7105 22.3932C95.0211 23.7155 96.444 26.1913 96.444 28.8499V52.3134C96.444 54.972 95.007 57.4478 92.7105 58.77L72.3521 70.5018C70.0556 71.8241 67.1815 71.8241 64.885 70.5018L44.5266 58.77C42.216 57.4337 40.7931 54.9579 40.7931 52.3134V28.8499Z"
        fill={color ?? 'url(#paint0_linear_940_1085)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_940_1085"
          x1="38.8347"
          y1="136.278"
          x2="112.136"
          y2="9.14142"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
