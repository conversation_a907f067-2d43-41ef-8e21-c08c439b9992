import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'

import {
  useZealyNftContract,
  useZealyRewardsContract,
} from 'modules/tenset-web3/lib/hooks/zealy'
import { detectLocale } from '~/i18n/detect-locale'
import { i18next, Namespace } from '~/i18n'
import { withCache } from '~/utils'
import { zealyRewards } from '~/assets/images'
import { <PERSON><PERSON>, <PERSON> } from '~/tenset-components'
import { ConnectWallet } from '~/components/connect-wallet'
import { useWallet } from '~/tenset-web3'
import { TableComponent } from '~/components/zealy-rewards/token-table'
import { TokenInfoComponent } from '~/components/zealy-rewards/token-info'
import { SpecialRewards } from '~/sections/zealy/special-rewards'
import { useZealyRewards } from '~/hooks/zealy'
import { useZealyNft } from '~/hooks/zealy/use-zealy-nft'
import { isDevelopment } from '~/environment'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.ZEALY)

  const title = t('meta.title')
  const description = t('meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.ZEALY, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function ZealyRewards() {
  const { t } = useTranslation([Namespace.ZEALY, Namespace.COMMON])
  const { wallet } = useWallet()
  const { zealyRewardsContract } = useZealyRewardsContract(isDevelopment)
  const { zealyNftContract } = useZealyNftContract()

  const {
    isDataLoading,
    isCorrectNetwork,
    isRewardClaimed,
    zealyRewardsData,
    handleClaim,
  } = useZealyRewards({ wallet, zealyRewardsContract })

  const {
    isDataLoading: isDataLoadingNft,
    zealyNftData,
    handleClaim: handleClaimNft,
    isNftClaimed,
  } = useZealyNft({ wallet, zealyNftContract, isCorrectNetwork })

  const renderButton = () => {
    if (!wallet?.isConnected) {
      return (
        <ConnectWallet buttonConnectLabel={t('hero.button.connect-wallet')} />
      )
    }
    const contractChain = zealyRewardsContract?.chain
    if (contractChain === undefined) return

    if (!isCorrectNetwork) {
      return (
        <Button onClick={() => wallet?.switchNetwork(contractChain)}>
          {t('hero.button.switch-network')}
        </Button>
      )
    }

    return (
      <Button
        onClick={handleClaim}
        loading={isDataLoading}
        disabled={
          isRewardClaimed ||
          zealyRewardsData?.data.rewardInEther === 0 ||
          !zealyRewardsData?.data.rewardInEther
        }
      >
        {t('hero.button.claim')}
      </Button>
    )
  }

  const isSpecialRewardVisible = false

  return (
    <>
      <div className="container gap-16 lg:gap-32 lg:px-[72px]">
        <Hero
          title={t('hero.title')}
          description={t('hero.description')}
          leftContent={
            <div className="grid gap-6">
              {wallet?.isConnected && isCorrectNetwork && zealyRewardsData && (
                <>
                  <TokenInfoComponent />
                  <TableComponent
                    isDataLoading={isDataLoading}
                    rewards={zealyRewardsData.data.rewardInEther}
                    timeToClaim={zealyRewardsData.data.claimsEndsAtDateFormat}
                  />
                </>
              )}

              <div>{renderButton()}</div>
            </div>
          }
          rightContent={<img src={zealyRewards} alt={t('hero.title')} />}
        />

        {isSpecialRewardVisible &&
          wallet?.isConnected &&
          isCorrectNetwork &&
          zealyNftData && (
            <SpecialRewards
              isDataLoading={isDataLoadingNft}
              handleClaim={handleClaimNft}
              tokenId={zealyNftData.data.tokenId}
              isNftClaimed={isNftClaimed}
            />
          )}
      </div>
    </>
  )
}
