import { useEffect, useState } from 'react'
import type { Id } from 'react-toastify'
import { toast } from 'react-toastify'
import { useTranslation } from 'react-i18next'

import { Button, ButtonVariant } from '~/tenset-components'
import { Namespace } from '~/i18n'
import type { Wallet, ZealyNftContract } from '~/tenset-web3'

export interface ZealyNftData {
  error: boolean
  message: string
  data: {
    address: string
    tokenId: string | null
    proofs: string[]
  }
}

const ZealyNftDataModel = {
  error: false,
  message: '',
  data: {
    address: '',
    tokenId: null,
    proofs: ['', ''],
  },
}

export function useZealyNft({
  wallet,
  zealyNftContract,
  isCorrectNetwork,
}: {
  wallet: Wallet | null
  zealyNftContract: ZealyNftContract | undefined
  isCorrectNetwork: boolean
}) {
  const { t } = useTranslation([Namespace.ZEALY, Namespace.COMMON])

  const [isDataLoading, setIsDataLoading] = useState(true)

  const [zealyNftData, setZealyNftData] = useState<ZealyNftData | null>(null)

  useEffect(() => {
    if (!wallet?.account || !zealyNftContract?.address || !isCorrectNetwork)
      return

    const fetchRewardsAndStatus = async () => {
      try {
        const url = `https://data.tenset.io/proofs/${zealyNftContract.address.toLowerCase()}/whitelist/${
          wallet.account
        }`
        const response = await fetch(url)
        const zealyNftData = await response.json()

        if (zealyNftData.error && zealyNftData.message.includes('No proofs')) {
          setZealyNftData(ZealyNftDataModel)
          return
        }

        setZealyNftData({
          ...zealyNftData,
          data: {
            ...zealyNftData.data,
          },
        })
      } catch (error) {
        console.error('Error fetching reward data or status:', error)
        toast.error('Failed to load data about your zealy rewards.')
      } finally {
        setIsDataLoading(false)
      }
    }

    if (wallet?.account && zealyNftContract?.address && isCorrectNetwork) {
      fetchRewardsAndStatus()
    }
  }, [
    wallet,
    zealyNftContract,
    wallet?.account,
    zealyNftContract?.address,
    isCorrectNetwork,
  ])

  const [isNftClaimed, setIsNftClaimed] = useState(false)

  async function handleClaim() {
    if (!zealyNftContract) {
      toast.error('Failed to connect with contract.')
      return
    }

    if (
      !zealyNftData ||
      !zealyNftData.data.proofs ||
      !zealyNftData.data.tokenId
    ) {
      toast.error('Failed to load data about your zealy rewards.')
      return
    }

    const MessageWithHash = ({
      message,
      txHash,
    }: {
      message: string
      txHash: string
    }) => (
      <div>
        <div>{message}</div>
        <Button
          className="mt-2"
          variant={ButtonVariant.Link}
          to={`https://bscscan.com/tx/${txHash}`}
        >
          View on Bsc Scan
        </Button>
      </div>
    )

    const toastTxPending = (txHash: string, id: Id) =>
      toast.update(id, {
        render: MessageWithHash({
          message: t('toast.waiting'),
          txHash,
        }),
        closeButton: false,
      })

    const toastPendingMessage = t('toast.pending')

    const toastId = toast.loading(toastPendingMessage)

    try {
      const { tokenId, proofs } = zealyNftData.data

      const txHash = await wallet
        ?.interact(zealyNftContract)
        .mint(tokenId, proofs, txHash => toastTxPending(txHash, toastId))

      txHash &&
        toast.update(toastId, {
          render: MessageWithHash({
            message: t('toast.success'),
            txHash,
          }),
          type: 'success',
          isLoading: false,
          autoClose: 5000,
          closeButton: true,
        })

      setIsNftClaimed(true)
    } catch (error) {
      console.error('Error claiming rewards', error)

      const render = t('toast.error')

      toast.update(toastId, {
        render,
        type: 'error',
        isLoading: false,
        autoClose: 5000,
        closeButton: true,
      })
    }
  }

  return {
    isDataLoading,
    zealyNftData,
    handleClaim,
    isNftClaimed,
  }
}
