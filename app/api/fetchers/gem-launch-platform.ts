import { getSubscriptionAddresses } from '../../database/gem-launch-platform'
import { adaptLock, adaptNft } from '../adapters/gem-launch-platform'
import type {
  GlpSubscriptionInfo,
  GlpSubscriptionInfoForMany,
} from '../types/gem-launch-platform'

import { getGLPCurrentLaunch } from './glp-current-launch'

import { BSC_NETWORK, MAINNET_NETWORK } from '~/config'
import { getServerEnvironment } from '~/environment/environment.server'
import { isDevelopment } from '~/environment'

export const fetchGlpSubscriptionInfo = async (
  address: string,
  resetCache?: boolean
): Promise<GlpSubscriptionInfo> => {
  const parameters = new URLSearchParams({
    action: 'getTglpSubscriptionInfo',
    address,
  })

  if (resetCache) {
    parameters.append('resetCache', '1')
  }

  // if (isDevelopment) {
  //   parameters.append('testnet', '1')
  // }

  const TGLP_SUBSCRIPTION_INFO_URL = `${getServerEnvironment().DATA_TENSET_URL}?${parameters}`

  return fetch(TGLP_SUBSCRIPTION_INFO_URL)
    .then(response => response.json())
    .then(({ data }) => {
      const { locks, nfts } = data

      if (isDevelopment) {
        nfts.push(
          // BNB Smart Chain
          {
            collection: 'TGLP Crypto Mayhem',
            tokenId: 1,
            contractAddress: '******************************************',
            chainId: BSC_NETWORK,
            tokenName: 'TGLP Crypto Mayhem NFT #1',
            image: 'https://cdn-static.tenset.io/mayhem/image/1.jpg',
          },

          // MainNet
          {
            collection: 'TGLP Genesis',
            tokenId: 1,
            contractAddress: '******************************************',
            chainId: MAINNET_NETWORK,
            tokenName: 'TGLP Genesis NFT #1',
            image: 'https://cdn-static.tenset.io/TGLP_genesis/1.png',
          },
          {
            collection: 'TGLP Genesis',
            tokenId: 2,
            contractAddress: '******************************************',
            chainId: MAINNET_NETWORK,
            tokenName: 'TGLP Genesis NFT #2',
            image: 'https://cdn-static.tenset.io/TGLP_genesis/2.png',
          },
          {
            collection: 'TGLP Sumeragi',
            tokenId: 1,
            contractAddress: '******************************************',
            chainId: MAINNET_NETWORK,
            tokenName: 'TGLP Sumeragi NFT #1',
            image: 'https://cdn-static.tenset.io/sumeragi/images/1.jpg',
          },

          {
            collection: 'TGLP Arrland',
            tokenId: 1,
            contractAddress: '0x07Fc9398c75068F301d7Ff5F85dB4BaeAF779a9d',
            chainId: MAINNET_NETWORK,
            tokenName: 'TGLP Arrland NFT #1',
            image: 'https://cdn-static.tenset.io/arrland/1.png',
          },
          {
            collection: 'TGLP Alaska',
            tokenId: 1,
            contractAddress: '0x6F70EbE834e99356646F885B08A34c8b003c9080',
            chainId: MAINNET_NETWORK,
            tokenName: 'TGLP Alaska NFT #1',
            image: 'https://cdn-static.tenset.io/alaska/images/1.jpg',
          }
        )
      }

      const locksAdapted = locks.map((lock: Record<string, never>) =>
        adaptLock(lock)
      )

      const nftsAdapted = nfts.map((nft: Record<string, never>) =>
        adaptNft(nft)
      )

      return {
        locks: locksAdapted,
        nfts: nftsAdapted,
      }
    })
}

export const fetchGlpSubscriptionInfoForMany = async (
  address: string,
  resetCache?: boolean
): Promise<GlpSubscriptionInfoForMany> => {
  const currentLaunch = await getGLPCurrentLaunch()

  const assigningAddresses = currentLaunch
    ? await getSubscriptionAddresses(address, currentLaunch.launchId)
    : []

  const primarySubscriptionPromise = fetchGlpSubscriptionInfo(
    address,
    resetCache
  ).then(({ locks, nfts }) => ({
    locks,
    nfts,
  }))

  const assigningAddressesPromises = assigningAddresses.map(address => ({
    address,
    tglpInfo: fetchGlpSubscriptionInfo(address, resetCache),
  }))

  const { locks, nfts, fromDelegation } = await Promise.all([
    primarySubscriptionPromise,
    assigningAddressesPromises,
  ]).then(async ([{ locks, nfts }, fromDelegation]) => ({
    locks,
    nfts,
    fromDelegation: await Promise.all(
      fromDelegation.map(async ({ address, tglpInfo }) => {
        const { locks, nfts } = await tglpInfo

        return {
          address,
          locks,
          nfts,
        }
      })
    ),
  }))

  return {
    locks,
    nfts,
    assigningAddresses,
    fromDelegation,
  }
}
