import { AssetProps } from './index'

export default function Airdrop({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M69.1462 82.873C59.6991 82.873 52.035 90.5188 52.035 99.9299C52.035 108.319 58.1024 115.301 66.1125 116.721C67.0971 116.907 68.1084 117 69.1462 117C69.8914 117 70.6099 116.947 71.3151 116.854C79.7376 115.792 86.2441 108.624 86.2441 99.9299C86.2441 90.5055 78.58 82.873 69.1462 82.873Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M68.92 10.4645C47.4578 10.4645 30.3067 27.8133 30.3067 49.1974C38.6893 45.4542 52.1548 47.4586 56.4126 53.4583C58.1557 50.9894 63.0921 49.1974 68.92 49.1974C74.748 49.1974 79.6711 50.9761 81.4275 53.4583C85.6853 47.4586 99.1507 45.4542 107.533 49.1974C107.533 27.8133 90.3822 10.4645 68.92 10.4645Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M105.79 92.2576C105.418 92.2576 105.125 92.5497 105.125 92.9213V97.3813C105.125 97.753 105.418 98.045 105.79 98.045C106.163 98.045 106.456 97.753 106.456 97.3813V92.9213C106.456 92.5497 106.163 92.2576 105.79 92.2576ZM16.9743 72.3602C16.6017 72.3602 16.309 72.6522 16.309 73.0239V76.1565C16.309 76.5282 16.6017 76.8202 16.9743 76.8202C17.3469 76.8202 17.6396 76.5282 17.6396 76.1565V73.0239C17.6396 72.6522 17.3469 72.3602 16.9743 72.3602ZM105.79 81.6519C105.418 81.6519 105.125 81.9439 105.125 82.3156V86.7755C105.125 87.1472 105.418 87.4392 105.79 87.4392C106.163 87.4392 106.456 87.1472 106.456 86.7755V82.3156C106.456 81.9439 106.163 81.6519 105.79 81.6519ZM14.8055 70.2098H11.6653C11.2927 70.2098 11 70.5019 11 70.8735C11 71.2452 11.2927 71.5372 11.6653 71.5372H14.8055C15.178 71.5372 15.4707 71.2452 15.4707 70.8735C15.4707 70.5019 15.178 70.2098 14.8055 70.2098ZM22.2833 70.2098H19.1431C18.7706 70.2098 18.4778 70.5019 18.4778 70.8735C18.4778 71.2452 18.7706 71.5372 19.1431 71.5372H22.2833C22.6559 71.5372 22.9486 71.2452 22.9486 70.8735C22.9486 70.5019 22.6559 70.2098 22.2833 70.2098ZM16.9743 64.9136C16.6017 64.9136 16.309 65.2056 16.309 65.5773V68.7099C16.309 69.0816 16.6017 69.3736 16.9743 69.3736C17.3469 69.3736 17.6396 69.0816 17.6396 68.7099V65.5773C17.6396 65.2056 17.3469 64.9136 16.9743 64.9136ZM79.6179 98.8016L81.015 98.9078C81.015 98.9078 81.0416 98.9078 81.0682 98.9078C81.4142 98.9078 81.7069 98.6423 81.7335 98.2972C81.7601 97.9255 81.4807 97.607 81.1214 97.5804L79.7243 97.4742C79.3518 97.4477 79.0324 97.7264 79.0058 98.0848C78.9792 98.4432 79.2586 98.7751 79.6179 98.8016ZM66.8044 96.5716V91.4479C66.8044 91.0763 66.5117 90.7842 66.1391 90.7842C65.7666 90.7842 65.4738 91.0763 65.4738 91.4479V96.3592C63.5977 95.961 61.868 94.9788 61.868 93.2266C61.868 91.4745 63.8505 89.948 66.2988 89.948C68.0685 89.948 69.6652 90.7311 70.3704 91.9391C70.5566 92.2576 70.9558 92.3638 71.2752 92.178C71.5945 91.9921 71.7009 91.5939 71.5147 91.2754C70.6498 89.7887 68.8269 88.7932 66.8044 88.6472V87.0278C66.8044 86.6561 66.5117 86.3641 66.1391 86.3641C65.7666 86.3641 65.4738 86.6561 65.4738 87.0278V88.687C62.6929 89.0055 60.5374 90.917 60.5374 93.2266C60.5374 95.5363 62.4135 97.1424 65.4738 97.6999V105.266C63.8771 105.067 62.48 104.297 61.7615 103.182C61.5619 102.877 61.1495 102.784 60.8434 102.983C60.5374 103.182 60.4443 103.593 60.6438 103.899C61.7083 105.545 63.7707 106.567 66.0859 106.633C66.1125 106.633 66.1258 106.633 66.1391 106.633C66.1391 106.633 66.1657 106.633 66.179 106.633C66.219 106.633 66.2589 106.633 66.2988 106.633C69.7982 106.633 72.659 104.363 72.659 101.563C72.659 98.7618 69.7051 97.0097 66.8044 96.5583V96.5716ZM66.8044 105.279V97.9255C69.1329 98.3503 71.3284 99.6644 71.3284 101.576C71.3284 103.487 69.3458 105.093 66.8044 105.279ZM74.0694 81.1873L105.218 46.8347C105.218 46.8347 105.245 46.7816 105.271 46.755C105.285 46.7285 105.311 46.7019 105.325 46.6754C105.325 46.6754 105.325 46.6754 105.325 46.6621C105.338 46.6223 105.351 46.5825 105.351 46.5427C105.351 46.5028 105.365 46.4763 105.378 46.4365C105.378 46.4365 105.378 46.4232 105.378 46.4099C105.391 24.6807 87.761 7 66.1125 7C44.464 7 26.8339 24.6807 26.8339 46.3966C26.8339 46.3966 26.8339 46.4099 26.8339 46.4232C26.8339 46.463 26.8472 46.4896 26.8605 46.5294C26.8605 46.5692 26.8605 46.609 26.8871 46.6488C26.8871 46.6488 26.8871 46.6488 26.8871 46.6621C26.9004 46.6887 26.927 46.7152 26.9403 46.7418C26.9536 46.7683 26.9669 46.7949 26.9935 46.8214L58.5948 81.174C52.6604 84.0411 48.5489 90.1073 48.5489 97.1158C48.5489 105.717 54.6962 113.058 63.1586 114.571C64.1566 114.757 65.221 114.85 66.3254 114.85C67.0971 114.85 67.8556 114.797 68.5741 114.704C77.4224 113.589 84.0886 106.036 84.0886 97.1158C84.0886 90.1073 79.9771 84.0411 74.0428 81.174L74.0694 81.1873ZM72.792 80.6298C71.8207 80.2448 70.8095 79.9528 69.7583 79.7404L79.3651 50.7903C83.4233 45.5073 95.4251 43.503 103.595 46.6488L72.792 80.6165V80.6298ZM66.1258 47.0603C71.3816 47.0603 76.1717 48.6134 77.9414 50.8699L68.4277 79.5413C67.7491 79.4617 67.0572 79.4086 66.3653 79.4086C65.5138 79.4086 64.6755 79.4882 63.8505 79.6077L54.3103 50.8699C56.08 48.6134 60.8833 47.0603 66.1258 47.0603ZM53.8313 49.4231L52.4475 45.2418C54.5365 42.6534 60.085 40.8615 66.1391 40.8615C72.1933 40.8615 77.7285 42.6534 79.8308 45.2418L78.447 49.4231C76.1185 47.1798 71.3949 45.733 66.1524 45.733C60.91 45.733 56.1731 47.1798 53.8579 49.4231H53.8313ZM80.2965 48.016L81.148 45.4675C82.9709 43.41 87.4151 42.0296 92.2717 42.0296C97.1283 42.0296 100.987 43.2375 102.983 45.0162C95.3985 42.5605 85.5389 43.941 80.2965 48.016ZM104.007 44.1932C101.772 42.0827 97.3279 40.7022 92.2717 40.7022C88.1469 40.7022 84.3813 41.6313 81.9198 43.1578C82.1593 42.4145 82.4121 41.565 82.625 40.7287C84.8071 32.4326 81.6404 19.0526 69.8647 8.51321C88.3332 10.3317 102.956 25.4771 104.021 44.2065L104.007 44.1932ZM66.1125 8.32738C66.6314 8.32738 67.1504 8.34065 67.656 8.3672C80.0969 18.6942 83.4632 32.1937 81.3077 40.3969C80.9884 41.6313 80.5626 42.9322 80.2699 43.8215C77.622 41.2331 72.1666 39.5474 66.0992 39.5474C60.0318 39.5474 54.5764 41.2331 51.9286 43.8215C51.6358 42.9322 51.2234 41.6313 50.8907 40.3969C48.7352 32.1937 52.1015 18.6942 64.5424 8.3672C65.0614 8.34065 65.567 8.32738 66.0859 8.32738H66.1125ZM51.9419 48.0293C46.7127 43.9675 36.8398 42.5738 29.2555 45.0294C31.2381 43.2508 35.3629 42.0296 39.9667 42.0296C44.8233 42.0296 49.2674 43.41 51.0903 45.4675L51.9419 48.016V48.0293ZM62.3869 8.51321C50.598 19.0526 47.4445 32.4459 49.6267 40.7287C49.8396 41.565 50.1057 42.4145 50.3319 43.1578C47.8703 41.6313 44.0915 40.7022 39.98 40.7022C34.9238 40.7022 30.4797 42.0959 28.2443 44.1932C29.2954 25.4771 43.9185 10.3317 62.3869 8.51321ZM28.6435 46.6621C36.8132 43.5162 48.8017 45.5206 52.86 50.8035L62.4933 79.8466C61.6019 80.0457 60.737 80.3112 59.8987 80.6431L28.6435 46.6621ZM68.4277 113.403C66.7911 113.628 65.0081 113.575 63.4248 113.283C55.5877 111.89 49.9061 105.093 49.9061 97.1424C49.9061 88.1029 57.2775 80.7492 66.352 80.7492C75.4266 80.7492 82.7847 88.1029 82.7847 97.1424C82.7847 105.385 76.6108 112.381 68.4277 113.416V113.403ZM102.717 89.1781H98.246C97.8734 89.1781 97.5807 89.4701 97.5807 89.8418C97.5807 90.2135 97.8734 90.5055 98.246 90.5055H102.717C103.089 90.5055 103.382 90.2135 103.382 89.8418C103.382 89.4701 103.089 89.1781 102.717 89.1781ZM73.2311 108.584C73.0581 108.253 72.659 108.133 72.3263 108.306C72.007 108.478 71.8739 108.876 72.0469 109.208L72.7122 110.443C72.8319 110.668 73.0581 110.788 73.2976 110.788C73.4041 110.788 73.5105 110.761 73.617 110.708C73.9363 110.536 74.0694 110.137 73.8964 109.806L73.2311 108.571V108.584ZM113.335 89.1781H108.864C108.491 89.1781 108.199 89.4701 108.199 89.8418C108.199 90.2135 108.491 90.5055 108.864 90.5055H113.335C113.707 90.5055 114 90.2135 114 89.8418C114 89.4701 113.707 89.1781 113.335 89.1781ZM61.5486 84.373C61.6285 84.373 61.695 84.373 61.7748 84.3332C62.653 84.0146 63.5711 83.8022 64.5025 83.6695C64.8618 83.6164 65.1279 83.2845 65.0747 82.9261C65.0214 82.5678 64.6888 82.3023 64.3295 82.3554C63.305 82.4881 62.2938 82.7403 61.3357 83.0854C60.9898 83.2049 60.8035 83.5898 60.9366 83.935C61.0297 84.2004 61.2958 84.373 61.5619 84.373H61.5486ZM66.1391 106.925C65.7666 106.925 65.4738 107.217 65.4738 107.589V108.239C65.4738 108.611 65.7666 108.903 66.1391 108.903C66.5117 108.903 66.8044 108.611 66.8044 108.239V107.589C66.8044 107.217 66.5117 106.925 66.1391 106.925ZM57.8363 84.8774C54.6562 87.0808 52.4741 90.3727 51.7024 94.1292C51.6225 94.4876 51.862 94.846 52.2213 94.9124C52.2612 94.9124 52.3144 94.9124 52.3543 94.9124C52.6604 94.9124 52.9398 94.7 53.0063 94.3814C53.7115 90.9568 55.6941 87.9569 58.5948 85.9526C58.9008 85.7402 58.9673 85.3287 58.7677 85.0234C58.5548 84.7181 58.1424 84.6517 57.8363 84.8509V84.8774ZM75.8656 106.514C75.6128 106.248 75.2004 106.222 74.9209 106.474C74.6548 106.726 74.6282 107.138 74.881 107.416L75.8257 108.438C75.9588 108.584 76.1318 108.651 76.318 108.651C76.4777 108.651 76.6374 108.598 76.7704 108.478C77.0366 108.226 77.0632 107.815 76.8104 107.536L75.8656 106.514ZM78.7929 102.027L80.1102 102.478C80.1767 102.505 80.2566 102.518 80.3231 102.518C80.6025 102.518 80.8553 102.346 80.9485 102.067C81.0682 101.722 80.8819 101.35 80.536 101.231L79.2187 100.779C78.8727 100.673 78.4869 100.846 78.3804 101.191C78.2607 101.536 78.447 101.908 78.7929 102.027ZM79.0723 104.616L77.9014 103.859C77.5954 103.66 77.1829 103.739 76.9833 104.058C76.7837 104.363 76.8636 104.775 77.1829 104.974L78.3538 105.731C78.4603 105.797 78.5933 105.837 78.7131 105.837C78.926 105.837 79.1389 105.731 79.2719 105.531C79.4715 105.226 79.3917 104.815 79.0723 104.616Z"
        fill="url(#paint0_linear_820_778)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_820_778"
          x1="45.6482"
          y1="105.704"
          x2="96.0412"
          y2="18.2176"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
