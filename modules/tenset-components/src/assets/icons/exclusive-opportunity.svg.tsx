import type { AssetProps } from '.'

export default function ExclusiveOpportunity({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '42'}
      height={size ?? '35'}
      viewBox="0 0 42 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g>
        <path
          d="M21 33L1.72845 10.9634L10.4525 0.999878H21H31.5595L40.2716 10.9634L21 33Z"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeMiterlimit="10"
          fill="none"
        />
        <path
          d="M1.72845 10.9634H40.2716"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeMiterlimit="10"
        />
        <path
          d="M10.4525 0.999878L21 33"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeMiterlimit="10"
        />
        <path
          d="M31.5595 0.999878L21.0001 33"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeMiterlimit="10"
        />
        <path
          d="M13.73 10.9634L21 0.999878L28.2701 10.9634"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinejoin="bevel"
        />
      </g>
    </svg>
  )
}
