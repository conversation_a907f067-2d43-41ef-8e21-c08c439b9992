import sumBy from 'lodash.sumby'

import type { VotingDTO, VotingOption, VoteDTO } from '~/state/governance'

export class Voting {
  get uuid() {
    return this.dto.id
  }

  get title() {
    return this.dto.title
  }

  get endsAt() {
    return this._endsAt
  }

  get image() {
    return this.dto.image
  }

  get wistiaVideoUrl() {
    return this.dto.wistiaVideoUrl
  }

  get endsInSeconds() {
    const now = new Date()

    return Math.max(0, this._endsAt.getTime() - now.getTime())
  }

  get description() {
    const { description } = this.dto

    if (Array.isArray(description)) return description

    if (description) return [description]

    return []
  }

  get tiers() {
    if (this._tiers.length === 0) return [{ tier: 1, threshold: 1 }]

    return this._tiers
  }

  get votes() {
    if (this._votes.length > 0) return this._votes

    return this.dto.votes ?? []
  }

  get options() {
    return this._options.sort(
      (a, b) => this.totalVotesByOption(b.key) - this.totalVotesByOption(a.key)
    )
  }

  get requirements() {
    return this.dto.requirements
  }

  protected dto: VotingDTO

  protected _endsAt: Date = new Date()

  protected _tiers: { tier: number; threshold: number }[] = []

  protected _votes: VoteDTO[] = []

  protected _options: VotingOption[] = []

  constructor(dto: VotingDTO) {
    this.dto = dto

    const { endsAt, tiers = {}, options, votes } = this.dto

    this._endsAt = new Date(endsAt.seconds * 1000)

    for (const [tier, threshold] of Object.entries(tiers)) {
      this._tiers.push({ tier: Number.parseInt(tier), threshold })
    }

    for (const [key, value] of Object.entries(options)) {
      this._options.push({ key, value })
    }

    if (Array.isArray(votes)) this._votes = votes
  }

  consume(vote: VoteDTO) {
    const exists = this._votes.find(({ account }) => vote.account === account)

    if (exists) return this

    this._votes.push(vote)

    return this
  }

  totalVotes() {
    if (Array.isArray(this.votes)) {
      return sumBy(this.votes.map(vote => vote.tier || 0))
    }

    return this.votes.total
  }

  totalVotesByOption(key: string) {
    if (Array.isArray(this.votes)) {
      return sumBy(
        this.votes.filter(vote => vote.option === key).map(vote => vote.tier)
      )
    }

    return this.votes.items[key] || 0
  }

  participants() {
    if (Array.isArray(this.votes)) return this.votes.length

    return this.votes.participants
  }

  require(requirement: string) {
    return this.requirements.includes(requirement)
  }

  static make(dto: VotingDTO) {
    return new Voting(dto)
  }
}
