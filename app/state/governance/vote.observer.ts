import { where } from 'firebase/firestore'

import { Query } from './query'
import type { VoteDTO } from './vote.dto'
import type { Dispatcher } from './dispatcher'
import type { FirebaseClient, VotingRepository } from './firebase'
import { VoteHasBeenCreated } from './vote-has-been-created.event'

export class VoteObserver {
  protected initialized = false

  constructor(
    protected firebase: FirebaseClient,
    protected repository: VotingRepository,
    protected dispatcher: Dispatcher
  ) {
    //
  }

  async observe() {
    const query = new Query().where('endsAt', '>=', new Date())

    const votings = await this.repository.get(query)

    for (let index = 0, x = votings.length; index < x; index++) {
      const voting = votings[index]

      this.firebase
        .firestore()
        .collection<VoteDTO>(`votings/${voting.id}/votes`)
        .listen(where('createdAt', '>', Date.now()))
        .subscribe(async votes => {
          if (!votes || votes.length === 0) return

          for (let index = 0, x = votes.length; index < x; index++) {
            this.dispatcher.emit(new VoteHasBeenCreated(voting, votes[index]))
          }
        })
    }
  }
}
