import { useOutletContext } from '@remix-run/react'
import { BigNumber } from 'ethers'
import { parseEther } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'

import type { GlpLock } from '~/api/types'
import { SwitchNetworkButton } from '~/components/switch-network/button'
import { BSC_NETWORK } from '~/config'
import { Namespace } from '~/i18n'
import type { GlpOutletData } from '~/routes/$locale.gem-launch-platform.panel'
import { Button, ButtonVariant } from '~/tenset-components'
import type { Wallet } from '~/tenset-web3'
import {
  chains,
  parseEthersError,
  useGlpContracts,
  useWallet,
} from '~/tenset-web3'

interface WithdrawLockButtonProps {
  lock: GlpLock
  onWithdraw?: (lock: GlpLock) => void
}

export function WithdrawLockButton({
  lock,
  onWithdraw,
}: WithdrawLockButtonProps) {
  const { wallet } = useWallet()

  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  const { blockTimestamp } = useOutletContext<GlpOutletData>()

  const { proofs } = lock

  const isOnCorrectNetwork = wallet?.chain === BSC_NETWORK

  if (!isOnCorrectNetwork) {
    return (
      <SwitchNetworkButton
        label={t('withdraw')}
        id={BSC_NETWORK}
        name={chains[BSC_NETWORK].name}
      />
    )
  }

  if (proofs && proofs?.withdraw.length > 0) {
    return (
      <WithdrawOldLockButton
        lock={lock}
        onWithdraw={onWithdraw}
        t={t}
        wallet={wallet}
        blockTimestamp={blockTimestamp}
      />
    )
  }

  return (
    <WithdrawNewLockButton
      lock={lock}
      onWithdraw={onWithdraw}
      t={t}
      wallet={wallet}
      blockTimestamp={blockTimestamp}
    />
  )
}

interface WithdrawOldLockButtonProps extends WithdrawLockButtonProps {
  t: TFunction
  wallet: Wallet | null
  blockTimestamp: number
}

function WithdrawOldLockButton({
  lock,
  onWithdraw,
  t,
  wallet,
  blockTimestamp,
}: WithdrawOldLockButtonProps) {
  const { glpOldWithdrawContract } = useGlpContracts(BSC_NETWORK)

  const [isWithdrawing, setIsWithdrawing] = useState(false)

  const isLocked = lock.timestamps.unlock > blockTimestamp
  const isWithdrawn = lock.timestamps.withdraw && lock.timestamps.withdraw > 0

  const canWithdraw = !isLocked && !isWithdrawn

  const withdraw = async () => {
    if (
      !wallet ||
      wallet?.isProcessing ||
      !glpOldWithdrawContract ||
      !canWithdraw
    )
      return

    setIsWithdrawing(true)

    const amountParsed = parseEther(lock.amount)

    const unlockTime = lock.timestamps.unlock / 1000

    await wallet
      .interact(glpOldWithdrawContract)
      .withdraw(lock.id, amountParsed, unlockTime, lock.proofs!.withdraw)
      .then(() => onWithdraw?.(lock))
      .catch(error => {
        const parsedError = parseEthersError(error)

        toast.error(parsedError)

        return false
      })
      .finally(() => setIsWithdrawing(false))
  }

  return (
    <Button
      variant={ButtonVariant.Secondary}
      disabled={!canWithdraw || wallet?.isProcessing}
      loading={isWithdrawing}
      onClick={withdraw}
    >
      {t('withdraw')}
    </Button>
  )
}

interface WithdrawNewLockButtonProps extends WithdrawLockButtonProps {
  t: TFunction
  wallet: Wallet | null
  blockTimestamp: number
}

function WithdrawNewLockButton({
  lock,
  onWithdraw,
  t,
  wallet,
  blockTimestamp,
}: WithdrawNewLockButtonProps) {
  const { glpWithdrawContract, glpMembershipContract } =
    useGlpContracts(BSC_NETWORK)

  const [isMembershipWithdrawApproved, setIsMembershipWithdrawApproved] =
    useState<boolean>(false)

  const [isWithdrawing, setIsWithdrawing] = useState(false)

  const canWithdraw = lock.timestamps.unlock <= blockTimestamp

  useEffect(() => {
    if (!wallet || !glpMembershipContract) return
    async function getMembershipWithdrawApproval(): Promise<boolean> {
      if (!wallet || !glpMembershipContract || !glpWithdrawContract)
        return false

      return await wallet
        .interact(glpMembershipContract)
        .isApprovedForAll(wallet.account!, glpWithdrawContract.address)
    }

    getMembershipWithdrawApproval().then(setIsMembershipWithdrawApproved)
  }, [wallet, glpMembershipContract, lock.id])

  async function approveMembershipForWithdraw(): Promise<boolean> {
    if (isMembershipWithdrawApproved) return true

    if (
      !wallet ||
      wallet?.isProcessing ||
      !glpWithdrawContract ||
      !glpMembershipContract
    )
      return false

    return await wallet
      .interact(glpMembershipContract)
      .setApprovalForAll(glpWithdrawContract.address, true)
      .then(() => {
        setIsMembershipWithdrawApproved(true)

        return true
      })
      .catch(error => {
        const parsedError = parseEthersError(error)

        toast.error(parsedError)

        return false
      })
  }

  const withdraw = async () => {
    if (!wallet || wallet?.isProcessing || !glpWithdrawContract || !canWithdraw)
      return

    setIsWithdrawing(true)

    const approved = await approveMembershipForWithdraw()

    if (!approved) {
      setIsWithdrawing(false)

      return
    }

    await wallet
      .interact(glpWithdrawContract)
      .withdraw(BigNumber.from(lock.id))
      .then(() => onWithdraw?.(lock))
      .finally(() => setIsWithdrawing(false))
  }

  return (
    <div className="flex flex-col gap-2">
      <div>
        <Button
          variant={ButtonVariant.Secondary}
          disabled={!canWithdraw || wallet?.isProcessing}
          loading={isWithdrawing}
          onClick={withdraw}
        >
          {t('withdraw')}
        </Button>
      </div>
    </div>
  )
}
