import { AssetProps } from '.'

export default function Refund({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M41.9473 76.71C42.0873 78.02 43.2573 78.98 44.5773 78.84L92.8573 73.84C94.0273 73.72 94.9073 72.78 94.9873 71.65C95.4973 71.25 95.8273 70.65 95.8873 69.97H95.8973C96.1973 69.64 96.3973 69.22 96.4773 68.74L99.4973 48.7C99.6973 47.39 98.7973 46.17 97.4873 45.98L49.4873 38.74C48.1773 38.54 46.9573 39.44 46.7673 40.75L45.9773 45.96H45.5273C44.2073 45.92 43.1073 46.96 43.0773 48.28L42.9473 53.82L41.9773 53.92C40.6673 54.06 39.7073 55.23 39.8473 56.55L41.9373 76.71H41.9473Z"
        fill="#15121A"
      />
      <path
        d="M103.137 107.38C100.987 107.5 97.2873 107.73 93.4573 107.97C94.0273 107.76 94.5173 107.59 94.8573 107.48C96.6973 106.9 97.5473 104.64 98.6273 102.22C99.7173 99.8 100.047 99.07 101.037 96.88C102.027 94.68 97.7973 91.93 95.7973 96.39C93.7973 100.84 92.6173 102.78 89.9873 103.88C89.3473 104.15 88.3173 104.55 87.1273 105.01L86.9173 104.97C87.7773 103.75 88.0173 101.84 88.3673 99.8C88.8173 97.19 88.9673 96.4 89.3773 94.03C89.7873 91.66 85.0173 90.04 84.1773 94.85C83.3373 99.66 82.6873 101.83 80.4073 103.55C80.1473 103.75 80.0873 103.79 79.7073 104.07L79.4573 104.19C79.2673 104 79.0573 103.85 78.8273 103.74C76.7373 102.8 70.5473 100.69 63.3273 98.76C56.1073 96.83 53.2973 100.37 49.7073 99.24C46.1173 98.11 38.5973 95.81 38.5973 95.81C36.8473 95.5 34.3973 101.02 33.1273 108.15C32.8773 109.56 32.6873 110.93 32.5673 112.21C32.5673 112.21 51.0473 115.67 56.3373 117.33C59.0473 118.18 64.9973 118.41 69.8273 117.79C74.6573 117.16 101.147 113.45 105.067 113.06C108.987 112.67 108.987 107.04 103.147 107.37L103.137 107.38Z"
        fill="#15121A"
      />
      <path
        d="M41.0873 77.29C41.6073 77.71 42.2473 77.94 42.9073 77.94C43.0073 77.94 43.1073 77.94 43.2073 77.92L91.4873 72.92C92.2573 72.84 92.9473 72.47 93.4373 71.87C93.7673 71.46 93.9673 70.98 94.0473 70.47C94.5373 70.02 94.8573 69.43 94.9573 68.77C95.2773 68.37 95.4873 67.9 95.5673 67.39L98.5873 47.35C98.6973 46.59 98.5173 45.82 98.0573 45.2C97.5973 44.58 96.9273 44.17 96.1573 44.06L48.1573 36.82C47.3973 36.7 46.6273 36.89 46.0073 37.35C45.3873 37.81 44.9773 38.48 44.8673 39.25L44.1473 44.02H44.1373C44.1373 44.02 44.0873 44.02 44.0673 44.02C42.5073 44.02 41.2173 45.27 41.1773 46.84L41.0573 51.94L40.5273 51.99C39.7573 52.07 39.0673 52.44 38.5773 53.04C38.0873 53.64 37.8673 54.39 37.9473 55.16L40.0373 75.32C40.1173 76.09 40.4873 76.78 41.0873 77.27V77.29ZM45.8573 39.41C45.9373 38.91 46.1973 38.47 46.6073 38.17C47.0173 37.87 47.5173 37.74 48.0173 37.82L96.0173 45.06C97.0473 45.22 97.7573 46.18 97.6073 47.21L95.1273 63.66L95.4973 48.15C95.5173 47.38 95.2273 46.64 94.6973 46.09C94.1673 45.53 93.4473 45.21 92.6773 45.19L45.1673 44.06L45.8673 39.41H45.8573ZM42.1773 46.88C42.1973 45.84 43.0673 45.02 44.1173 45.03L92.6473 46.19C93.1573 46.2 93.6273 46.41 93.9673 46.78C94.3173 47.15 94.4973 47.63 94.4873 48.13L94.0073 68.39C94.0073 68.49 93.9873 68.59 93.9673 68.69L91.9873 49.59C91.8273 48 90.3873 46.85 88.8073 47.01L42.0573 51.85L42.1773 46.88ZM39.3573 53.69C39.6773 53.3 40.1273 53.05 40.6273 53L88.9073 48C88.9773 48 89.0373 47.99 89.1073 47.99C90.0673 47.99 90.8873 48.72 90.9873 49.69L93.0773 69.85C93.1873 70.89 92.4273 71.82 91.3873 71.93L43.1073 76.93C42.6073 76.99 42.1073 76.84 41.7173 76.52C41.3273 76.2 41.0773 75.75 41.0273 75.25L38.9373 55.09C38.8873 54.59 39.0273 54.09 39.3473 53.7L39.3573 53.69ZM65.7173 10C38.0773 10 15.5873 32.49 15.5873 60.14C15.5873 68.21 17.5673 76.18 21.2673 83.29L15.6673 81.34C15.4073 81.25 15.1273 81.39 15.0273 81.65C14.9373 81.91 15.0773 82.2 15.3373 82.29L22.1173 84.65C22.1673 84.67 22.2273 84.68 22.2773 84.68C22.3573 84.68 22.4473 84.66 22.5173 84.62C22.5273 84.62 22.5373 84.6 22.5473 84.6C22.5573 84.6 22.5673 84.58 22.5873 84.57C22.6573 84.52 22.7073 84.45 22.7373 84.37C22.7373 84.37 22.7473 84.36 22.7573 84.35L25.1173 77.57C25.2073 77.31 25.0673 77.02 24.8073 76.93C24.5473 76.84 24.2573 76.98 24.1673 77.24L22.1973 82.92C18.5373 75.93 16.5873 68.09 16.5873 60.16C16.5873 33.04 38.6273 11 65.7173 11C92.8073 11 114.857 33.04 114.857 60.14C114.857 68.32 112.807 76.42 108.927 83.56C108.797 83.8 108.887 84.11 109.127 84.24C109.367 84.37 109.677 84.28 109.807 84.04C113.767 76.75 115.857 68.49 115.857 60.14C115.857 32.49 93.3673 10 65.7173 10ZM43.5773 71.13C45.1773 70.96 46.6173 72.13 46.7873 73.74C46.8173 74 47.0273 74.19 47.2873 74.19C47.3073 74.19 47.3173 74.19 47.3373 74.19L86.6773 70.11C86.8073 70.1 86.9273 70.03 87.0173 69.93C87.0973 69.83 87.1373 69.7 87.1273 69.56C86.9573 67.96 88.1273 66.52 89.7373 66.35C90.0073 66.32 90.2073 66.08 90.1873 65.8L88.9973 54.27C88.9673 54 88.7173 53.8 88.4473 53.82C86.8473 53.99 85.4073 52.82 85.2373 51.21C85.2273 51.08 85.1573 50.96 85.0573 50.87C84.9573 50.79 84.8273 50.75 84.6873 50.76L45.3473 54.84C45.0773 54.87 44.8773 55.11 44.8973 55.39C44.9773 56.17 44.7473 56.93 44.2573 57.53C43.7673 58.14 43.0673 58.51 42.2873 58.59C42.1573 58.6 42.0373 58.67 41.9473 58.77C41.8673 58.87 41.8273 59 41.8373 59.14L43.0273 70.67C43.0573 70.94 43.2973 71.14 43.5773 71.12V71.13ZM45.0273 58.17C45.5773 57.49 45.8873 56.66 45.9073 55.79L84.3173 51.81C84.7273 53.54 86.2573 54.79 88.0373 54.83L89.1373 65.43C87.4073 65.84 86.1573 67.37 86.1173 69.15L47.7073 73.13C47.2973 71.4 45.7673 70.15 43.9873 70.11L42.8873 59.51C43.7273 59.31 44.4773 58.85 45.0373 58.16L45.0273 58.17ZM62.0273 67.38C63.1673 68.3 64.5573 68.8 65.9973 68.8C66.2173 68.8 66.4373 68.79 66.6573 68.77C70.1273 68.41 72.6473 65.3 72.2973 61.83C71.9373 58.36 68.8273 55.84 65.3573 56.19C63.6773 56.36 62.1673 57.18 61.1073 58.49C60.0473 59.8 59.5473 61.45 59.7273 63.13C59.8973 64.81 60.7173 66.32 62.0273 67.39V67.38ZM61.8773 59.12C62.7773 58.02 64.0473 57.33 65.4573 57.18C65.6473 57.16 65.8273 57.15 66.0173 57.15C67.2373 57.15 68.4073 57.56 69.3573 58.34C70.4573 59.24 71.1473 60.51 71.2973 61.92C71.5973 64.84 69.4673 67.46 66.5573 67.76C65.1373 67.91 63.7573 67.49 62.6573 66.6C61.5573 65.7 60.8673 64.43 60.7173 63.02C60.5673 61.61 60.9873 60.22 61.8773 59.12ZM78.0373 65.42C78.6473 65.91 79.3873 66.18 80.1573 66.18C80.2773 66.18 80.3973 66.18 80.5073 66.16C81.4073 66.07 82.2173 65.63 82.7773 64.93C83.3473 64.23 83.6073 63.35 83.5173 62.45C83.3273 60.6 81.6673 59.25 79.8073 59.44C77.9573 59.63 76.6073 61.3 76.7973 63.15C76.8873 64.05 77.3273 64.86 78.0273 65.42H78.0373ZM79.9173 60.43C79.9973 60.43 80.0873 60.42 80.1673 60.42C80.7073 60.42 81.2373 60.61 81.6673 60.95C82.1573 61.35 82.4673 61.92 82.5373 62.55C82.6073 63.18 82.4173 63.8 82.0173 64.3C81.6173 64.79 81.0473 65.1 80.4173 65.17C79.7873 65.24 79.1673 65.05 78.6773 64.65C78.1873 64.25 77.8773 63.68 77.8073 63.05C77.6673 61.75 78.6273 60.57 79.9273 60.44L79.9173 60.43ZM101.687 105.35C100.077 105.44 97.5673 105.59 94.7873 105.76C95.9173 104.83 96.6373 103.23 97.3773 101.55L100.077 95.55C100.627 94.33 99.9573 92.87 98.5473 92.24C97.1873 91.63 95.1573 91.89 93.9173 94.65C91.8773 99.19 90.7873 100.87 88.3673 101.89C87.9073 102.08 87.2373 102.35 86.4673 102.65C86.8773 101.61 87.0973 100.37 87.3173 99.07L88.4473 92.59C88.5873 91.78 88.2773 90.99 87.5873 90.42C86.7673 89.73 85.5873 89.53 84.5973 89.9C83.8973 90.16 82.6673 90.93 82.2673 93.25C81.4173 98.15 80.7773 100.05 78.6673 101.65C78.4573 101.81 78.3673 101.87 78.1073 102.07C77.9573 101.95 77.7873 101.85 77.6173 101.77C75.3373 100.74 68.8873 98.59 62.0373 96.77C57.7473 95.63 54.9573 96.36 52.7073 96.94C51.0973 97.36 49.8273 97.69 48.4373 97.25C44.8473 96.12 37.3273 93.82 37.3273 93.82C37.3273 93.82 37.2873 93.81 37.2673 93.81C36.9873 93.76 36.7073 93.81 36.4273 93.94C36.4873 93.68 36.3373 93.42 36.0773 93.35L31.3373 92.05C31.0673 91.98 30.7973 92.13 30.7273 92.4C30.6573 92.67 30.8073 92.94 31.0773 93.01L35.8173 94.31C35.8173 94.31 35.8473 94.31 35.8673 94.31C33.9673 95.95 32.1773 101.19 31.2173 106.54C30.5973 110.02 30.3473 113.37 30.5073 115.95C30.6073 117.46 30.8373 118.6 31.1973 119.38L24.8073 118.39C24.5373 118.35 24.2773 118.53 24.2373 118.81C24.1973 119.08 24.3773 119.34 24.6573 119.38L32.4973 120.6C32.4973 120.6 32.5473 120.6 32.5773 120.6C32.6273 120.6 32.6773 120.61 32.7273 120.61C34.9873 120.61 36.8273 114.87 37.5073 112.43C43.3673 113.59 51.5673 115.28 54.7773 116.29C56.5173 116.84 59.4473 117.13 62.5673 117.13C64.5473 117.13 66.5973 117.01 68.4773 116.77C70.0773 116.56 74.0673 116.01 78.8173 115.36C88.2173 114.07 101.087 112.3 103.697 112.04C105.917 111.82 106.987 110.11 106.877 108.62C106.737 106.86 105.087 105.16 101.697 105.35H101.687ZM88.7673 102.81C91.6473 101.6 92.8773 99.42 94.8473 95.06C95.7873 92.98 97.1673 92.71 98.1573 93.15C98.9273 93.5 99.5373 94.35 99.1773 95.14L96.4673 101.14C95.5473 103.21 94.7573 105 93.2973 105.46C92.9573 105.57 92.4873 105.73 91.9373 105.93C86.6773 106.26 81.1973 106.61 79.2173 106.74C79.2573 106.63 79.3073 106.52 79.3373 106.4C82.1873 105.34 86.9573 103.55 88.7673 102.8V102.81ZM79.2973 102.42C81.7873 100.54 82.4373 98.11 83.2573 93.41C83.4873 92.06 84.0873 91.15 84.9473 90.83C85.6173 90.58 86.4073 90.72 86.9573 91.18C87.1973 91.38 87.5773 91.8 87.4673 92.41L86.3373 98.9C86.0573 100.58 85.7873 102.16 85.0973 103.15C85.0973 103.16 85.0973 103.17 85.0873 103.18C83.2573 103.88 81.1473 104.67 79.5073 105.28C79.5073 104.37 79.2473 103.48 78.7873 102.8C78.9973 102.65 79.0873 102.58 79.2973 102.42ZM32.6573 119.6C32.3173 119.54 31.6873 118.69 31.5073 115.88C31.4173 114.53 31.4573 112.97 31.5973 111.28C32.4473 111.44 34.2573 111.79 36.5073 112.23C35.0373 117.51 33.3973 119.73 32.6573 119.6ZM103.597 111.04C100.967 111.3 88.0873 113.07 78.6773 114.36C73.9273 115.01 69.9373 115.56 68.3373 115.77C63.7073 116.37 57.7573 116.17 55.0673 115.33C51.6973 114.27 43.1573 112.52 37.2673 111.36C34.6673 110.85 32.5873 110.45 31.6973 110.28C31.8173 109.13 31.9873 107.93 32.2073 106.71C33.5573 99.16 35.9673 94.6 37.0873 94.78C37.5873 94.93 44.6773 97.1 48.1373 98.19C49.7973 98.71 51.2573 98.33 52.9573 97.89C55.2073 97.31 57.7473 96.64 61.7873 97.72C69.2673 99.71 75.3073 101.81 77.2073 102.67C78.0873 103.07 78.6673 104.47 78.4773 105.74C78.3673 106.48 77.9073 107.77 76.0573 108C74.5373 108.19 73.2573 107.93 71.6373 107.6C68.5273 106.97 64.2673 106.11 55.2773 107.9C55.0073 107.95 54.8273 108.22 54.8873 108.49C54.9373 108.76 55.2073 108.94 55.4773 108.88C64.2673 107.12 68.4173 107.96 71.4373 108.58C72.6973 108.83 73.8173 109.06 75.0273 109.06C75.3973 109.06 75.7873 109.04 76.1773 108.99C77.1873 108.86 78.0073 108.43 78.5873 107.79C79.8073 107.71 96.1673 106.65 101.747 106.35C104.457 106.2 105.777 107.42 105.877 108.7C105.957 109.75 105.197 110.89 103.597 111.05V111.04ZM51.8473 69.11C51.9673 69.11 52.0773 69.11 52.1973 69.09C53.0973 69 53.9073 68.56 54.4673 67.86C55.0373 67.16 55.2973 66.28 55.2073 65.38C55.1173 64.48 54.6773 63.67 53.9773 63.11C53.2773 62.54 52.3973 62.28 51.4973 62.37C49.6473 62.56 48.2973 64.23 48.4873 66.08C48.6673 67.82 50.1373 69.11 51.8473 69.11ZM51.6073 63.36C51.6873 63.36 51.7773 63.35 51.8573 63.35C52.3973 63.35 52.9273 63.54 53.3573 63.88C53.8473 64.28 54.1573 64.85 54.2273 65.48C54.2973 66.11 54.1073 66.73 53.7073 67.23C53.3073 67.72 52.7373 68.03 52.1073 68.1C50.7973 68.24 49.6273 67.28 49.4973 65.98C49.3573 64.68 50.3173 63.5 51.6173 63.37L51.6073 63.36ZM28.0073 114.65C28.0073 114.26 27.6873 113.95 27.3073 113.95C26.9273 113.95 26.6073 114.27 26.6073 114.65C26.6073 115.03 26.9273 115.35 27.3073 115.35C27.6873 115.35 28.0073 115.03 28.0073 114.65ZM27.0073 114.65C27.0073 114.49 27.1373 114.35 27.3073 114.35C27.4773 114.35 27.6073 114.48 27.6073 114.65C27.6073 114.82 27.4773 114.95 27.3073 114.95C27.1373 114.95 27.0073 114.82 27.0073 114.65Z"
        fill="url(#paint0_linear_1848_52)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1848_52"
          x1="34.2373"
          y1="124.94"
          x2="95.2273"
          y2="19.29"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
