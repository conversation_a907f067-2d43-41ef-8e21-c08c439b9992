import { useFetcher } from '@remix-run/react'
import clsx from 'clsx'
import { isAddress } from 'ethers/lib/utils'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { adaptAssignWalletData } from '~/api/adapters'
import { SwitchNetworkButton } from '~/components/switch-network/button'
import { BSC_NETWORK } from '~/config'
import { Namespace } from '~/i18n'
import {
  Button,
  ButtonVariant,
  H3,
  Input,
  Modal,
  ModalContent,
  ModalDescription,
  ModalFooter,
  ModalHeader,
  ModalMain,
  ModalTitle,
  Text,
  TextS,
} from '~/tenset-components'
import { chains, useWallet } from '~/tenset-web3'

interface AssignWalletProps {
  assignedWallet: string | null
  launchName: string
  launchId: string
}

export function AssignWallet({
  assignedWallet,
  launchName,
  launchId,
}: AssignWalletProps) {
  const fetcher = useFetcher()

  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION], {
    keyPrefix: 'assign-wallet',
  })

  const { wallet } = useWallet()

  const [isModalOpen, setModalOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [assignedAddress, setAssignedAddress] = useState('')

  const walletAction = async (remove = false) => {
    setIsProcessing(true)
    try {
      if ((!isAddressValid && !remove) || !wallet) return

      const date = new Date().toLocaleString('en-US', {
        timeZone: 'UTC',
      })

      const data = {
        launchName,
        subscriptionAddress: wallet!.account!,
        assignedAddress: remove ? assignedWallet! : assignedAddress,
        date,
      }

      const typedData = remove
        ? adaptAssignWalletData(data, true)
        : adaptAssignWalletData(data)

      const signature = await wallet.signTypedData(
        { name: 'tenset.io' },
        typedData.types,
        typedData.value
      )

      fetcher.submit(
        {
          subscriptionAddress: wallet!.account!,
          assignedAddress: remove ? assignedWallet : assignedAddress,
          message: JSON.stringify(typedData),
          signature,
          date,
          launchId,
        },
        {
          method: remove ? 'delete' : 'post',
        }
      )
    } catch {
      setModalOpen(false)
      setIsProcessing(false)
    }
  }

  useEffect(() => {
    if (fetcher.data && fetcher.state === 'idle') {
      setModalOpen(false)
      setIsProcessing(false)
    }
  }, [fetcher.data, fetcher.state])

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAssignedAddress(event.target.value)
  }

  const isAddressValid = isAddress(assignedAddress)

  const isOnCorrectNetwork = wallet?.chain === BSC_NETWORK

  return (
    <>
      <div
        className={clsx(
          'flex flex-col justify-between gap-4 rounded-[12px] py-6 px-8 md:py-8 md:px-12 lg:w-2/3',
          assignedWallet
            ? 'border-blue-500 border bg-neutral-800'
            : 'bg-blue-900'
        )}
      >
        <div className="flex gap-4">
          <div>
            {assignedWallet ? (
              <div className="mb-4 flex flex-col gap-4">
                <H3 isBold>{t('assigned-wallet-title')}</H3>
                <Text>
                  <b>{t('wallet-address')}:</b> {assignedWallet}
                </Text>
              </div>
            ) : (
              <H3 isBold className="mb-4">
                {t('not-assigned-wallet-title')}
              </H3>
            )}

            <Text>
              {assignedWallet
                ? t('assigned-wallet-info')
                : t('not-assigned-wallet-info')}
            </Text>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant={ButtonVariant.Secondary}
            disabled={isProcessing}
            loading={isProcessing && isModalOpen}
            onClick={() => setModalOpen(true)}
            className={clsx(
              assignedWallet ? 'md:hover:bg-green-500' : 'md:hover:bg-blue-300'
            )}
          >
            {assignedWallet ? t('update-wallet') : t('assign-wallet')}
          </Button>

          {assignedWallet &&
            (isOnCorrectNetwork ? (
              <Button
                variant={ButtonVariant.Ghost}
                disabled={isProcessing}
                loading={isProcessing && !isModalOpen}
                onClick={() => walletAction(true)}
                className="md:hover:bg-red-100 md:hover:text-neutral-900"
              >
                {t('remove-wallet')}
              </Button>
            ) : (
              <SwitchNetworkButton
                id={BSC_NETWORK}
                name={chains[BSC_NETWORK].name}
              />
            ))}
        </div>
      </div>

      <Modal open={isModalOpen} onClose={() => setModalOpen(false)}>
        <ModalContent>
          <ModalHeader>
            <ModalTitle>{t('assign-wallet')}</ModalTitle>

            <ModalDescription>{t('modal-info')}</ModalDescription>
          </ModalHeader>

          <ModalMain>
            <Input
              name="wallet"
              placeholder={t('wallet-address')}
              onChange={handleInputChange}
              value={assignedAddress}
            />
            {assignedAddress && !isAddressValid && (
              <TextS className="text-red-500">{t('invalid-address')}</TextS>
            )}
          </ModalMain>

          <ModalFooter>
            {isOnCorrectNetwork ? (
              <Button
                variant={ButtonVariant.Secondary}
                disabled={isProcessing || !isAddressValid}
                loading={isProcessing}
                onClick={() => walletAction()}
              >
                {t('assign-wallet')}
              </Button>
            ) : (
              <SwitchNetworkButton
                id={BSC_NETWORK}
                name={chains[BSC_NETWORK].name}
              />
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}
