import { marked } from 'marked'
import { baseUrl as markedBaseUrl } from 'marked-base-url'

const MARKED_REPLACEMENTS = [
  {
    match: /<\/{3}/g,
    into: '</',
  },
  {
    match: /<\/\//g,
    into: '<',
  },
]

export const renderMarkdown = (source: string, baseUrl?: string) => {
  let output = ''
  let markedHTML = source

  try {
    for (const r of MARKED_REPLACEMENTS) {
      markedHTML = markedHTML.replace(r.match, r.into)
    }

    if (baseUrl) marked.use(markedBaseUrl(baseUrl))

    output = marked(markedHTML) as string
  } catch (error) {
    console.warn(error)
  }

  return output
}
