import { Trans, useTranslation } from 'react-i18next'

import {
  TelegramLogo,
  TensetLogo,
  TwitterLogo,
} from 'modules/tenset-components/src/assets/icons'
import type { VerifiedMediaScopes } from '~/api/types'
import { Namespace } from '~/i18n'
import type { MediaUrls } from '~/routes/$locale.verification'
import {
  Button,
  ButtonVariant,
  Icon,
  IconName,
  Modal,
  ModalContent,
  ModalDescription,
  ModalFooter,
  ModalHeader,
  ModalMain,
  ModalTitle,
  TextL,
} from '~/tenset-components'

interface VerifiedModalProps {
  isOpen: boolean
  onDismiss: () => void
  verifiedPhrase: string
  mediaButtons: MediaUrls[]
  mediaScopes: VerifiedMediaScopes
}

export function VerifiedModal({
  isOpen,
  onDismiss,
  verifiedPhrase,
  mediaButtons,
  mediaScopes,
}: VerifiedModalProps) {
  const { t } = useTranslation([Namespace.VERIFICATION])

  const mediaNames = mediaButtons.map(
    button => button.type.charAt(0).toUpperCase() + button.type.slice(1)
  )

  const getMedaiIcon = (type: string) => {
    let icon = <TensetLogo />

    switch (type) {
      case 'twitter': {
        icon = <TwitterLogo />
        break
      }
      case 'telegram': {
        icon = <TelegramLogo />
        break
      }
      case 'email': {
        icon = <Icon name={IconName.Mail} />
        break
      }
      case 'linkedin': {
        icon = <Icon name={IconName.Linkedin} />
        break
      }
    }

    return icon
  }

  return (
    <Modal open={isOpen} onClose={onDismiss}>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{t('verified-modal.header')}</ModalTitle>

          <ModalDescription>
            <Trans
              t={t}
              i18nKey={'verified-modal.description'}
              values={{
                value: `${verifiedPhrase}`,
                chanel: mediaNames.join(' & '),
              }}
              components={{ bold: <strong /> }}
            />
          </ModalDescription>
        </ModalHeader>

        <ModalMain>
          {mediaScopes && (
            <div>
              <TextL isBold className="mb-2">
                {t('verified-modal.scope-header')}
              </TextL>
              <ul className="list-disc pl-10">
                {mediaScopes.map(scope => (
                  <li key={scope.name}>
                    <strong>{scope.name}</strong>
                    {scope.description && ` - ${scope.description}`}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </ModalMain>

        <ModalFooter>
          <Button onClick={onDismiss}>{t('common:got-it')}</Button>

          {mediaButtons &&
            mediaButtons.map(button => (
              <Button
                key={button.type}
                to={button.url}
                variant={ButtonVariant.Secondary}
              >
                {getMedaiIcon(button.type)}
                {button.url}
              </Button>
            ))}
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
