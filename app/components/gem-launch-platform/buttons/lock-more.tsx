import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { Button } from '~/tenset-components'

export function GetMoreAllocationsButton() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])
  const localizePathname = useLocalizePathname()

  const GLP_SUBSCRIBE_URL = localizePathname(
    '/gem-launch-platform/panel/subscribe'
  )

  return (
    <div>
      <Button to={GLP_SUBSCRIBE_URL}>{t('lock-more.button')}</Button>
    </div>
  )
}
