import { AssetProps } from './index'

export default function Lamborghini({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? 130}
      height={size ?? 130}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M106.485 65.5941C101.196 65.5941 96.8948 69.9431 96.8948 75.3023C96.8948 80.6616 101.196 85 106.485 85C111.774 85 116.075 80.6509 116.075 75.3023C116.075 69.9538 111.774 65.5941 106.485 65.5941ZM106.485 83.9367C101.774 83.9367 97.9464 80.0555 97.9464 75.3023C97.9464 70.5492 101.774 66.6574 106.485 66.6574C111.196 66.6574 115.023 70.5386 115.023 75.3023C115.023 80.0661 111.196 83.9367 106.485 83.9367ZM127.705 72.5164C127.515 72.2612 127.274 72.1123 127.032 72.0166C127.389 71.1872 127.862 69.6028 127.862 67.0083C127.862 63.5312 125.36 58.4591 124.603 57.0023C126.369 55.5987 127.295 53.9292 127.337 53.8548C127.421 53.6953 127.421 53.5145 127.337 53.355C127.253 53.1955 127.105 53.0892 126.927 53.0679L119.65 52.2279C119.471 52.2066 119.292 52.2811 119.177 52.4193L117.042 55.1095C105.444 53.0892 82.8569 49.1867 76.9999 48.5062C68.7664 47.5492 56.7999 47.9958 51.3846 49.4526C47.6727 50.4521 40.2594 53.3231 33.7294 55.8539C30.4801 57.1086 27.4202 58.2996 25.1278 59.1396C9.16559 62.4891 1.46838 70.7619 1.1424 71.1128C1.01622 71.251 0.963642 71.4637 1.02673 71.6445L2.8564 77.1207L1.59456 78.4711C1.47889 78.5987 1.42632 78.7795 1.46838 78.9496C1.49992 79.1197 1.62611 79.258 1.78384 79.3324C1.8995 79.3749 4.57039 80.4808 6.4947 80.4808H19.4075H19.4285C19.5652 80.4808 19.6914 80.417 19.786 80.3213C19.786 80.3213 19.7861 80.3107 19.7966 80.3C19.8807 80.2043 19.9333 80.0874 19.9333 79.9491C19.9333 79.8747 19.9122 79.8003 19.8807 79.7365C19.2813 78.3329 18.9869 76.8442 18.9869 75.3023C18.9869 69.1031 23.9711 64.0629 30.1016 64.0629C36.232 64.0629 41.2163 69.1031 41.2163 75.3023C41.2163 76.589 40.9954 77.8331 40.5748 79.0347C40.3961 79.1197 40.2699 79.3005 40.2699 79.5132C40.2699 79.7684 40.4486 79.9704 40.6905 80.0236C40.7115 80.0342 40.7325 80.0555 40.7641 80.0661C40.9113 80.1193 41.0585 80.1086 41.1847 80.0448H94.8443C94.981 80.0448 95.1177 79.9917 95.2229 79.896C95.328 79.8003 95.3806 79.662 95.3806 79.5238V75.313C95.3806 69.1137 100.365 64.0735 106.495 64.0735C112.626 64.0735 117.61 69.1137 117.61 75.313C117.61 76.6953 117.368 78.0564 116.874 79.3324C116.853 79.3962 116.842 79.4494 116.842 79.5238C116.842 79.5238 116.842 79.5344 116.842 79.5451C116.842 79.5663 116.842 79.577 116.842 79.5982C116.842 79.6514 116.863 79.6939 116.884 79.7365C116.884 79.7365 116.884 79.7365 116.884 79.7471C116.916 79.8003 116.948 79.8534 116.99 79.896C116.99 79.896 116.99 79.896 117 79.9066C117.053 79.9491 117.105 79.9917 117.168 80.0129C117.231 80.0342 117.295 80.0448 117.358 80.0448C117.358 80.0448 117.358 80.0448 117.368 80.0448C117.389 80.0448 117.41 80.0448 117.421 80.0448C121.806 79.4068 126.085 77.8756 126.127 77.8544C126.233 77.8118 126.327 77.748 126.39 77.6523C126.422 77.5992 127.168 76.4508 127.831 74.4517C128.094 73.6435 128.052 72.9949 127.694 72.527L127.705 72.5164ZM119.829 53.3231L125.97 54.0356C125.539 54.6417 124.792 55.5349 123.782 56.3005C123.141 56.1835 121.122 55.8326 118.241 55.3222L119.829 53.3231ZM34.1289 56.8534C40.6379 54.3333 48.0197 51.4729 51.679 50.484C56.9892 49.0485 68.7769 48.6232 76.9052 49.5695C82.0472 50.165 100.165 53.2593 112.331 55.3754C108.409 55.3116 103.088 55.3222 96.6214 55.5455C80.0914 50.4308 71.6581 50.7286 62.7411 51.0369C61.8788 51.0689 61.0166 51.1008 60.1438 51.122C51.6474 51.3666 43.5296 55.4604 37.6095 58.4591C36.6842 58.9269 35.8219 59.3629 35.0332 59.7457C34.8755 59.7244 34.7073 59.6925 34.5495 59.6713C32.7094 59.3948 31.0585 59.1396 28.314 59.1077C30.0805 58.4378 32.0679 57.6616 34.1289 56.864V56.8534ZM93.3196 55.6625C91.9842 55.7156 90.6067 55.7901 89.1766 55.8645C84.4762 56.1197 79.8495 56.5344 75.391 57.0235L74.4236 52.1216C79.5235 52.4618 85.4331 53.4082 93.3196 55.6625ZM74.35 57.1405C70.1544 57.6084 66.106 58.1401 62.2994 58.6292C54.3183 59.6713 47.4413 60.5645 42.9092 60.4262C40.2909 60.3412 38.4297 60.1817 36.905 59.9903C37.2835 59.7989 37.6726 59.6075 38.0827 59.3948C43.9187 56.4493 51.9103 52.4087 60.1754 52.1747C61.0481 52.1535 61.9209 52.1216 62.7832 52.0897C66.2111 51.9727 69.576 51.8557 73.351 52.0578L74.3605 57.1405H74.35ZM126.842 74.1008C126.359 75.5682 125.822 76.5465 125.612 76.8974C124.845 77.1632 121.606 78.2478 118.136 78.8326C118.483 77.6842 118.662 76.4933 118.662 75.2811C118.662 68.497 113.204 62.9782 106.495 62.9782C99.7865 62.9782 94.3291 68.497 94.3291 75.2811V78.9602H41.7105C41.8051 78.6519 41.8892 78.3435 41.9628 78.0245C54.1921 77.1526 83.5299 74.2603 83.8348 74.2284C83.9715 74.2177 84.1082 74.1433 84.1923 74.0263C84.2239 73.9838 87.778 69.454 91.7949 62.9251C94.1924 62.6805 96.4847 62.4147 98.5037 62.1169C98.7876 62.0744 98.9874 61.8086 98.9453 61.5108C98.9032 61.2237 98.6299 61.0111 98.3459 61.0642C87.1787 62.7124 67.7674 63.4355 67.5676 63.4461C67.2732 63.4461 67.0524 63.7013 67.0629 63.9991C67.0629 64.2862 67.3047 64.5095 67.5887 64.5095C67.5887 64.5095 67.5992 64.5095 67.6097 64.5095C67.7674 64.5095 79.86 64.0522 90.47 63.0527C87.1051 68.4544 84.1608 72.3356 83.4983 73.1863C80.7643 73.4521 53.9398 76.0892 42.1521 76.9399C42.2257 76.3869 42.2783 75.834 42.2783 75.2811C42.2783 68.497 36.8209 62.9782 30.1121 62.9782C23.4033 62.9782 17.9459 68.497 17.9459 75.2811C17.9459 76.6953 18.1982 78.0777 18.6609 79.3962H6.50521C6.358 79.3962 6.18975 79.3962 6.03202 79.3749L7.50417 77.4716C7.63035 77.3121 7.65138 77.0994 7.56726 76.908L5.16977 71.8784C5.04358 71.6126 4.72812 71.5062 4.46524 71.6338C4.20236 71.7614 4.0972 72.0804 4.22339 72.3463L6.47366 77.0781L4.85431 79.1729C4.12875 79.0028 3.42423 78.7795 2.91949 78.5987L3.85535 77.5992C3.99205 77.4609 4.03411 77.2483 3.97102 77.0675L2.14136 71.6019C3.49783 70.2515 11.1845 63.1271 25.3171 60.171C29.9333 60.0222 32.0048 60.3305 34.4023 60.6921C36.4108 61.0004 38.6926 61.3407 42.8777 61.4683C47.4834 61.6065 54.413 60.7133 62.4361 59.6606C70.554 58.6079 79.7549 57.4064 89.2502 56.8959C91.837 56.7577 94.287 56.6514 96.5899 56.5769C96.6214 56.5769 96.6425 56.5769 96.674 56.5769C110.765 56.0984 119.377 56.6301 119.85 56.6514C121.743 56.981 123.078 57.2149 123.604 57.3107C124.193 58.4484 126.832 63.6588 126.832 66.987C126.832 70.6237 125.843 72.0592 125.833 72.0698C125.707 72.2293 125.696 72.4526 125.791 72.6334C125.886 72.8141 126.096 72.9311 126.275 72.9205C126.39 72.9205 126.748 72.9524 126.884 73.1438C127.011 73.3139 127 73.6435 126.853 74.0795L126.842 74.1008ZM30.1121 65.5941C24.8229 65.5941 20.5221 69.9431 20.5221 75.3023C20.5221 80.6616 24.8229 85 30.1121 85C35.4013 85 39.702 80.6509 39.702 75.3023C39.702 69.9538 35.4013 65.5941 30.1121 65.5941ZM30.1121 83.9367C25.4012 83.9367 21.5737 80.0555 21.5737 75.3023C21.5737 70.5492 25.4012 66.6574 30.1121 66.6574C34.8229 66.6574 38.6505 70.5386 38.6505 75.3023C38.6505 80.0661 34.8229 83.9367 30.1121 83.9367Z"
        fill="url(#paint0_linear_2185_37)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_37"
          x1="46.1479"
          y1="105.235"
          x2="89.4202"
          y2="31.1137"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
