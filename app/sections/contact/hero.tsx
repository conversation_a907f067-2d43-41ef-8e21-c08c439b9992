import { useTranslation } from 'react-i18next'

import { Card, H1, TextL } from '~/tenset-components'
import { Namespace } from '~/i18n'

export function HeroContact(_props: unknown) {
  const { t } = useTranslation([Namespace.CONTACT_PAGE])

  return (
    <div className="flex flex-col items-center md:text-center">
      <Card isLarge title={<H1 isBold>{t('hero.title')}</H1>}>
        <TextL>{t('hero.description')}</TextL>
      </Card>
    </div>
  )
}
