{"name": "tenset-web3", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint --ext \".js,.ts,.md,.jsx,.tsx,.mdx\" --ignore-path .gitignore .", "lint:fix": "yarn lint --fix", "prepare": "husky", "update-deps": "ncu '/^(?!ethers).*$/' -u && yarn"}, "devDependencies": {"@commitlint/cli": "19.2.1", "@commitlint/config-conventional": "19.1.0", "@types/node": "20.11.29", "@types/react": "18.2.67", "@types/react-dom": "18.2.22", "@typescript-eslint/eslint-plugin": "7.3.1", "@typescript-eslint/parser": "7.3.1", "@walletconnect/ethereum-provider": "2.11.3", "@walletconnect/modal": "2.6.2", "@walletconnect/types": "2.11.3", "buffer": "6.0.3", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-alias": "1.1.2", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-import-helpers": "1.3.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-mdx": "3.1.5", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-unicorn": "51.0.1", "ethers": "5.7.2", "ethers-multicall-provider": "3.1.2", "husky": "9.0.11", "lint-staged": "15.2.2", "npm-check-updates": "16.14.17", "prettier": "3.2.5", "react": "18.2.0", "react-dom": "18.2.0", "rxjs": "7.8.1", "typescript": "5.4.2"}, "engines": {"node": ">=20.9.0 <21"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0"}}