import type { AssetProps } from '.'

export default function HigherReturn({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '48'}
      height={size ?? '36'}
      viewBox="0 0 48 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g>
        <path
          d="M33.32 17.5451L42.67 8.19507"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M23.01 13.5651L29.37 17.5451"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5.32 27.8051L19.07 13.5651"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M19.07 14.3851C19.07 15.4731 19.952 16.3551 21.04 16.3551C22.128 16.3551 23.01 15.4731 23.01 14.3851V8.7951C23.01 7.7071 22.128 6.8251 21.04 6.8251C19.952 6.8251 19.07 7.7071 19.07 8.7951V14.3851Z"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.38 32.575C1.38 33.663 2.262 34.545 3.35 34.545C4.438 34.545 5.32 33.663 5.32 32.575L5.32 26.985C5.32 25.897 4.438 25.015 3.35 25.015C2.262 25.015 1.38 25.897 1.38 26.985L1.38 32.575Z"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M29.38 22.315C29.38 23.403 30.262 24.285 31.35 24.285C32.438 24.285 33.32 23.403 33.32 22.315V16.725C33.32 15.637 32.438 14.755 31.35 14.755C30.262 14.755 29.38 15.637 29.38 16.725V22.315Z"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M42.68 9.01511C42.68 10.1031 43.562 10.9851 44.65 10.9851C45.738 10.9851 46.62 10.1031 46.62 9.01511V3.42511C46.62 2.33711 45.738 1.45511 44.65 1.45511C43.562 1.45511 42.68 2.33711 42.68 3.42511V9.01511Z"
          stroke={color ?? '#F0F0F0'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}
