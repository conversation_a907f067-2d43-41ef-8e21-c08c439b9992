import { config, infinityNftWithdrawalFeeAbi } from '../..'
import { Utils } from '../../types'
import { Contract } from '../base'

export class InfinityNftWithdrawalFeeContract extends Contract {
  constructor(network: Utils.Network) {
    super(
      config.infinity.nftWithdrawalFee[network]!,
      network,
      infinityNftWithdrawalFeeAbi
    )
  }

  public async pay(id: string, overrides: Utils.ContractMethodOverrides = {}) {
    this.wallet!.asBusy()

    try {
      const tx = await this.adapter!.pay(id, overrides)

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
