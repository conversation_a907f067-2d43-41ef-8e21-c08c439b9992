import type { <PERSON>tch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@remix-run/react'
import { <PERSON>wait, useF<PERSON>cher, useOutletContext } from '@remix-run/react'
import { BigNumber } from 'ethers'
import { formatEther, parseEther } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import { Suspense, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'

import { type GlpLock } from '~/api/types'
import {
  ExtendLockButton,
  ExtendLockModal,
  WithdrawLockButton,
} from '~/components/gem-launch-platform'
import { isDevelopment } from '~/environment'
import { tiersPrivileges } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import type { GlpOutletData } from '~/routes/$locale.gem-launch-platform.panel'
import {
  But<PERSON>,
  ButtonVariant,
  CryptoCurrencyFormatter,
  DateFormatter,
  H3,
  Icon,
  IconName,
  Table,
  Tag,
  TagColor,
  TextNumeric,
  TextS,
  TextSNumeric,
} from '~/tenset-components'
import { useWallet } from '~/tenset-web3'

function getLockStatus(
  lock: GlpLock,
  t: TFunction,
  blockTimestamp: number
): string {
  const isLocked = lock.timestamps.unlock > blockTimestamp
  const isWithdrawn = lock.timestamps.withdraw && lock.timestamps.withdraw > 0

  if (isWithdrawn) return t('tokens-table.statuses.withdrawn')
  if (!isLocked) return t('tokens-table.statuses.released')
  return t('tokens-table.statuses.locked')
}

function getLockPrivileges(lock: GlpLock, t: TFunction) {
  const { amount, membershipAttributes } = lock
  const { tierId, tokensForExtension } = membershipAttributes || {}

  const tensetTiers = tiersPrivileges(t).filter(
    ({ entry }) => typeof entry === 'number'
  )

  if (tierId) {
    return {
      tier: tierId,
      privileges: tensetTiers[tierId - 1],
    }
  }

  const lockedAmount =
    Number.parseFloat(amount) -
    Number.parseFloat(formatEther(tokensForExtension || 0))

  let index = tensetTiers.findIndex(
    ({ entry }) => (entry as number) >= lockedAmount
  )

  index = index === -1 ? tensetTiers.length - 1 : index

  return {
    tier: index + 1,
    privileges: tensetTiers[index],
  }
}

type AdaptedLock = {
  level: JSX.Element
  amount: JSX.Element
  poolWeight: JSX.Element
  claimBack: JSX.Element | string
  tradableVesting: JSX.Element | string
  status: string
  releaseDate: JSX.Element
  actions: JSX.Element
}

function adaptRpcLock(
  lock: GlpLock,
  t: TFunction,
  removeLockFromTable: (lockId: number) => void,
  refreshTensetBalance: (amount: BigNumber) => void,
  setCurrentlyExtendedLock: (lock?: GlpLock) => void,
  blockTimestamp: number,
  isProcessing: boolean
): AdaptedLock {
  const { tier, privileges } = getLockPrivileges(lock, t)
  const { membershipAttributes } = lock || {}
  const { bonusNumerator, bonusDenominator } = membershipAttributes || {}

  const isReferralAdded = Boolean(
    bonusNumerator && bonusDenominator && bonusNumerator > 0
  )

  const canWithdraw = lock.timestamps.unlock <= blockTimestamp
  const canExtendAndUpgrade = lock.timestamps.unlock > blockTimestamp

  return {
    level: (
      <div className="flex flex-col gap-1 items-center">
        {t('gem-launch-platform:tiers-privileges.tier', { tier })}

        {isDevelopment && <Tag color={TagColor.NEUTRAL}>{lock.id}</Tag>}
      </div>
    ),
    amount: (
      <CryptoCurrencyFormatter
        value={Number.parseFloat(lock.amount)}
        currency="10set"
      />
    ),
    poolWeight: (
      <div className="flex gap-2 items-center">
        {privileges.poolWeight}x
        {isReferralAdded && (
          <Tag color={TagColor.GREEN}>
            {t('referral-program.boost', {
              bonus: (bonusNumerator! / bonusDenominator!) * 100,
            })}
          </Tag>
        )}
      </div>
    ),
    claimBack: privileges.claimBack ? (
      <Icon name={IconName.Success} color="#ffffff" />
    ) : (
      ''
    ),
    tradableVesting: privileges.tradableVesting ? (
      <Icon name={IconName.Success} color="#ffffff" />
    ) : (
      ''
    ),
    status: getLockStatus(lock, t, blockTimestamp),
    releaseDate: (
      <div className="flex gap-2">
        <DateFormatter date={new Date(lock.timestamps.unlock)} />
      </div>
    ),
    actions: (
      <div className="flex flex-wrap items-center justify-end gap-2">
        {canWithdraw && (
          <WithdrawLockButton
            lock={lock}
            onWithdraw={() => {
              removeLockFromTable(lock.id)
              refreshTensetBalance(parseEther(lock.amount))

              toast.success(t('withdrawn-successfully'))
            }}
          />
        )}

        {canExtendAndUpgrade && tier < (isDevelopment ? 4 : 3) && (
          <>
            <Button
              to={isProcessing ? undefined : `./upgrade?id=${lock.id}`}
              disabled={isProcessing}
              variant={ButtonVariant.Secondary}
            >
              {t('upgrade.button')}
            </Button>

            {isDevelopment && (
              <ExtendLockButton
                lock={lock}
                setLock={setCurrentlyExtendedLock}
              />
            )}
          </>
        )}
      </div>
    ),
  }
}

function adaptOldLock(
  lock: GlpLock,
  t: TFunction,
  removeLockFromTable: (lockId: number) => void,
  refreshTensetBalance: (amount: BigNumber) => void,
  isReferralAdded: boolean,
  blockTimestamp: number,
  fetcher: FetcherWithComponents<unknown>,
  walletAddress: string
): AdaptedLock {
  const isLocked = lock.timestamps.unlock > blockTimestamp
  const isWithdrawn = Boolean(
    lock.timestamps.withdraw && lock.timestamps.withdraw > 0
  )

  const hasWithdrawProofs = lock.proofs!.withdraw.length > 0

  const canWithdraw = !isLocked && !isWithdrawn && hasWithdrawProofs

  const { tier, privileges } = getLockPrivileges(lock, t)

  const lockAmount = isReferralAdded
    ? (Number(lock.amount) - Number(lock.amount) * 0.05).toString()
    : lock.amount

  return {
    level: (
      <div className="flex flex-col gap-1 items-center">
        {t('gem-launch-platform:tiers-privileges.tier', { tier })}

        {isDevelopment && (
          <Tag color={TagColor.NEUTRAL} className="text-xs">
            OLD {lock.id}
          </Tag>
        )}
      </div>
    ),
    amount: (
      <>
        <CryptoCurrencyFormatter
          value={Number.parseFloat(lockAmount)}
          currency="10set"
        />
      </>
    ),
    poolWeight: (
      <div className="flex gap-2 items-center">
        {privileges.poolWeight}x
        {isReferralAdded && (
          <Tag color={TagColor.GREEN}>
            {t('referral-program.boost', {
              bonus: 25,
            })}
          </Tag>
        )}
      </div>
    ),
    claimBack: privileges.claimBack ? (
      <Icon name={IconName.Success} color="#ffffff" />
    ) : (
      ''
    ),
    tradableVesting: privileges.tradableVesting ? (
      <Icon name={IconName.Success} color="#ffffff" />
    ) : (
      ''
    ),
    status: getLockStatus(lock, t, blockTimestamp),
    releaseDate: (
      <div className="flex gap-2">
        <DateFormatter date={new Date(lock.timestamps.unlock)} />
      </div>
    ),
    actions: (
      <div className="flex flex-wrap items-center justify-end gap-2">
        <div className="flex gap-2">
          {canWithdraw && (
            <WithdrawLockButton
              lock={lock}
              onWithdraw={() => {
                removeLockFromTable(lock.id)
                refreshTensetBalance(parseEther(lockAmount))

                fetcher.submit(
                  {},
                  {
                    method: 'post',
                    action: `/api/gem-launch-platform/reset-cache?walletAddress=${walletAddress}`,
                  }
                )

                toast.success(t('withdrawn-successfully'))
              }}
            />
          )}
        </div>
      </div>
    ),
  }
}

interface TglpTokensTableProps {
  membershipBalance: number
  rpcLocks: Promise<GlpLock[]>
  oldReleasedLocks: GlpLock[]
  referralLocks: number[]
  isAddressApprovedForReferral: boolean
  removeLockFromTable: (lockId: number) => void
  refreshTensetBalance: (amount: BigNumber) => void
  extendAllowance: BigNumber
  setExtendAllowance: (allowance: BigNumber) => void
  tensetBalance: BigNumber
}

export function TglpTokensTable({
  membershipBalance,
  rpcLocks,
  oldReleasedLocks,
  referralLocks,
  removeLockFromTable,
  refreshTensetBalance,
  extendAllowance,
  tensetBalance,
  setExtendAllowance,
}: TglpTokensTableProps) {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
    Namespace.GEM_LAUNCH_PLATFORM,
  ])

  const { wallet } = useWallet()

  const fetcher = useFetcher({ key: 'root' })

  const { blockTimestamp } = useOutletContext<GlpOutletData>()

  const [_rpcLocks, setRpcLocks] = useState(rpcLocks)

  useEffect(() => {
    setRpcLocks(rpcLocks)
  }, [rpcLocks])

  function updateExtendedLock(
    lock: GlpLock,
    tokensAmount: number,
    numberOfYears: number
  ) {
    _rpcLocks.then(rpcLocks => {
      const updatedLocks = rpcLocks.map(_lock => {
        const { timestamps, amount, membershipAttributes } = _lock
        const { tokensForExtension } = membershipAttributes!

        return _lock.id === lock.id
          ? {
              ..._lock,
              amount: `${Number.parseFloat(amount) + tokensAmount}`,
              timestamps: {
                ...timestamps,
                unlock:
                  timestamps.unlock + numberOfYears * 365 * 24 * 60 * 60 * 1000,
              },
              membershipAttributes: {
                ...membershipAttributes!,
                tokensForExtension: BigNumber.from(tokensForExtension).add(
                  parseEther(tokensAmount.toString())
                ),
              },
            }
          : _lock
      })

      setRpcLocks(Promise.resolve(updatedLocks))
    })
  }

  const headers = {
    level: t('tokens-table.headers.level'),
    amount: t('tokens-table.headers.amount'),
    poolWeight: t('gem-launch-platform:tiers-privileges.pool-weight'),
    claimBack: t('gem-launch-platform:tiers-privileges.claim-back'),
    tradableVesting: t('gem-launch-platform:tiers-privileges.tradable-vesting'),
    status: t('tokens-table.headers.status'),
    releaseDate: t('tokens-table.headers.release-date'),
    actions: '',
  }

  const oldReleasedLocksAdapted = sortLocks(
    oldReleasedLocks.map(token => {
      const isReferralAdded = referralLocks.includes(token.id)

      return adaptOldLock(
        token,
        t,
        removeLockFromTable,
        refreshTensetBalance,
        isReferralAdded,
        blockTimestamp,
        fetcher,
        wallet!.account!
      )
    })
  )

  const skeletonRpcLocks = Array.from({
    length: membershipBalance,
  }).map((_, index) =>
    Object.fromEntries(
      Object.keys(headers).map(key => [
        key,
        <div
          key={index}
          className="h-4 w-20 animate-pulse rounded-md bg-neutral-600"
        />,
      ])
    )
  )

  const [currentlyExtendedLock, setCurrentlyExtendedLock] = useState<
    GlpLock | undefined
  >()

  return (
    <>
      <Suspense
        fallback={
          <section className="flex flex-col gap-6">
            <H3 isBold>{t('tokens-table.title')}</H3>

            <Table
              headers={headers}
              items={[...oldReleasedLocksAdapted, ...skeletonRpcLocks]}
              typography={{
                header: TextS,
                mainRow: TextNumeric,
                row: TextSNumeric,
              }}
            />
          </section>
        }
      >
        <Await resolve={_rpcLocks}>
          {_rpcLocks => {
            const rpcLocksAdapted = _rpcLocks.map(lock =>
              adaptRpcLock(
                lock,
                t,
                removeLockFromTable,
                refreshTensetBalance,
                setCurrentlyExtendedLock,
                blockTimestamp,
                Boolean(wallet?.isProcessing)
              )
            )

            const locks = sortLocks([
              ...rpcLocksAdapted,
              ...oldReleasedLocksAdapted,
            ])

            return (
              locks.length > 0 && (
                <section className="flex flex-col gap-6">
                  <H3 isBold>{t('tokens-table.title')}</H3>

                  <Table
                    headers={headers}
                    items={locks}
                    typography={{
                      header: TextS,
                      mainRow: TextNumeric,
                      row: TextSNumeric,
                    }}
                  />
                </section>
              )
            )
          }}
        </Await>
      </Suspense>

      {isDevelopment && (
        <ExtendLockModal
          lock={currentlyExtendedLock}
          setLock={setCurrentlyExtendedLock}
          extendAllowance={extendAllowance}
          onExtend={updateExtendedLock}
          setExtendAllowance={setExtendAllowance}
          tensetBalance={tensetBalance}
        />
      )}
    </>
  )
}

/*
 * Sort by the release date
 */
function sortLocks(locks: AdaptedLock[]): AdaptedLock[] {
  return locks.sort((a, b) => {
    const releaseDateA = new Date(a.releaseDate.props.children.props.date)
    const releaseDateB = new Date(b.releaseDate.props.children.props.date)

    return releaseDateA.getTime() - releaseDateB.getTime()
  })
}
