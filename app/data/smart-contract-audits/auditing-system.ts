import type { TFunction } from 'i18next'

import { IconName } from '~/tenset-components'
import type { CardWithTile } from '~/components/cards-with-title'

export const auditingSystemCards = (t: TFunction): CardWithTile[] => [
  {
    title: t('smart-contract-audits.auditing-system.processing.title'),
    description: t(
      'smart-contract-audits.auditing-system.processing.description'
    ),
    image: IconName.ScAuditsProcessing,
  },
  {
    title: t('smart-contract-audits.auditing-system.ai.title'),
    description: t('smart-contract-audits.auditing-system.ai.description'),
    image: IconName.ScAuditAI,
  },
  {
    title: t('smart-contract-audits.auditing-system.model-training.title'),
    description: t(
      'smart-contract-audits.auditing-system.model-training.description'
    ),
    image: IconName.ScAuditModel,
  },
]
