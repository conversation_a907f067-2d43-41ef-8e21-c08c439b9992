import { useTranslation } from 'react-i18next'

import { NftCard } from '~/components/blocks/nft-card'
import { nftCards } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import {
  ButtonVariant,
  Icon,
  IconName,
  Modal,
  ModalContent,
  ModalHeader,
  ModalMain,
  ModalTitle,
  ModalTrigger,
} from '~/tenset-components'

export function PurchaseNftModal({
  showTier = null,
}: {
  showTier?: number | null
}) {
  const { t } = useTranslation(Namespace.GEM_LAUNCH_PLATFORM)

  const filteredNftCards =
    showTier === null
      ? nftCards
      : nftCards.filter(card => card.tier === showTier)

  return (
    <>
      <Modal>
        <ModalTrigger variant={ButtonVariant.Secondary}>
          {t('access-launchpad.purchase-nft.action')}

          <Icon name={IconName.ChevronRight16} />
        </ModalTrigger>

        <ModalContent>
          <ModalHeader>
            <ModalTitle>
              {t('access-launchpad.purchase-nft.modal-title')}
            </ModalTitle>
          </ModalHeader>

          <ModalMain className="flex flex-col items-center gap-4 pb-4 md:flex-row md:items-stretch md:flex-wrap">
            {filteredNftCards.map(
              ({
                title,
                image,
                linkOpenSea,
                linkOkx,
                linkBlur,
                utilities,
                tier,
              }) => (
                <NftCard
                  title={title}
                  image={image}
                  linkOpenSea={linkOpenSea}
                  linkOkx={linkOkx}
                  linkBlur={linkBlur}
                  utilities={utilities}
                  key={title}
                  className="bg-neutral-900"
                  tier={tier}
                />
              )
            )}
          </ModalMain>
        </ModalContent>
      </Modal>
    </>
  )
}
