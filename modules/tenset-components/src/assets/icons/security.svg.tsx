import { AssetProps } from './index'

export default function Security({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2040_92)">
        <path
          d="M80.6996 54.2286V43.1716V33.1755V23.1793C80.6996 19.8337 73.8557 17.1273 65.4093 17.1273C56.963 17.1273 50.119 19.8337 50.119 23.1793V32.7131C47.3759 31.6931 43.696 31.0539 39.6357 31.0539C31.1894 31.0539 24.3454 33.7603 24.3454 37.106V87.0595C24.3454 90.4052 31.1894 93.1116 39.6357 93.1116C41.8084 93.1116 43.8861 92.9348 45.76 92.6084C48.4216 105.284 58.5382 115.198 71.3299 117.483C73.1631 117.823 75.0642 118 77.0061 118C78.3912 118 79.7355 117.905 81.0527 117.728C96.7776 115.742 108.945 102.292 108.945 85.9987C108.945 69.7057 96.5875 56.0646 80.6996 54.2286ZM71.2077 99.5853L60.7244 89.0859L64.6896 85.1147L71.2077 91.6428L90.3953 72.4257L94.3605 76.3969L71.2077 99.5853Z"
          fill="#15121A"
        />
        <path
          d="M102.902 16.9913C103.282 16.9913 103.581 16.6921 103.581 16.3113V12.68C103.581 12.2992 103.282 12 102.902 12C102.522 12 102.223 12.2992 102.223 12.68V16.3113C102.223 16.6921 102.522 16.9913 102.902 16.9913ZM62.6527 82.109C62.3947 81.8506 61.9466 81.8506 61.6886 82.109L57.7234 86.0803C57.4518 86.3523 57.4518 86.7739 57.7234 87.0459L68.2066 97.5453C68.3424 97.6813 68.519 97.7493 68.6819 97.7493C68.8449 97.7493 69.035 97.6813 69.1572 97.5453L92.31 74.3569C92.5815 74.0849 92.5815 73.6633 92.31 73.3913L88.3448 69.4201C88.0868 69.1617 87.6387 69.1617 87.3807 69.4201L68.6683 88.1611L62.6255 82.109H62.6527ZM87.8831 70.8617L90.8841 73.8673L68.6819 96.1037L59.1628 86.5699L62.1638 83.5643L68.2066 89.6164C68.3289 89.7388 68.5054 89.8204 68.6819 89.8204C68.8584 89.8204 69.035 89.7524 69.1572 89.6164L87.8695 70.8753L87.8831 70.8617ZM102.902 30.2243C103.282 30.2243 103.581 29.9251 103.581 29.5443V25.913C103.581 25.5322 103.282 25.233 102.902 25.233C102.522 25.233 102.223 25.5322 102.223 25.913V29.5443C102.223 29.9251 102.522 30.2243 102.902 30.2243ZM69.7411 25.437C69.7411 25.437 69.8497 25.437 69.9041 25.4234C70.7731 25.2194 71.6286 24.9474 72.4706 24.6482C72.8236 24.5258 73.0001 24.1314 72.8779 23.7778C72.7421 23.4242 72.3483 23.2474 72.0089 23.3698C71.2213 23.6554 70.4201 23.9002 69.6053 24.1042C69.2387 24.1858 69.0214 24.553 69.1029 24.9202C69.1844 25.233 69.4559 25.437 69.7683 25.437H69.7411ZM111.321 20.4321H107.695C107.315 20.4321 107.016 20.7313 107.016 21.1121C107.016 21.4929 107.315 21.7921 107.695 21.7921H111.321C111.701 21.7921 112 21.4929 112 21.1121C112 20.7313 111.701 20.4321 111.321 20.4321ZM77.7122 50.9646V19.6025C77.7122 15.8217 70.6917 12.8704 61.7429 12.8704C52.7941 12.8704 45.7736 15.8217 45.7736 19.6025V28.1842C43.0849 27.3138 39.7036 26.797 35.9693 26.797C27.0205 26.797 20 29.7483 20 33.5291V83.4827C20 87.2635 27.0205 90.2148 35.9693 90.2148C38.2235 90.2148 40.3962 90.0108 42.4466 89.6436C44.9452 102.727 55.2112 113.226 68.6819 115.634C70.5151 115.974 72.4706 116.15 74.4803 116.15C75.879 116.15 77.2776 116.055 78.622 115.878C94.8629 113.825 107.111 99.8981 107.111 83.4691C107.111 67.04 94.1975 52.583 77.7122 50.9646ZM47.1315 22.3498C49.5894 24.7162 55.1297 26.3346 61.7429 26.3346C62.2589 26.3346 62.7749 26.3346 63.2773 26.3074C63.6576 26.2938 63.9427 25.981 63.9291 25.6002C63.9156 25.2194 63.6304 24.9474 63.223 24.9474C62.7342 24.961 62.2453 24.9746 61.7429 24.9746C53.1336 24.9746 47.1315 22.1458 47.1315 19.6025C47.1315 17.0593 53.1336 14.2304 61.7429 14.2304C70.3522 14.2304 76.3542 17.0593 76.3542 19.6025V29.5987C76.3542 32.1419 70.3522 34.9707 61.7293 34.9707C58.4295 35.0251 55.1026 34.5219 51.925 33.4883C51.8979 31.5843 50.0782 29.8843 47.1315 28.6739V22.3498ZM51.9386 53.4806V44.8989C54.9532 45.8237 58.0765 46.3133 61.2269 46.3133C61.4034 46.3133 61.5799 46.3133 61.7429 46.3133C68.356 46.3133 73.8964 44.6949 76.3542 42.3285V49.5774C76.3542 49.9854 76.1913 50.407 75.879 50.8422C75.4173 50.8286 74.9556 50.8014 74.4939 50.8014C68.8041 50.8014 63.4539 52.2702 58.7962 54.8406C56.4605 54.6502 54.1656 54.1878 51.9522 53.467L51.9386 53.4806ZM56.8543 56.0103C55.089 57.1527 53.4459 58.4447 51.9386 59.8999V54.9086C53.541 55.3983 55.1841 55.7791 56.8543 56.0103ZM61.7293 44.9533C58.3752 45.0077 55.1026 44.5045 51.9386 43.4845V34.9163C54.9532 35.8412 58.0629 36.3308 61.2133 36.3308C61.3898 36.3308 61.5663 36.3308 61.7429 36.3308C68.356 36.3308 73.8964 34.7123 76.3542 32.3459V39.5812C76.3542 42.1245 70.3522 44.9533 61.7293 44.9533ZM21.3579 36.2764C23.8158 38.6428 29.3562 40.2612 35.9693 40.2612C36.4853 40.2612 37.0013 40.2612 37.5038 40.234C37.884 40.2204 38.1692 39.9076 38.1556 39.5268C38.142 39.146 37.8297 38.8468 37.4494 38.874C36.9606 38.8876 36.4717 38.9012 35.9693 38.9012C27.36 38.9012 21.3579 36.0724 21.3579 33.5291C21.3579 30.9859 27.36 28.157 35.9693 28.157C44.5786 28.157 50.5807 30.9859 50.5807 33.5291V33.9235C50.5807 33.9235 50.5807 33.9779 50.5807 34.0187V43.5253C50.5807 46.0685 44.5786 48.8974 35.9693 48.8974C27.36 48.8974 21.3579 46.0685 21.3579 43.5253V36.29V36.2764ZM21.3579 46.2725C23.8158 48.639 29.3562 50.2574 35.9693 50.2574C42.5824 50.2574 48.1228 48.639 50.5807 46.2725V53.5078C50.5807 56.0511 44.5786 58.8799 35.9693 58.8799C27.36 58.8799 21.3579 56.0511 21.3579 53.5078V46.2725ZM21.3579 56.2687C23.8158 58.6351 29.3562 60.2535 35.9693 60.2535C42.5824 60.2535 48.1228 58.6351 50.5807 56.2687V61.2735C48.8832 63.096 47.4031 65.1088 46.1674 67.2848C43.3972 68.3048 39.7987 68.8761 35.9829 68.8761C27.3736 68.8761 21.3715 66.0472 21.3715 63.504V56.2687H21.3579ZM35.9693 88.8548C27.36 88.8548 21.3579 86.0259 21.3579 83.4827V76.2338C23.8158 78.6002 29.3562 80.2186 35.9693 80.2186C38.0877 80.2186 40.1246 80.0418 42.0664 79.7154C41.9306 80.953 41.8491 82.2043 41.8491 83.4827C41.8491 85.1283 41.9985 86.7331 42.2294 88.3107C40.2468 88.6779 38.1556 88.8684 35.9693 88.8684V88.8548ZM42.2565 78.301C40.2604 78.6682 38.1556 78.8586 35.9693 78.8586C27.36 78.8586 21.3579 76.0298 21.3579 73.4865V66.2512C23.8158 68.6176 29.3562 70.2361 35.9693 70.2361C39.337 70.2361 42.5417 69.8009 45.2304 69.0121C43.8046 71.8953 42.7861 75.0097 42.2565 78.301ZM105.74 83.4691C105.74 99.2181 94.0074 112.56 78.4319 114.532C75.2951 114.954 71.9002 114.858 68.9128 114.301C55.768 111.948 45.8007 101.571 43.6416 88.7323C43.3565 87.0187 43.1935 85.2779 43.1935 83.4963C43.1935 66.2376 57.221 52.1886 74.4667 52.1886C91.7125 52.1886 105.726 66.2376 105.726 83.4963L105.74 83.4691ZM70.4472 57.8871C70.3793 57.5199 70.0127 57.2751 69.6596 57.3431C60.3578 59.0431 52.6176 65.6664 49.4536 74.5881C49.3314 74.9417 49.5079 75.3362 49.861 75.4586C49.9289 75.4858 50.0103 75.4994 50.0918 75.4994C50.377 75.4994 50.635 75.3226 50.73 75.0505C53.7311 66.5776 61.0775 60.3079 69.9041 58.6895C70.2707 58.6215 70.5151 58.2679 70.4472 57.9007V57.8871ZM43.9675 39.3636C43.9675 39.3636 44.0762 39.3636 44.1305 39.35C44.9996 39.146 45.8551 38.874 46.697 38.5748C47.05 38.4524 47.2266 38.058 47.1044 37.7044C46.9686 37.3508 46.5748 37.174 46.2353 37.2964C45.4477 37.582 44.6465 37.8268 43.8317 38.0308C43.4651 38.1124 43.2478 38.4796 43.3293 38.8468C43.4108 39.1596 43.6824 39.3636 43.9947 39.3636H43.9675ZM94.4827 21.7921H98.1083C98.4886 21.7921 98.7873 21.4929 98.7873 21.1121C98.7873 20.7313 98.4886 20.4321 98.1083 20.4321H94.4827C94.1024 20.4321 93.8037 20.7313 93.8037 21.1121C93.8037 21.4929 94.1024 21.7921 94.4827 21.7921Z"
          fill="url(#paint0_linear_2040_92)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2040_92"
          x1="40.8714"
          y1="101.802"
          x2="95.3684"
          y2="7.54361"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2040_92">
          <rect
            width="92"
            height="106"
            fill="white"
            transform="translate(20 12)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
