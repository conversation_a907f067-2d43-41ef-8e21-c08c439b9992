import type { GemAttributesType } from '~/api/types'
import DataPointsContainer from '~/components/containers/data-points-container'
import { DataPointAdapted, TagAdapted } from '~/data'
import { H1, H3, Tag } from '~/tenset-components'

type GemsDetailsHeroSectionProps = Pick<
  GemAttributesType,
  | 'title'
  | 'logo'
  | 'tokenDataTitle'
  | 'tokenData'
  | 'launchDataTitle'
  | 'launchData'
  | 'status'
  | 'tags'
>

export function GemsDetailsHeroSection({
  title,
  logo,
  tokenDataTitle,
  tokenData,
  launchDataTitle,
  launchData,
  status,
  tags,
}: GemsDetailsHeroSectionProps) {
  const logoAttributes = logo.data.attributes
  return (
    <section className="flex flex-col gap-8 md:gap-16">
      <header className="flex flex-col gap-4 md:flex-row md:gap-8">
        <div>
          <img
            src={logoAttributes.url}
            alt={logoAttributes.alternativeText}
            width="96"
            height="96"
          />
        </div>

        <div className="flex flex-col items-start gap-4">
          <H1 isBold>{title}</H1>

          <div className="flex gap-2">
            <TagAdapted status={status} />
            {tags &&
              tags.map(tag => (
                <Tag key={tag.label} color={tag.color}>
                  {tag.label}
                </Tag>
              ))}
          </div>
        </div>
      </header>

      <main className="flex flex-col gap-8 md:gap-12">
        <div className="flex flex-col gap-4">
          <H3 isBold>{tokenDataTitle}</H3>

          <DataPointsContainer>
            {tokenData &&
              tokenData.map(item => (
                <DataPointAdapted key={item.label} field={item} />
              ))}
          </DataPointsContainer>
        </div>

        <div className="flex flex-col gap-4">
          <H3 isBold>{launchDataTitle}</H3>

          <DataPointsContainer>
            {launchData &&
              launchData.map(item => (
                <DataPointAdapted key={item.label} field={item} />
              ))}
          </DataPointsContainer>
        </div>
      </main>
    </section>
  )
}
