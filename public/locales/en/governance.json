{"active-poll": "Active poll", "connect-wallet": {"description": "Only 10set holders are eligible to participate in a poll. Connect your crypto wallet to see if you can vote.", "title": "Connect your wallet to vote"}, "hero": {"action": "Connect wallet to vote", "description": "Tenset governance enables 10set holders to participate in the decision-making process. Each holder can vote once per poll.", "title": "Influence the future direction of <PERSON><PERSON>"}, "last-active-poll": "There are no active polls", "meta": {"description": "10set token holders can voice their opinions and vote on decisions being made to influence the future direction of the project.", "title": "Governance"}, "no-rights-to-vote": {"available-to": "Voting is only available to the following users:", "description": "If you do not meet the above criteria, you can purchase any 10set on an exchange to become eligible. (Note that you must hold the 10set on a private wallet to vote - so if you use a CEX you need to withdraw to your wallet).", "infinity-stakers": "Infinity stakers", "tenset-holders": "10set token holders", "tglp-subscribers": "TGLP subscribers", "title": "You are unable to vote."}, "poll-ends-in-day": "Poll ends in {{time}} day", "poll-ends-in-days": "Poll ends in {{time}} days", "poll-ends-in-hour": "Poll ends in {{time}} hour", "poll-ends-in-hours": "Poll ends in {{time}} hours", "poll-ends-in-minutes": "Poll ends in {{time}} minutes", "previous-polls": "Previous polls", "purchase-tensets": {"description": "Only 10set holders are eligible to participate in a poll. Purchase any amount of 10set tokens on one of the exchanges first so you can vote.", "title": "Purchase 10set tokens to vote"}, "socials": {"description": "Join <PERSON> to find out about the results and upcoming polls", "discord": {"label": "Join Discord Community"}, "kakao": {"label": "Join <PERSON>"}, "line": {"label": "Join Line Community"}, "telegram": {"label": "Join <PERSON>egram <PERSON>"}, "title": "Stay up to date", "x": {"count": "130k+ followers", "label": "Follow us on X"}}, "submit-error": "Select one answer to submit", "vote-submitted": "Vote submitted", "vote-weight": "Your vote weight is {{voteWeight}}"}