import { Meta, StoryFn } from '@storybook/react'

import { Tag } from '../Tag'

import { Tooltip } from '.'

type TooltipType = typeof Tooltip

export default {
  title: 'Components/Tooltip',
  component: Tooltip,
  args: {
    text: 'The exclusive gem badge is awarded to projects that offer their tokens only on the tenset gem launch platform',
  },
} as Meta<TooltipType>

const Template: StoryFn<TooltipType> = arguments_ => (
  <Tooltip {...arguments_}>
    <button className="cursor-default">
      <Tag>Exclusive Gem</Tag>
    </button>
  </Tooltip>
)

export const TextS = {
  render: Template,
}

export const TextXs = {
  render: Template,

  args: {
    textSize: 'xs',
  },
}
