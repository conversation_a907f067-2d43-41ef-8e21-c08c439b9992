import { PrismaClient } from '@prisma/client'
import { Pool } from 'pg'

import { getServerEnvironment } from '~/environment/environment.server'

let database: PrismaClient | null = null

declare global {
  // eslint-disable-next-line no-var
  var __database: PrismaClient | undefined
}

if (getServerEnvironment().NODE_ENV === 'production') {
  if (!database) database = new PrismaClient()
} else {
  if (!global.__database) {
    global.__database = new PrismaClient()
  }

  database = global.__database
}

// Initialize `pg` for the telegram bot database
const telegramBotDatabase = new Pool({
  connectionString: getServerEnvironment().TELEGRAM_BOT_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false,
  },
})

export { database, telegramBotDatabase as telegramBotDb }
