generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("AGREEMENTS_DATABASE_URL")
}

model AgreementSignature {
  id          Int      @id(map: "agreement_signatures") @default(autoincrement())
  agreementId Int
  message     String   @db.VarChar(1024)
  wallet      String   @db.VarChar(42)
  signature   String   @db.VarChar(255)
  createdAt   DateTime @db.Date

  @@unique([wallet, agreementId], name: "walletAgreement")
  @@map("public.agreement_signatures")
}

model Agreement {
  id          Int      @id(map: "agreements") @default(autoincrement())
  description String   @db.VarChar(255)
  message     String   @db.VarChar(1024)
  createdAt   DateTime @db.Date

  @@map("public.agreements")
}

model AssignedWallets {
  subscription_address String   @db.Var<PERSON>har(42)
  assigned_address     String   @db.Var<PERSON>har(42)
  message              String   @db.VarChar()
  sign                 String   @db.Var<PERSON>har()
  date                 DateTime @db.Timestamp(6)
  launch_id            String   @db.Var<PERSON>har(255)

  @@unique([subscription_address, launch_id], name: "walletLaunch")
  @@map("assigned_wallets")
}

model ReferralLock {
  id                   Int      @id(map: "_id") @default(autoincrement())
  subscription_address String   @db.VarChar(42)
  lock_id              Int      @db.Integer()
  message              String   @db.VarChar()
  sign                 String   @db.VarChar()
  date                 DateTime @db.Timestamp(6)

  @@unique([subscription_address, lock_id], name: "walletLock")
  @@map("referral_locks")
}
