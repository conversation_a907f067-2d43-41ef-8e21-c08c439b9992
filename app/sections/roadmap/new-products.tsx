import {
  bannerTensetCard,
  bannerTensetDEX,
  bannerTensetHardwareWallet,
  bannerTensetLauncher,
} from '~/assets/images'
import { ProductCard } from '~/components/roadmap/product-card'
import { H2, TextL } from '~/tenset-components'

export function NewProductsRoadmap() {
  const productDetails = [
    {
      image: bannerTensetDEX,
      title: 'Tenset DEX',
      description:
        'Tenset DEX will differentiate itself with an intuitive interface and advanced trading features, including order books, limit orders, and stop-loss options. It will support liquidity pools, trading pairs, and fees paid in the 10SET tokens. The platform will explore perpetual features and integrate account abstraction for seamless onboarding and gasless transactions. Users and loyal community members can expect exclusive bonuses and benefits.',
    },
    {
      image: bannerTensetLauncher,
      title: 'Tenset Launcher',
      description:
        'Tenset Launcher is a self-service platform for projects to presale, launch, and vest their tokens. It offers streamlined process with options for obtaining badges such as team KYC, audited by Tenset Security, etc. The platform connects projects with recognized agencies and service providers and gives projects access to exclusive discounts. Tenset Launcher provide Tenset Community many more opportunities to invest in promising projects on favorable terms.',
    },
    {
      image: bannerTensetCard,
      title: 'Tenset Card',
      description:
        'Stake your 10SET to get a unique, Tenset-branded debit card and pay with crypto for your everyday expenses. This is the first self-custody debit card in crypto.',
    },
    {
      image: bannerTensetHardwareWallet,
      title: 'Tenset Hardware Wallet',
      description:
        'Slim as a bank card, secure as a bank vault. Store, buy, earn, transfer and swap thousands of coins and tokens. Tenset partnered with the most secure and convenient hardware wallet manufacturer in the world. All dApps from the Tenset ecosystem are supported.',
    },
  ]

  return (
    <div className="flex flex-col max-w-screen-lg gap-4">
      <H2 isBold>New Products</H2>
      <TextL>Introducing new products to enhance the Tenset ecosystem.</TextL>

      <div className="grid gap-6 md:grid-cols-2 mt-4">
        {productDetails.map((product, index) => (
          <ProductCard
            key={index}
            image={product.image}
            title={product.title}
            description={product.description}
          />
        ))}
      </div>
    </div>
  )
}
