import { AssetProps } from './index'

export default function AutomaticSwapback({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M75.53 111.05C75.23 107.1 71.91 104.01 67.97 104.01C64.03 104.01 61.03 106.82 60.48 110.51C41.42 107.38 26.08 92.2101 22.69 73.2401L30.06 78.11L30.61 77.28L21.86 71.5L21.59 71.89L16 80.2L16.83 80.76L21.71 73.5C25.2 92.87 40.89 108.34 60.36 111.51C60.36 111.55 60.35 111.59 60.35 111.63C60.35 115.83 63.76 119.24 67.96 119.24C72.16 119.24 75.57 115.83 75.57 111.63C75.57 111.5 75.55 111.37 75.54 111.25C75.54 111.19 75.52 111.13 75.52 111.07L75.53 111.05ZM67.97 118.23C64.33 118.23 61.36 115.26 61.36 111.62C61.36 111.53 61.37 111.45 61.38 111.37C61.39 111.29 61.4 111.21 61.41 111.13C61.67 107.7 64.55 105.01 67.97 105.01C71.39 105.01 74.27 107.7 74.53 111.13C74.53 111.21 74.55 111.29 74.56 111.38C74.57 111.46 74.58 111.54 74.58 111.63C74.58 115.27 71.61 118.24 67.97 118.24V118.23ZM64.39 71.41L74.47 65.1201L64.39 58.83V71.42V71.41ZM65.39 60.6201L72.58 65.11L65.39 69.6V60.6201ZM95.06 65.86L90.44 63.72C90.33 61.96 90.03 60.2301 89.53 58.5601L93.13 54.96C94.01 54.08 94.17 52.6701 93.5 51.6201L90.72 47.27C90.04 46.2 88.72 45.76 87.54 46.19L82.76 47.94C81.46 46.79 80.01 45.78 78.46 44.94V39.84C78.46 38.59 77.58 37.49 76.36 37.22L71.32 36.11C70.1 35.84 68.83 36.47 68.31 37.6L66.17 42.22C64.41 42.33 62.68 42.63 61.01 43.13L57.41 39.53C56.53 38.65 55.12 38.49 54.07 39.16L49.72 41.94C48.67 42.61 48.21 43.9501 48.64 45.1201L50.39 49.9C49.24 51.2 48.23 52.64 47.39 54.2H42.3C41.05 54.2 39.95 55.08 39.68 56.3L38.57 61.34C38.3 62.56 38.93 63.83 40.06 64.35L44.68 66.4901C44.79 68.25 45.09 69.98 45.59 71.65L41.99 75.25C41.11 76.13 40.95 77.54 41.62 78.59L44.4 82.94C45.07 83.99 46.41 84.44 47.58 84.02L52.36 82.27C53.66 83.42 55.11 84.43 56.66 85.27V90.3701C56.66 91.6201 57.54 92.7201 58.76 92.9901L63.8 94.1C63.99 94.14 64.18 94.16 64.37 94.16C65.4 94.16 66.37 93.56 66.81 92.61L68.95 87.9901C70.71 87.8801 72.44 87.58 74.11 87.08L77.7 90.6801C78.58 91.5601 79.99 91.72 81.04 91.05L85.39 88.27C86.44 87.6 86.9 86.26 86.47 85.09L84.72 80.3101C85.87 79.0101 86.88 77.57 87.72 76.01H92.81C94.06 76.01 95.16 75.13 95.43 73.91L96.54 68.8701C96.81 67.6501 96.18 66.38 95.05 65.86H95.06ZM95.58 68.66L94.47 73.7C94.3 74.46 93.61 75.02 92.83 75.02H87.43C87.24 75.02 87.07 75.12 86.99 75.28C86.12 76.95 85.04 78.49 83.79 79.86C83.66 80 83.63 80.1901 83.69 80.3701L85.55 85.4301C85.82 86.1701 85.54 87.0101 84.88 87.4301L80.53 90.21C79.87 90.63 78.99 90.54 78.43 89.98L74.62 86.16C74.49 86.03 74.29 85.98 74.12 86.04C72.35 86.59 70.5 86.92 68.62 87.01C68.43 87.01 68.27 87.1301 68.19 87.3L65.92 92.2C65.59 92.91 64.81 93.3 64.03 93.13L58.99 92.02C58.23 91.85 57.67 91.16 57.67 90.38V84.98C57.67 84.79 57.57 84.62 57.41 84.54C55.74 83.67 54.2 82.59 52.83 81.34C52.74 81.26 52.62 81.21 52.49 81.21C52.43 81.21 52.37 81.2101 52.32 81.2401L47.26 83.1C46.52 83.37 45.68 83.0901 45.26 82.4301L42.48 78.08C42.06 77.42 42.15 76.54 42.71 75.98L46.53 72.17C46.66 72.04 46.71 71.84 46.65 71.67C46.1 69.9 45.77 68.05 45.68 66.17C45.68 65.98 45.56 65.8201 45.39 65.7401L40.49 63.47C39.78 63.14 39.39 62.35 39.55 61.58L40.66 56.54C40.83 55.78 41.52 55.22 42.3 55.22H47.7C47.89 55.22 48.06 55.12 48.14 54.96C49.01 53.29 50.09 51.75 51.34 50.38C51.47 50.24 51.5 50.0501 51.44 49.8701L49.58 44.8101C49.31 44.0701 49.59 43.2301 50.25 42.8101L54.6 40.03C55.26 39.61 56.14 39.7 56.69 40.26L60.5 44.08C60.63 44.21 60.83 44.26 61 44.2C62.77 43.65 64.62 43.32 66.5 43.23C66.69 43.23 66.85 43.11 66.93 42.94L69.2 38.04C69.53 37.33 70.32 36.93 71.09 37.1L76.13 38.21C76.91 38.38 77.45 39.06 77.45 39.85V45.25C77.45 45.44 77.55 45.61 77.71 45.69C79.38 46.56 80.92 47.64 82.29 48.89C82.43 49.02 82.62 49.0501 82.8 48.9901L87.86 47.13C88.61 46.85 89.43 47.1301 89.86 47.8L92.64 52.15C93.06 52.81 92.97 53.69 92.41 54.25L88.59 58.0601C88.46 58.1901 88.41 58.3901 88.47 58.5601C89.02 60.3201 89.35 62.1701 89.44 64.0601C89.44 64.2501 89.56 64.41 89.73 64.4901L94.63 66.76C95.34 67.09 95.73 67.88 95.57 68.65L95.58 68.66ZM119.1 49.4901L114.22 56.75C110.73 37.38 95.04 21.91 75.57 18.73C75.57 18.69 75.58 18.65 75.58 18.61C75.58 14.41 72.17 11 67.97 11C63.77 11 60.36 14.41 60.36 18.61C60.36 18.74 60.38 18.87 60.39 19C60.39 19.06 60.41 19.1201 60.41 19.1801C60.71 23.1301 64.03 26.22 67.97 26.22C71.91 26.22 74.91 23.41 75.46 19.72C94.51 22.85 109.86 38.02 113.25 57L105.88 52.13L105.33 52.96L114.08 58.7401L114.35 58.35L119.94 50.04L119.11 49.48L119.1 49.4901ZM74.56 18.86C74.55 18.94 74.54 19.02 74.53 19.1C74.27 22.53 71.39 25.22 67.97 25.22C64.55 25.22 61.67 22.53 61.41 19.1C61.41 19.02 61.39 18.94 61.38 18.86C61.37 18.78 61.36 18.69 61.36 18.61C61.36 14.96 64.33 12 67.97 12C71.61 12 74.58 14.97 74.58 18.61C74.58 18.69 74.57 18.78 74.56 18.86ZM68.02 51.7C60.62 51.7 54.61 57.72 54.61 65.11C54.61 72.5 60.63 78.52 68.02 78.52C75.41 78.52 81.43 72.5 81.43 65.11C81.43 57.72 75.41 51.7 68.02 51.7ZM68.02 77.53C61.18 77.53 55.61 71.9601 55.61 65.1201C55.61 58.2801 61.18 52.71 68.02 52.71C74.86 52.71 80.43 58.2801 80.43 65.1201C80.43 71.9601 74.86 77.53 68.02 77.53Z"
        fill="url(#paint0_linear_2185_41)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_41"
          x1="44.03"
          y1="106.58"
          x2="91.91"
          y2="23.65"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
