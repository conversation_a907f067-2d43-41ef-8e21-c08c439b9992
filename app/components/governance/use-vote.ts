import { useState, useEffect } from 'react'

import type { Voting } from './voting'

import { useWallet } from '~/tenset-web3'
import { getEnvironment } from '~/environment'

export const useVote = (voting: Voting) => {
  const [voted, setVoted] = useState(true)
  const [voteable, setVoteable] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [voteWeight, setVoteWeight] = useState(0)

  const { wallet } = useWallet()

  useEffect(() => {
    if (voting.endsInSeconds <= 0) return

    if (!wallet?.account) {
      setVoted(false)
      setVoteable(false)

      return
    }

    if (!Array.isArray(voting.votes)) {
      setVoteable(false)

      return
    }

    const walletAlreadyVoted =
      voting.votes.findIndex(({ account }) => account === wallet.account) !== -1

    setVoted(walletAlreadyVoted)

    if (walletAlreadyVoted) {
      setVoteable(false)

      return
    }

    setProcessing(true)

    fetch(
      `${getEnvironment().FIREBASE_API_HOST}/voteable/weight/${wallet.account}`
    )
      .then(response => response.json())
      .then(({ voteWeight }) => {
        setVoteWeight(voteWeight)
        setVoteable(voteWeight > 0)
      })
      .catch(() => setVoteable(false))
      .finally(() => setProcessing(false))
  }, [wallet?.account])

  const vote = (wallet: string, message: string, signature: string) => {
    setProcessing(true)

    return new Promise(resolve => {
      if (voting.endsInSeconds <= 0) return resolve(false)

      fetch(`${getEnvironment().FIREBASE_API_HOST}/vote/${voting.uuid}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ wallet, message, signature }),
      })
        .then(({ status }) => {
          setVoted(status === 200)

          resolve(true)
        })
        .finally(() => setProcessing(false))
    })
  }

  return { vote, voted, voteable, processing, voteWeight }
}
