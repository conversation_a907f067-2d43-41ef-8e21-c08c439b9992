import { Meta, StoryFn } from '@storybook/react'

import { H3 } from '../Typo/Header'
import { Text } from '../Typo/Text'

import { ProgressCircle } from './index'

type ProgressCircleType = typeof ProgressCircle

const numerator = 25
const denominator = 100

export default {
  title: 'Components/ProgressCircle',
  component: ProgressCircle,
  parameters: {
    docs: {
      description: {
        component: 'Fits to the parent size',
      },
    },
  },
  args: {
    numerator,
    denominator,
    children: (
      <>
        <H3 isBold>
          {numerator} / {denominator}
        </H3>

        <Text>Progress circle</Text>
      </>
    ),
  },
} as Meta<ProgressCircleType>

const Template: StoryFn<ProgressCircleType> = arguments_ => (
  <div className="relative w-96 h-auto">
    <ProgressCircle {...arguments_} />
  </div>
)

export const Preview = Template.bind({})
