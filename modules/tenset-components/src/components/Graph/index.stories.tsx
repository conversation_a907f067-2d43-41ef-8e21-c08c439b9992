import { Meta, StoryFn } from '@storybook/react'

import { graphBurnedMonthlyData, graphTokenPriceData } from './story-data'

import {
  Graph,
  graphBurnedMonthlyArguments,
  graphTokenPriceArguments,
} from './index'

type GraphType = typeof Graph

export default {
  title: 'Components/Graph',
  component: Graph,
  parameters: {
    docs: {
      description: {
        component: 'Recharts (https://recharts.org/)',
      },
    },
  },
  args: {},
} as Meta<GraphType>

const Template: StoryFn<GraphType> = _arguments => (
  <div className="w-[500px] h-[250px]">
    <Graph {..._arguments} />
  </div>
)

export const BurnedMonthly = {
  render: Template,

  args: {
    data: graphBurnedMonthlyData,
    ...graphBurnedMonthlyArguments,
  },
}

export const TokenPrice = {
  render: Template,

  args: {
    data: graphTokenPriceData,
    ...graphTokenPriceArguments(graphTokenPriceData),
  },
}
