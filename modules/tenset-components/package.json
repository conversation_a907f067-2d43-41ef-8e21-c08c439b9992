{"name": "tenset-components", "private": true, "version": "0.0.0", "license": "UNLICENSED", "files": ["package.json", "yarn.lock", "README.md"], "scripts": {"dev": "yarn storybook:start", "dev:no-open": "yarn storybook:start --no-open", "storybook:start": "STORYBOOK=true storybook dev -p 6006", "storybook:build": "yarn ci:prebuilt && STORYBOOK=true storybook build", "lint": "eslint --ext \".js,.ts,.md,.jsx,.tsx,.mdx\" --ignore-path .gitignore . --fix", "prepare": "husky", "ci:prebuilt": "yarn lint", "update-deps": "ncu '/^(?!stylelint).*$/' -u && yarn"}, "devDependencies": {"@babel/core": "7.24.1", "@babel/preset-env": "7.24.1", "@babel/preset-react": "7.24.1", "@babel/preset-typescript": "7.24.1", "@commitlint/cli": "19.2.1", "@commitlint/config-conventional": "19.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-tooltip": "1.0.7", "@react-spring/web": "9.7.3", "@remix-run/react": "2.8.1", "@restart/ui": "1.6.8", "@storybook/addon-actions": "8.0.1", "@storybook/addon-docs": "8.0.1", "@storybook/addon-essentials": "8.0.1", "@storybook/addon-interactions": "8.0.1", "@storybook/addon-links": "8.0.1", "@storybook/addon-mdx-gfm": "8.0.1", "@storybook/addon-styling-webpack": "1.0.0", "@storybook/addon-webpack5-compiler-babel": "3.0.3", "@storybook/manager-api": "8.0.1", "@storybook/mdx2-csf": "1.1.0", "@storybook/preview-api": "8.0.1", "@storybook/react": "8.0.1", "@storybook/react-webpack5": "8.0.1", "@storybook/test": "8.0.1", "@types/react": "18.2.67", "@types/react-dom": "18.2.22", "@typescript-eslint/eslint-plugin": "7.3.1", "@typescript-eslint/parser": "7.3.1", "autoprefixer": "10.4.18", "babel-loader": "9.1.3", "clsx": "2.1.0", "cssnano": "6.1.0", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-alias": "1.1.2", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-import-helpers": "1.3.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-mdx": "3.1.5", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-storybook": "0.8.0", "eslint-plugin-unicorn": "51.0.1", "history": "5.3.0", "husky": "9.0.11", "lint-staged": "15.2.2", "npm-check-updates": "16.14.17", "postcss": "8.4.36", "postcss-loader": "8.1.1", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "0.5.12", "react": "18.2.0", "react-dom": "18.2.0", "recharts": "^2.12.7", "storybook": "8.0.1", "stylelint": "15.11.0", "stylelint-config-prettier": "9.0.5", "stylelint-config-standard": "35.0.0", "tailwindcss": "3.4.1", "typescript": "5.4.2"}, "resolutions": {"jackspeak": "2.1.1"}, "engines": {"node": ">=20.9.0 <21"}, "eslintIgnore": ["/node_modules", "/build", "/public/build"]}