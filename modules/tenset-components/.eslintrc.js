/**
 * @type {import('@types/eslint').Linter.BaseConfig}
 */
module.exports = {
  root: true,
  env: {
    browser: true,
    es6: true,
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: 'module',
  },
  settings: {
    react: {
      version: 'detect',
    },
    'import/parser': '@typescript-eslint/parser',
    'import/resolver': {
      node: {
        paths: ['~/'],
        extensions: ['.js', '.ts', '.tsx', '.jsx'],
      },
      alias: {
        map: [['~', './src/']],
        extensions: ['.ts', '.js', '.tsx'],
      },
    },
  },
  plugins: ['@typescript-eslint', 'prettier', 'eslint-plugin-import-helpers'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'prettier',
    'plugin:eslint-comments/recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:react-hooks/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:sonarjs/recommended',
    'plugin:unicorn/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:storybook/recommended',
  ],
  overrides: [
    {
      files: '**/*.{md,mdx}',
      extends: 'plugin:mdx/recommended',
      settings: {
        'mdx/code-blocks': true,
      },
    },
  ],
  ignorePatterns: [
    'README.md',
    'rollup.config.js',
    'postcss.config.js',
    'tailwind.config.js',
    '.eslintrc.js',
  ],
  rules: {
    eqeqeq: 'error',
    'react/jsx-uses-react': 'error',
    'prettier/prettier': ['error', { endOfLine: 'auto' }],
    'prefer-const': 'warn',
    'no-console': 'off',
    'eslint-comments/no-unused-disable': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    // empty functions are helpful for defining default values
    '@typescript-eslint/no-empty-function': 'off',
    // ignore only until https://github.com/sindresorhus/eslint-plugin-unicorn/pull/1628 is opened
    'unicorn/filename-case': [
      'error',
      {
        case: 'kebabCase',
        ignore: [/^\$/, 'README.md$', '.*.mdx$'],
      },
    ],
    'import/order': [
      'warn',
      {
        'newlines-between': 'always',
      },
    ],
    'unicorn/no-null': 'off',
    'unicorn/no-nested-ternary': 'off',
    // we use typescript instead of propTypes
    'react/prop-types': 'off',
    'unicorn/prevent-abbreviations': [
      'error',
      {
        allowList: {
          // props and ref are popular lingo in react world
          Props: true,
          props: true,
          ref: true,
          env: true,
          db: true,
        },
      },
    ],
  },
}
