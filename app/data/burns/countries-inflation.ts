export enum CountriesInflationCountry {
  JAPAN = 'Japan',
  KOREA = 'Korea',
  USA = 'USA',
  TURKEY = 'Turkey',
  CHINA = 'China',
}

export interface CountriesInflationCountryData {
  country: CountriesInflationCountry
  addPerSecond: number
}

export interface CountriesInflationCountryScrappedData
  extends CountriesInflationCountryData {
  baseValue: number
  inflationPercentage: number
}

export interface CountriesInflationScrappedData {
  baseTime: number
  debt: CountriesInflationCountryScrappedData[]
}

/*
 * Scraped from https://www.usdebtclock.org/world-debt-clock.html 31.05.2024
 * Sources -> world-debt-clock.html
 * */
export const countriesInflationData: CountriesInflationScrappedData = {
  baseTime: 19_722.166_66 * 24 * 60 * 60,
  debt: [
    {
      country: CountriesInflationCountry.JAPAN,
      baseValue: 13_167_947_726_027,
      addPerSecond: 21_434.550_989,
      inflationPercentage: 37.1,
    },
    {
      country: CountriesInflationCountry.KOREA,
      baseValue: 1_039_716_186_411,
      addPerSecond: 1692.431_507,
      inflationPercentage: 4.4,
    },
    {
      country: CountriesInflationCountry.USA,
      baseValue: 34_362_662_000_000,
      addPerSecond: 31_898.148_148,
      inflationPercentage: 7.6,
    },
    {
      country: CountriesInflationCountry.TURKEY,
      baseValue: 416_748_130_082,
      addPerSecond: 678.375_19,
      inflationPercentage: 6.3,
    },
    {
      country: CountriesInflationCountry.CHINA,
      baseValue: 14_141_009_504_932,
      addPerSecond: 23_018.483_638,
      inflationPercentage: 0.3,
    },
  ],
}
