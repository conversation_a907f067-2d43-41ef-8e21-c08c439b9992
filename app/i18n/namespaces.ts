export enum Namespace {
  BRIDGE = 'bridge',
  BURNS = 'burns',
  BUSINESS = 'business',
  COMMON = 'common',
  CONTACT_PAGE = 'contact-page',
  FORM_VALIDATORS = 'form-validators',
  GEM_LAUNCH_PLATFORM_SUBSCRIPTION = 'gem-launch-platform-subscription',
  GEM_LAUNCH_PLATFORM = 'gem-launch-platform',
  GEM = 'gem',
  GOVERNANCE = 'governance',
  HOMEPAGE = 'homepage',
  INCUBATOR = 'incubator',
  NEWS = 'news',
  VERIFICATION = 'verification',
  VERIFY_OWNERSHIP = 'verify-ownership',
  ZEALY = 'zealy',
}

export const DEFAULT_NAMESPACE = Namespace.COMMON

export const NAMESPACES = Object.values(Namespace)
