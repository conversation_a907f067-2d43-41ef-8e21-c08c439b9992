import { Utils } from '.'

export interface Config {
  walletConnectProjectId: string
  tensetToken: Utils.Addresses
  tensetX: {
    infinityVaultBridgeExchanger: Utils.Addresses
    infinityVaultBurnExchanger: Utils.Addresses
    token: Utils.Addresses
  }
  gemLaunchPlatform: {
    subscribe: Utils.Addresses
    membership: Utils.Addresses
    withdraw: Utils.Addresses
    extend: Utils.Addresses
    upgrade: Utils.Addresses
    lifetimeMembership: Utils.Addresses
    oldWithdraw: Utils.Addresses
  }
  agreementsApiUrl: string
  infinity: {
    vault: Utils.Addresses
    vaultBridge: Utils.Addresses
    nftVault: Utils.Addresses
    withdrawalFee: Utils.Addresses
    nftWithdrawalFee: Utils.Addresses
  }
  zealy: {
    rewards: Utils.Addresses
    nft: Utils.Addresses
    nftMinter: Utils.Addresses
  }
  utils: {
    erc721Multisender: Utils.Addresses
  }
}
