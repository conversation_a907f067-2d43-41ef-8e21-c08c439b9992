import { AssetProps } from './index'

export default function Subscription({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2232_119)">
        <path
          d="M103.207 22.9947H95.9647V20.7413C95.9647 17.5794 93.3749 15.011 90.1713 15.011H42.8785C39.675 15.011 37.0851 17.5794 37.0851 20.7413V22.9947H29.8433C26.6397 22.9947 24.0375 25.563 24.0375 28.725V102.298C24.0375 105.46 26.6397 108.028 29.8433 108.028H37.0851V110.282C37.0851 113.444 39.675 116.012 42.8785 116.012H90.1713C93.3749 116.012 95.9647 113.444 95.9647 110.282V108.028H103.207C106.41 108.028 109.012 105.46 109.012 102.298V28.7129C109.012 25.5509 106.41 22.9826 103.207 22.9826V22.9947ZM47.7146 37.4961L66.5311 26.7745L85.3475 37.4961V58.9514L66.5311 69.673L47.7146 58.9514V37.4961ZM76.3136 108.137H56.7362C54.7723 108.137 53.1766 106.575 53.1766 104.636C53.1766 103.667 53.5817 102.795 54.22 102.153C54.8582 101.511 55.7542 101.123 56.7362 101.123H76.3136C78.2775 101.123 79.8732 102.698 79.8732 104.636C79.8732 106.575 78.2775 108.137 76.3136 108.137Z"
          fill="#15121A"
        />
        <path
          d="M77.541 55.1595V42.0028L66.531 35.7274L55.5087 42.0028V55.1595L66.531 61.435L77.541 55.1595Z"
          fill="#15121A"
        />
        <path
          d="M74.8898 99.112H55.3124C53.0171 99.112 51.1391 100.953 51.1391 103.231C51.1391 105.509 53.0048 107.35 55.3124 107.35H74.8898C77.1851 107.35 79.0631 105.509 79.0631 103.231C79.0631 100.953 77.1974 99.112 74.8898 99.112ZM74.8898 106.126H55.3124C53.6922 106.126 52.3665 104.83 52.3665 103.219C52.3665 101.608 53.6799 100.311 55.3124 100.311H74.8898C76.51 100.311 77.8356 101.608 77.8356 103.219C77.8356 104.83 76.5223 106.126 74.8898 106.126ZM53.4712 83.7989C53.4712 84.1381 53.7413 84.4046 54.0849 84.4046H76.1172C76.4609 84.4046 76.731 84.1381 76.731 83.7989C76.731 83.4597 76.4609 83.1931 76.1172 83.1931H54.0849C53.7413 83.1931 53.4712 83.4597 53.4712 83.7989ZM59.2279 88.4994C58.8842 88.4994 58.6141 88.7659 58.6141 89.1051C58.6141 89.4444 58.8842 89.7109 59.2279 89.7109H70.9621C71.3057 89.7109 71.5758 89.4444 71.5758 89.1051C71.5758 88.7659 71.3057 88.4994 70.9621 88.4994H59.2279ZM101.783 20.9837H95.1546V19.336C95.1546 15.847 92.2825 13 88.7352 13H41.4547C37.9197 13 35.0353 15.8349 35.0353 19.336V20.9837H28.4072C24.8722 20.9837 21.9877 23.8185 21.9877 27.3197V100.893C21.9877 104.382 24.8599 107.229 28.4072 107.229H35.0353V108.877C35.0353 112.366 37.9075 115.213 41.4547 115.213H88.7352C92.2702 115.213 95.1546 112.378 95.1546 108.877V107.229H101.783C105.318 107.229 108.202 104.394 108.202 100.893V27.3076C108.202 23.8185 105.33 20.9715 101.783 20.9715V20.9837ZM35.0353 106.005H28.4072C25.5473 106.005 23.2152 103.703 23.2152 100.881V27.3076C23.2152 24.4848 25.5473 22.183 28.4072 22.183H35.0353V105.993V106.005ZM93.9272 108.864C93.9272 111.687 91.5951 113.989 88.7352 113.989H41.4547C38.5948 113.989 36.2627 111.687 36.2627 108.864V19.336C36.2627 16.5133 38.5948 14.2115 41.4547 14.2115H88.7352C91.5951 14.2115 93.9272 16.5133 93.9272 19.336V108.864ZM106.962 100.881C106.962 103.703 104.63 106.005 101.77 106.005H95.1424V22.1951H101.77C104.63 22.1951 106.962 24.4969 106.962 27.3197V100.893V100.881ZM84.4515 35.7879C84.4024 35.7031 84.3287 35.6426 84.2551 35.5941C84.2551 35.5941 84.2428 35.582 84.2305 35.5699L65.4141 24.8483C65.23 24.7392 64.9845 24.7392 64.8004 24.8483L45.9839 35.5699C45.9839 35.5699 45.9716 35.582 45.9594 35.5941C45.8735 35.6426 45.8121 35.7031 45.7507 35.7879C45.7016 35.8727 45.6771 35.9697 45.6771 36.0545V36.0787V57.5219C45.6771 57.7399 45.7998 57.9338 45.9839 58.0428L64.8004 68.7644H64.8249C64.9108 68.8129 64.9968 68.8371 65.1072 68.8371C65.2177 68.8371 65.3036 68.8129 65.3895 68.7644H65.4141L84.2305 58.0428C84.4146 57.9338 84.5374 57.7399 84.5374 57.5219V36.0787V36.0545C84.5374 35.9575 84.5128 35.8727 84.4637 35.7879H84.4515ZM65.095 26.0719L82.684 36.0908L76.0804 39.8827L65.4018 33.8011C65.4018 33.8011 65.3527 33.789 65.3282 33.7769C65.2913 33.7648 65.2545 33.7405 65.2054 33.7405C65.1686 33.7405 65.1318 33.7405 65.095 33.7405C65.0581 33.7405 65.0213 33.7405 64.9845 33.7405C64.9354 33.7405 64.8986 33.7648 64.8617 33.7769C64.8372 33.7769 64.8126 33.7769 64.7881 33.8011L54.1095 39.8827L47.5059 36.0908L65.095 26.0719ZM64.4812 67.2137L46.8922 57.1948V37.1569L53.459 40.9246V53.7663C53.459 53.7663 53.459 53.8148 53.4712 53.839C53.4712 53.8874 53.4712 53.9238 53.4958 53.9601C53.508 53.9965 53.5326 54.0328 53.5449 54.057C53.5694 54.0934 53.5817 54.1176 53.6062 54.154C53.6308 54.1903 53.6676 54.2145 53.7044 54.2388C53.729 54.2509 53.7413 54.2751 53.7658 54.2872L64.4812 60.3931V67.2258V67.2137ZM64.4812 58.9757L54.6864 53.4028V41.6152L64.4812 47.2243V58.9757ZM65.095 46.1703L55.3369 40.5733L65.095 35.0126L74.853 40.5733L65.095 46.1703ZM75.5035 41.6152V53.4028L65.7087 58.9757V47.2243L75.5035 41.6152ZM83.2977 57.1827L65.7087 67.2016V60.3689L76.4241 54.263C76.4241 54.263 76.4609 54.2267 76.4855 54.2145C76.5223 54.1903 76.5468 54.1661 76.5837 54.1297C76.6082 54.1055 76.6328 54.0692 76.645 54.0328C76.6696 53.9965 76.6819 53.9722 76.6941 53.9359C76.7064 53.8996 76.7187 53.8511 76.7187 53.8147C76.7187 53.7905 76.731 53.7663 76.731 53.7421V40.9004L83.2977 37.1327V57.1705V57.1827ZM70.9621 79.4739C71.3057 79.4739 71.5758 79.2074 71.5758 78.8681C71.5758 78.5289 71.3057 78.2624 70.9621 78.2624H59.2279C58.8842 78.2624 58.6141 78.5289 58.6141 78.8681C58.6141 79.2074 58.8842 79.4739 59.2279 79.4739H70.9621Z"
          fill="url(#paint0_linear_2232_119)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2232_119"
          x1="36.3364"
          y1="113.262"
          x2="92.7327"
          y2="14.2996"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2232_119">
          <rect
            width="87"
            height="103"
            fill="white"
            transform="translate(22 13)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
