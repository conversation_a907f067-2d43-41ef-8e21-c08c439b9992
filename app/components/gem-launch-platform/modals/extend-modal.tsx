import { BigNumber } from 'ethers'
import { formatEther, parseEther } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import { useEffect, useState, type ChangeEvent } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'

import { PurchaseTokensModal } from './purchase-tokens-modal'

import type { GlpLock } from '~/api/types'
import { SwitchNetworkButton } from '~/components/switch-network/button'
import { BSC_NETWORK } from '~/config'
import { getTierAmount } from '~/data/gem-launch-platform/tiers-privileges'
import { Namespace } from '~/i18n'
import {
  Button,
  Input,
  Modal,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalMain,
  ModalTitle,
  Text,
  TextS,
} from '~/tenset-components'
import {
  chains,
  parseEthersError,
  useGlpContracts,
  useTensetContract,
  useWallet,
} from '~/tenset-web3'

interface ExtendLockModalProps {
  lock?: GlpLock
  setLock: (lock?: GlpLock) => void
  onExtend?: (
    lock: GlpLock,
    tokensAmount: number,
    numberOfYears: number
  ) => void
  extendAllowance: BigNumber
  setExtendAllowance: (allowance: BigNumber) => void
  tensetBalance: BigNumber
}

export function ExtendLockModal({
  lock,
  setLock,
  onExtend,
  extendAllowance,
  setExtendAllowance,
  tensetBalance,
}: ExtendLockModalProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  const { wallet } = useWallet()

  const { tensetContract } = useTensetContract(BSC_NETWORK)

  const { glpExtendContract } = useGlpContracts(BSC_NETWORK)

  const [isExtending, setIsExtending] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | undefined>()

  const [numberOfYearsInput, setNumberOfYearsInput] = useState<string>('1')
  const [numberOfYears, setNumberOfYears] = useState<BigNumber>(
    BigNumber.from('1')
  )

  useEffect(() => {
    setNumberOfYears(BigNumber.from(numberOfYearsInput || '1'))
  }, [numberOfYearsInput])

  function onNumberOfYearsInput(event: ChangeEvent<HTMLInputElement>) {
    const { currentTarget } = event
    const value = currentTarget.value.replace(/^0+(?=\d)/, '') // remove leading zeros

    if (value === '0') return event.preventDefault()

    try {
      BigNumber.from(value || '1').toNumber()

      setNumberOfYearsInput(value)
    } catch {
      event.preventDefault()
    }
  }

  const [singleExtensionCost, setSingleExtensionCost] = useState<BigNumber>(
    BigNumber.from('0')
  )

  const [extensionCost, setExtensionCost] = useState<BigNumber>(
    BigNumber.from('0')
  )

  const [requiredAllowance, setRequiredAllowance] = useState(
    BigNumber.from('0')
  )

  const [requiredBalance, setRequiredBalance] = useState<BigNumber>(
    BigNumber.from('0')
  )

  useEffect(() => {
    setLock(undefined)
  }, [wallet?.account])

  useEffect(() => {
    if (!lock || !numberOfYears) {
      setSingleExtensionCost(BigNumber.from('0'))

      return
    }

    const tierAmount = getTierAmount(lock.membershipAttributes!.tierId)

    // 1/5 of the lock tier amount
    setSingleExtensionCost(parseEther(tierAmount.toString()).div(5))
  }, [lock, numberOfYears.toString()])

  useEffect(() => {
    if (!lock || !numberOfYears) {
      setExtensionCost(BigNumber.from('0'))

      return
    }

    setExtensionCost(singleExtensionCost.mul(numberOfYears))
  }, [singleExtensionCost.toString(), numberOfYears.toString()])

  useEffect(() => {
    setRequiredBalance(extensionCost.sub(tensetBalance))
  }, [extensionCost.toString(), tensetBalance.toString()])

  useEffect(() => {
    if (!requiredBalance.gt(0)) {
      setErrorMessage(undefined)

      return
    }

    setErrorMessage(
      t('extend.error', {
        amount: Number.parseFloat(formatEther(requiredBalance)),
      })
    )
  }, [requiredBalance.toString()])

  useEffect(() => {
    setRequiredAllowance(extensionCost.sub(extendAllowance))
  }, [extensionCost.toString(), extendAllowance.toString()])

  async function increaseAllowance() {
    if (
      !wallet ||
      wallet?.isProcessing ||
      !glpExtendContract ||
      !tensetContract ||
      !lock ||
      isExtending
    )
      return

    setErrorMessage(undefined)
    setIsExtending(true)

    await wallet
      .interact(tensetContract)
      .increaseAllowance(glpExtendContract, requiredAllowance)
      .then(() => {
        setExtendAllowance(extendAllowance.add(requiredAllowance))

        extend()
      })
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)

        setIsExtending(false)
      })
  }

  async function extend() {
    if (
      !wallet ||
      wallet?.isProcessing ||
      !glpExtendContract ||
      !lock ||
      isExtending
    )
      return

    setErrorMessage(undefined)
    setIsExtending(true)

    await wallet
      .interact(glpExtendContract)
      .extend(BigNumber.from(lock.id), numberOfYears)
      .then(() => {
        setExtendAllowance(extendAllowance.sub(extensionCost))

        onExtend?.(
          lock,
          Number.parseFloat(formatEther(extensionCost)),
          Number.parseInt(numberOfYearsInput)
        )

        setLock(undefined)

        setNumberOfYearsInput('1')

        toast.success(t('extended-successfully'))
      })
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)
      })
      .finally(() => {
        setIsExtending(false)
      })
  }

  async function handleExtend() {
    requiredAllowance.gt(0) ? await increaseAllowance() : await extend()
  }

  const isOnCorrectNetwork = wallet?.chain === BSC_NETWORK

  return (
    <Modal
      open={!!lock}
      onClose={() => {
        setLock(undefined)
        setIsExtending(false)
        setErrorMessage(undefined)
        setNumberOfYearsInput('1')
      }}
    >
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{t('extend.title')}</ModalTitle>
        </ModalHeader>

        <ModalMain>
          <Text>
            {t('extend.description', {
              amount: Number.parseFloat(formatEther(singleExtensionCost)),
            })}
          </Text>

          <div>
            <TextS isBold className="text-start">
              {t('extend.number-of-years')}
            </TextS>

            <div className="w-28">
              <Input
                type="text"
                inputMode="numeric"
                inputSize="medium"
                className="bg-neutral-900 border-neutral-100 h-[54px]"
                value={numberOfYearsInput}
                onInput={onNumberOfYearsInput}
              />
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <TextS>
              {t('extend.balance', {
                amount: Number.parseFloat(formatEther(tensetBalance)),
              })}
            </TextS>

            <TextS>
              {t('extend.balance-required', {
                amount: Number.parseFloat(formatEther(extensionCost)),
              })}
            </TextS>
          </div>
        </ModalMain>

        <ModalFooter>
          <ExtendAction
            t={t}
            isOnCorrectNetwork={isOnCorrectNetwork}
            insufficientBalance={requiredBalance.gt(0)}
            handleExtend={handleExtend}
            isExtending={isExtending}
            requiredAllowance={requiredAllowance}
            requiredBalance={requiredBalance}
          />
        </ModalFooter>

        {errorMessage && <TextS className="text-red-500">{errorMessage}</TextS>}
      </ModalContent>
    </Modal>
  )
}

interface ExtendActionProps {
  t: TFunction
  isOnCorrectNetwork: boolean
  insufficientBalance: boolean
  handleExtend: () => void
  isExtending: boolean
  requiredAllowance: BigNumber
  requiredBalance: BigNumber
}

function ExtendAction({
  t,
  isOnCorrectNetwork,
  insufficientBalance,
  handleExtend,
  isExtending,
  requiredAllowance,
  requiredBalance,
}: ExtendActionProps) {
  const { wallet } = useWallet()

  const { name } = chains[BSC_NETWORK]

  if (!isOnCorrectNetwork)
    return <SwitchNetworkButton id={BSC_NETWORK} name={name} />

  if (insufficientBalance) return <PurchaseTokensModal />

  return (
    <Button
      type="button"
      disabled={isExtending || wallet?.isProcessing || requiredBalance.gt(0)}
      loading={isExtending}
      onClick={handleExtend}
    >
      {t(requiredAllowance.gt(0) ? 'extend.button-allowance' : 'extend.button')}
    </Button>
  )
}
