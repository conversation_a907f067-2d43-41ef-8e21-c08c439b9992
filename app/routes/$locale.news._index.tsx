import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { Link, useLoaderData } from '@remix-run/react'
import { useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useTranslation } from 'react-i18next'

import { H1, Post, Text } from '~/tenset-components'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { Namespace, i18next } from '~/i18n'
import { getPostImage, withCache } from '~/utils'
import { detectLocale } from '~/i18n/detect-locale'
import { LoadingIndicator } from '~/components/utils'
import { getPostsList } from '~/api/fetchers'
import type { PostDataType } from '~/api/types'
import { PostImageSize } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.NEWS)

  const title = t('meta.title')
  const description = t('meta.description')

  const strapiUrl = getServerEnvironment().STRAPI_URL

  const postsPagination = await getPostsList(strapiUrl, 1, 8)

  return withCache(
    json({
      title,
      description,
      postsPagination,
      strapiUrl,
      newsPageSize: 8,
    }),
    300
  )
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export const handle = {
  i18n: [Namespace.NEWS, Namespace.COMMON],
}

export default function News() {
  const {
    postsPagination, // Posts pagination data with first page of posts
    strapiUrl,
    newsPageSize,
  } = useLoaderData<typeof loader>()

  const { t } = useTranslation([Namespace.NEWS, Namespace.COMMON])

  const localizePathname = useLocalizePathname()

  // Posts array
  const [posts, setPosts] = useState<PostDataType[]>(postsPagination!.data)
  // Current posts page
  const [page, setPage] = useState<number>(
    postsPagination!.meta?.pagination?.page
  )

  const handleInfiniteScroll = async () => {
    const nextPage = page + 1

    if (nextPage > postsPagination!.meta.pagination.pageCount) return

    const nextPagePostsList = await getPostsList(
      strapiUrl,
      nextPage,
      newsPageSize
    )

    setPosts([...posts, ...nextPagePostsList!.data])
    setPage(nextPage)
  }

  return (
    <div className="container">
      <div className="mx-auto flex max-w-[692px] flex-col gap-8">
        <H1 isBold>{t('tenset-news')}</H1>
        {posts ? (
          <InfiniteScroll
            className="flex flex-col gap-8"
            dataLength={posts.length} //This is important field to render the next data
            next={handleInfiniteScroll}
            hasMore={page + 1 <= postsPagination!.meta.pagination.pageCount}
            loader={<LoadingIndicator />}
          >
            {posts.map(data => {
              const { id, attributes } = data

              return (
                <Link
                  key={id}
                  to={localizePathname(`/news/${attributes.slug}`)}
                >
                  <Post
                    key={id}
                    title={attributes.title}
                    image={
                      getPostImage(attributes.cover, PostImageSize.SMALL).url
                    }
                    description={
                      <div
                        dangerouslySetInnerHTML={{
                          __html: attributes.description,
                        }}
                      />
                    }
                    date={new Date(attributes.date)}
                  />
                </Link>
              )
            })}
          </InfiniteScroll>
        ) : (
          <Text>Please try later</Text>
        )}
      </div>
    </div>
  )
}
