import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'

import { scAuditsIllustration } from '~/assets/images'
import { auditingSystemCards } from '~/data/smart-contract-audits/auditing-system'
import { bigCards } from '~/data/smart-contract-audits/big-cards'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import {
  // Text,
  BigCard,
  Button,
  H2,
  Hero,
  HeroImage,
} from '~/tenset-components'
import { withCache } from '~/utils'
// import { stepperData } from '~/data/smart-contract-audits/stepper'
// import { ActionStepper } from '~/components/action-stepper'
import { BusinessDescription } from '~/components/business-description'
import { CardsWithTitle } from '~/components/cards-with-title'
import useLocalizePathname from '~/i18n/use-localize-pathname'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.BUSINESS)

  const title = t('smart-contract-audits.meta.title')
  const description = t('smart-contract-audits.meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.BUSINESS, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}
export default function SmartContractAudits() {
  const { t } = useTranslation([Namespace.BUSINESS, Namespace.COMMON])
  const localizePathname = useLocalizePathname()

  return (
    <div className="container gap-32">
      <div className="flex flex-col gap-16">
        <Hero
          title={t('smart-contract-audits.hero.title')}
          description={t('smart-contract-audits.hero.description')}
          rightContent={
            <HeroImage
              src={scAuditsIllustration}
              alt={t('smart-contract-audits.hero.title')}
            />
          }
          leftContent={
            <>
              <Button to={localizePathname('/contact')}>
                {t('smart-contract-audits.hero.action')}
              </Button>
            </>
          }
        />

        <BusinessDescription
          title={t('smart-contract-audits.description.title')}
          description={t('smart-contract-audits.description.description')}
        />
      </div>

      <CardsWithTitle
        title={t('smart-contract-audits.auditing-system.title')}
        cards={auditingSystemCards(t)}
      />

      <div className="mt-8 flex flex-col gap-8 md:mt-0 md:gap-12">
        <H2 isBold>{t('smart-contract-audits.trust-and-recognition.title')}</H2>

        {/*<div className="grid gap-2 sm:grid-cols-2 md:grid-cols-3 md:gap-4 lg:grid-cols-4">*/}
        {/*  {trustAndRecognitionData.map(({ label, data }) => (*/}
        {/*    <DataPoint label={t(label)} key={label}>*/}
        {/*      {data}*/}
        {/*    </DataPoint>*/}
        {/*  ))}*/}
        {/*</div>*/}

        <div className="flex flex-col gap-8 lg:gap-6">
          {bigCards.map(card => (
            <BigCard
              key={card.title}
              cover={
                <img src={card.image} alt={t(card.title)} className="w-full" />
              }
              title={t(card.title)}
              description={t(card.description)}
            />
          ))}
        </div>
        <div className="mt-8 md:mt-12">
          <Button
            to={localizePathname('/contact')}
            className="w-full sm:w-auto"
          >
            {t('smart-contract-audits.stepper.action')}
          </Button>
        </div>
      </div>

      {/* <div className="flex flex-col gap-12">
        <ActionStepper
          title={t('smart-contract-audits.stepper.title')}
          steps={stepperData.map(step => {
            return {
              title: t(step.title),
              timeline: t(step.timeline),
              content: <Text>{t(step.content)}</Text>,
            }
          })}
          action={{
            label: t('smart-contract-audits.stepper.action'),
            to: localizePathname('/contact'),
          }}
        />
      </div> */}
    </div>
  )
}
