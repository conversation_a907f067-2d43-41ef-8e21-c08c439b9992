import { Meta } from '@storybook/react'

import { TextS } from '../Typo/Text'
import { blankCover } from '../../assets/images/'

import { Post } from './index'

type CardType = typeof Post

export default {
  title: 'Components/Post',
  component: Post,
  args: {
    date: new Date('2022-02-12'),
    title: 'Tenset technical team demonstrates its quality',
    description: (
      <div>
        <TextS className="sm:text-base">
          Tenset technical team demonstrates its quality
        </TextS>

        <TextS className="sm:text-base">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla varius
          sollicitudin odio, sed volutpat nisl rhoncus vel. Nam quam enim,
          interdum vitae pellentesque ut, efficitur ac nunc. Fusce at dignissim
          tellus. Praesent lectus tortor, interdum vel eros sit amet, viverra
          fermentum nunc. Aenean dignissim eros nec iaculis elementum.
          Pellentesque aliquam mauris eget tempus condimentum. Quisque vulputate
          neque sit amet justo porttitor vulputate. Proin ac ultrices risus.
          Phasellus ultricies nibh vel rutrum accumsan. Praesent vestibulum
          vestibulum felis, at facilisis ante tristique vel. Vestibulum ante
          ipsum primis in faucibus orci luctus et ultrices posuere cubilia
          curae; Nullam efficitur, arcu nec luctus malesuada, urna ipsum ornare
          nisi, vel porta magna orci sit amet metus. Nulla ac ante ut est
          sodales tempus. Etiam a ultricies ex. In cursus massa et nulla
          consectetur porttitor. Nulla vel mauris vitae ex finibus pharetra a
          eget felis.
        </TextS>
      </div>
    ),
    image: blankCover,
  },
} as Meta<CardType>

export const Preview = {}
