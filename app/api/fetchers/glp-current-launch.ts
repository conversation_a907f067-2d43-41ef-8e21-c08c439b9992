import type {
  GLPCurrentLaunchResponse,
  GLPCurrentLaunchType,
} from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function getGLPCurrentLaunch(): Promise<
  GLPCurrentLaunchType | undefined
> {
  const GEMS_LIST_URL = `${getServerEnvironment().STRAPI_URL}/api/glp-current-launch`

  const currentLaunch = await fetch(`${GEMS_LIST_URL}`)
    .then<GLPCurrentLaunchResponse>(response => response.json())
    .catch(error => {
      console.warn(error)
    })

  if (
    !currentLaunch ||
    !currentLaunch.data ||
    !currentLaunch.data.attributes.glp_launch.data
  )
    return undefined

  return currentLaunch.data.attributes.glp_launch.data.attributes
}
