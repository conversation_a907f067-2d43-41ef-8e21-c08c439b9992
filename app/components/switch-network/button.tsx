import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import { Button } from '~/tenset-components'
import { useWallet } from '~/tenset-web3'

export interface SwitchNetworkButtonProps {
  label?: string
  name: string
  id: number
}

export function SwitchNetworkButton({
  label,
  name,
  id,
}: SwitchNetworkButtonProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  const { wallet } = useWallet()

  const [isWaitingForConfirmation, setIsWaitingForConfirmation] =
    useState(false)

  const switchNetwork = async () => {
    if (wallet?.isProcessing) return

    setIsWaitingForConfirmation(true)

    await wallet
      ?.switchNetwork(id)
      .catch(console.warn)
      .finally(() => setIsWaitingForConfirmation(false))
  }

  return (
    <Button
      onClick={() => switchNetwork()}
      loading={isWaitingForConfirmation}
      disabled={wallet?.isProcessing}
    >
      {label ||
        t('switch-network.button', {
          network: name,
        })}
    </Button>
  )
}
