export interface VerifiedMedia {
  id: number
  attributes: {
    name: string
    description: string
    createdAt: Date
    updatedAt: Date
    media_source: {
      data: VerifiedMediaSource
    }
    media_scopes: {
      data: VerifiedMediaScope[]
    }
  }
}

export interface VerifiedMediaSource {
  id: number
  attributes: {
    name: 'telegram' | 'email' | 'twitter' | 'linkedin'
    baseUrl: string
  }
}

export interface VerifiedMediaScope {
  id: number
  attributes: {
    name: string
    description: string
  }
}

export type VerifiedMediaScopes = VerifiedMediaScope['attributes'][]
