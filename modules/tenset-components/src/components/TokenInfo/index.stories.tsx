import { Meta } from '@storybook/react'

import { blankCover } from '../../assets/images'
import { IconName } from '../Icon'

import { TokenInfo } from '.'

type TokenInfoType = typeof TokenInfo

export default {
  title: 'Components/TokenInfo',
  component: TokenInfo,
  args: {
    name: 'Tenset',
    currency: '10SET',
    icon: IconName.Tenset,
  },
} as Meta<TokenInfoType>

export const Preview = {}

export const WithImageInsteadOfIcon = {
  args: {
    icon: undefined,
    image: blankCover,
  },
}
