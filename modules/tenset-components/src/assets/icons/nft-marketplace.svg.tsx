import { AssetProps } from './index'

export default function NftMarketplace({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M70.2187 51.6814L76.5022 55.311C76.9847 55.5943 77.2889 56.1188 77.2889 56.6747V59.3707C77.2889 59.6644 77.5197 59.8952 77.8134 59.8952C78.1071 59.8952 78.3379 59.6644 78.3379 59.3707V56.6747C78.3379 55.7411 77.8344 54.8704 77.0267 54.3984L70.7432 50.7687C70.4915 50.6219 70.1768 50.7058 70.0299 50.9576C69.883 51.2093 69.967 51.524 70.2187 51.6709V51.6814ZM111.517 110.94H91.9328V106.65C91.9328 106.356 91.702 106.126 91.4083 106.126C91.1146 106.126 90.8838 106.356 90.8838 106.65V110.94H73.7224V106.65C73.7224 106.356 73.4916 106.126 73.1979 106.126C72.9041 106.126 72.6734 106.356 72.6734 106.65V110.94H55.5119V106.65C55.5119 106.356 55.2811 106.126 54.9874 106.126C54.6937 106.126 54.4629 106.356 54.4629 106.65V110.94H37.3014V106.65C37.3014 106.356 37.0706 106.126 36.7769 106.126C36.4832 106.126 36.2524 106.356 36.2524 106.65V110.94H19.521L32.9586 90.044L40.1442 95.1423C40.2596 95.2262 40.4064 95.2577 40.5428 95.2262C40.6792 95.1947 40.805 95.1213 40.889 94.9954L53.0992 76.239L62.5086 81.6729C62.9387 81.9246 63.4003 82.0715 63.8828 82.145V99.0551L61.0086 96.1808C60.7988 95.971 60.4736 95.971 60.2638 96.1808C60.054 96.3906 60.054 96.7158 60.2638 96.9256L63.8828 100.534V100.922C63.8828 101.216 64.1136 101.447 64.4073 101.447C64.701 101.447 64.9318 101.216 64.9318 100.922V100.534L68.5403 96.9256C68.7501 96.7158 68.7501 96.3906 68.5403 96.1808C68.3305 95.971 68.0054 95.971 67.7956 96.1808L64.9213 99.0551V82.145C65.3934 82.082 65.8654 81.9246 66.2955 81.6729L79.4498 74.078C80.6247 73.4066 81.3485 72.1478 81.3485 70.7946V61.9513L88.0516 51.8807L92.5412 55.4159C92.6566 55.5103 92.814 55.5418 92.9608 55.5208C93.1077 55.4894 93.2336 55.4054 93.3175 55.2795L104.521 36.9532C104.668 36.7014 104.594 36.3867 104.342 36.2294C104.101 36.0825 103.776 36.1559 103.619 36.4077L92.7196 54.2305L88.2509 50.7058C88.1355 50.6114 87.9886 50.58 87.8418 50.6009C87.6949 50.6219 87.569 50.7058 87.4851 50.8317L81.338 60.063V55.6152C81.338 54.262 80.6142 53.0032 79.4393 52.3318L66.285 44.7369C65.8549 44.4851 65.3934 44.3383 64.9108 44.2648V27.3546L67.7851 30.2289C67.89 30.3338 68.0263 30.3863 68.1522 30.3863C68.2781 30.3863 68.4249 30.3338 68.5194 30.2289C68.7292 30.0191 68.7292 29.694 68.5194 29.4842L64.9108 25.8755V25.435C64.9108 25.1412 64.6801 24.9105 64.3863 24.9105C64.0926 24.9105 63.8618 25.1412 63.8618 25.435V25.8755L60.2428 29.4842C60.033 29.694 60.033 30.0191 60.2428 30.2289C60.4526 30.4387 60.7778 30.4387 60.9876 30.2289L63.8618 27.3546V44.2648C63.3898 44.3278 62.9177 44.4851 62.4877 44.7369L49.3333 52.3318C48.1585 53.0032 47.4347 54.262 47.4347 55.6152V70.8051C47.4347 72.1583 48.1585 73.4171 49.3333 74.0885L52.1656 75.725L40.2805 93.9779L33.0845 88.8796C32.9691 88.7957 32.8222 88.7642 32.6859 88.7957C32.5495 88.8272 32.4236 88.9111 32.3397 89.0265L19.049 109.692V93.789H23.3394C23.6331 93.789 23.8639 93.5583 23.8639 93.2645C23.8639 92.9708 23.6331 92.74 23.3394 92.74H19.049V75.5781H23.3394C23.6331 75.5781 23.8639 75.3473 23.8639 75.0536C23.8639 74.7598 23.6331 74.5291 23.3394 74.5291H19.049V57.3671H23.3394C23.6331 57.3671 23.8639 57.1363 23.8639 56.8426C23.8639 56.5489 23.6331 56.3181 23.3394 56.3181H19.049V39.1561H23.3394C23.6331 39.1561 23.8639 38.9253 23.8639 38.6316C23.8639 38.3379 23.6331 38.1071 23.3394 38.1071H19.049V19.7912L24.9024 25.6447C25.0073 25.7496 25.1436 25.8021 25.2695 25.8021C25.3954 25.8021 25.5422 25.7496 25.6366 25.6447C25.8464 25.4349 25.8464 25.1098 25.6366 24.9L18.8916 18.1548C18.8916 18.1548 18.7867 18.0708 18.7238 18.0393C18.5979 17.9869 18.4511 17.9869 18.3252 18.0393C18.1993 18.0918 18.0944 18.1967 18.042 18.3226C18.0105 18.3855 18 18.459 18 18.5219V111.486C18 111.486 18 111.517 18 111.538C18 111.58 18 111.612 18.021 111.643C18.021 111.675 18.042 111.706 18.0629 111.727C18.0839 111.759 18.0944 111.78 18.1154 111.811C18.1364 111.843 18.1573 111.864 18.1888 111.885C18.1993 111.895 18.2098 111.916 18.2308 111.927C18.2308 111.927 18.2518 111.927 18.2622 111.927C18.3357 111.969 18.4196 112 18.514 112H111.476C111.769 112 112 111.769 112 111.475C112 111.182 111.769 110.951 111.476 110.951L111.517 110.94ZM49.8788 73.1758C49.0291 72.6827 48.5046 71.7806 48.5046 70.7946V55.6047C48.5046 54.6291 49.0291 53.7165 49.8788 53.2235L63.0331 45.6286C63.4527 45.3873 63.9353 45.2614 64.4073 45.2614C64.8794 45.2614 65.3619 45.3873 65.7815 45.6286L78.9358 53.2235C79.7855 53.7165 80.31 54.6186 80.31 55.6047V70.7946C80.31 71.7701 79.7855 72.6827 78.9358 73.1758L65.7815 80.7707C64.9318 81.2638 63.8828 81.2533 63.0331 80.7707L49.8788 73.1758ZM64.4073 71.7282C64.5961 71.7282 64.764 71.6338 64.8584 71.4659L69.4739 63.6297C69.4739 63.6297 69.4739 63.5983 69.4844 63.5878C69.5159 63.5249 69.5264 63.4724 69.5369 63.399C69.5369 63.378 69.5369 63.357 69.5369 63.336C69.5369 63.2626 69.5159 63.1787 69.4844 63.1052L64.8689 55.2691C64.7745 55.1117 64.6066 55.0068 64.4178 55.0068C64.229 55.0068 64.0611 55.1012 63.9667 55.2691L59.3407 63.1052C59.2987 63.1787 59.2882 63.2521 59.2882 63.336C59.2882 63.357 59.2882 63.378 59.2882 63.399C59.2882 63.4619 59.3092 63.5249 59.3407 63.5878C59.3407 63.5983 59.3407 63.6192 59.3512 63.6297L63.9772 71.4659C64.0716 71.6233 64.2395 71.7282 64.4283 71.7282H64.4073ZM64.4073 56.5594L68.2991 63.1577L64.4073 65.2243L60.5156 63.1577L64.4073 56.5594ZM64.1556 66.2838C64.229 66.3257 64.3129 66.3467 64.3968 66.3467C64.4808 66.3467 64.5647 66.3257 64.6381 66.2838L67.6172 64.6998L64.3863 70.1756L61.1554 64.6998L64.1346 66.2838H64.1556Z"
        fill="url(#paint0_linear_2185_49)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_49"
          x1="33.032"
          y1="120.424"
          x2="87.3195"
          y2="26.3908"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
