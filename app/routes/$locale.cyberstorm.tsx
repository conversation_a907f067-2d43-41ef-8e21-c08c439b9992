import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'

import { cyberstormIllustration } from '~/assets/images'
import { BusinessDescription } from '~/components/business-description'
import { bigCards } from '~/data/cyberstorm/big-cards'
import { stepperData } from '~/data/cyberstorm/stepper'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import {
  BigCard,
  H2,
  Hero,
  HeroImage,
  Stepper,
  Text,
  TextL,
} from '~/tenset-components'
import { withCache } from '~/utils'

export const handle = {
  i18n: [Namespace.BUSINESS, Namespace.COMMON],
}

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.BUSINESS)

  const title = t('cyberstorm.meta.title')
  const description = t('cyberstorm.meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function Cyberstorm() {
  const { t } = useTranslation([Namespace.BUSINESS, Namespace.COMMON])

  return (
    <div className="container gap-32">
      <div className="flex flex-col gap-16">
        <Hero
          title={t('cyberstorm.hero.title')}
          description={t('cyberstorm.hero.description')}
          rightContent={
            <HeroImage
              src={cyberstormIllustration}
              alt={t('cyberstorm.hero.title')}
            />
          }
        />
        <BusinessDescription
          title={t('cyberstorm.description.title')}
          description={t('cyberstorm.description.description')}
        />
      </div>

      <div className="flex flex-col gap-12">
        <div className="flex max-w-[800px] flex-col gap-4">
          <H2 isBold>{t('cyberstorm.stepper.title')}</H2>

          <TextL>{t('cyberstorm.stepper.description')}</TextL>
        </div>

        <Stepper
          steps={stepperData.map(({ title, content }) => {
            return {
              title: t(title),
              content: <Text>{t(content)}</Text>,
            }
          })}
        />
      </div>

      <div className="mt-8 flex flex-col gap-8 md:mt-0 md:gap-12">
        <div className="flex flex-col gap-8 lg:gap-6">
          {bigCards.map(card => (
            <BigCard
              key={card.title}
              cover={
                <img src={card.image} alt={t(card.title)} className="w-full" />
              }
              title={t(card.title)}
              description={t(card.description)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
