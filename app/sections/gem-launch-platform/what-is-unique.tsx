import { useTranslation } from 'react-i18next'

import { whatIsUnique } from '~/data/gem-launch-platform'
import { CardsWithTitle } from '~/components/cards-with-title'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'

export function GlpWhatIsUnique() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])
  const localizePathname = useLocalizePathname()

  return (
    <CardsWithTitle
      title={t('what-is-unique.title')}
      cards={whatIsUnique(t, localizePathname)}
    />
  )
}
