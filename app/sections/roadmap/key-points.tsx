import { KeyPoint } from '~/components/roadmap/key-points'
import { H2, TextL, Text } from '~/tenset-components'

interface KeyPointData {
  label: string
  content: string
}

const keyPoints: KeyPointData[] = [
  {
    label: 'Cex listings',
    content:
      'We aim to list our token on Tier 1 and Tier 2 centralized exchanges (CEXs) to increase liquidity and market presence. By securing listings on reputable exchanges, we enhance the accessibility and credibility of our token, fostering trust among investors and expanding our user base.',
  },
  {
    label: 'Expansion of marketing',
    content:
      'To amplify our reach, we are intensifying our marketing efforts in high-potential regions, including Turkey, India, the Middle East, Africa, and Southeast Asia (SEA) markets. This strategic focus aims to tap into large, diverse populations and emerging crypto markets, driving greater adoption and engagement.',
  },
  {
    label: 'Increase incubation projects',
    content:
      'Our goal is to grow the number of projects incubated through the AlphablockZ initial investment program. By providing resources, mentorship, and funding, we support innovative startups, fostering an ecosystem of growth and collaboration.',
  },
  {
    label: 'Launching at least 24 projects',
    content:
      'We plan to launch at least 24 high-quality, closely advised, and carefully vetted projects. This initiative ensures a steady pipeline of promising ventures, contributing to the robustness and diversity of our ecosystem.',
  },
  {
    label: 'Tenset services via Telegram',
    content:
      'Could anything be simpler for new investors than the intuitive purchase of subscriptions or allocations via Telegram? Or maybe someone would like to deposit funds to Infinity? These services will be available through the Tenset bot for greater adoption and convenience.',
  },
  {
    label: 'Tenset merch',
    content:
      'We know how much true Tensetters have wanted Tenset merch. In the coming year, we plan to introduce a range of merchandise that will allow fans to proudly identify with what Tenset represents. This merch will not only celebrate our community but also increase brand visibility, showcasing the beauty and quality of our designs.',
  },
  {
    label: 'KOL management platform',
    content:
      'Streamlining KOL management through an AI-powered platform for onboarding, content control, and reward management can improve content quality, reduce administrative burdens, and expand marketing campaigns.',
  },
  {
    label: 'Tenset affiliate platform',
    content:
      'The Tenset affiliate platform aims to expand Tenset through a performance-based reward system for affiliates, leveraging smart contracts and a simple frontend for tracking and managing KOL performance, potentially creating additional income streams and providing unique insights for more effective promotions.',
  },
  {
    label: 'New market making partner',
    content: `We are partnering with a new market-making provider to enhance liquidity, stabilize our token's price, and improve the overall trading experience. This collaboration will help us maintain a healthy market environment and attract more traders and investors.`,
  },
]

export function KeyPointsRoadmap() {
  return (
    <div className="flex flex-col gap-4">
      <H2 isBold>Explore Further</H2>

      <TextL>
        Discover the reasons and strategies behind our goals and how we plan to
        achieve them.
      </TextL>

      <div className="grid gap-6 mt-4 sm:grid-cols-2 md:grid-cols-3">
        {keyPoints.map((keyPoint, index) => (
          <KeyPoint key={index} label={keyPoint.label}>
            <Text>{keyPoint.content}</Text>
          </KeyPoint>
        ))}
      </div>
    </div>
  )
}
