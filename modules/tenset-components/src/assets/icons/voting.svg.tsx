import { AssetProps } from '.'

export default function Voting({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M56.63 44.1087H45.38V55.3587H56.63V44.1087Z" fill="#15121A" />
      <path d="M56.63 61.8887H45.38V73.1387H56.63V61.8887Z" fill="#15121A" />
      <path
        d="M109.41 18.6487C109.41 18.6487 88.12 18.6487 85.74 18.6487C83.51 18.6487 76.68 27.2187 76.68 32.3387C76.68 35.8987 76.68 36.5387 76.68 36.5387H82.29C82.29 36.5387 82.79 32.9487 82.87 32.5987C83.51 29.7887 86.78 27.7887 89.57 28.4487C94.45 29.5987 93.95 36.2287 90.76 38.9187C90.76 38.9187 86.64 42.5887 84.63 44.5987C81.55 47.6787 85.63 52.1787 89.49 48.6087C94.8 43.6787 96 42.6887 98.68 40.3587C101.36 38.0287 102.7 39.2587 106.82 37.2587C110.83 35.1387 112.19 32.2187 112.19 32.2187"
        fill="#15121A"
      />
      <path
        d="M29.99 68.0187H38.8V78.8587H91.33V68.0187H100.14L116.22 89.6987V100.759H13.91V89.6987L29.99 68.0187Z"
        fill="#15121A"
      />
      <path
        d="M55.21 59.9687H43.96C43.68 59.9687 43.46 60.1887 43.46 60.4687V71.7187C43.46 71.9987 43.68 72.2187 43.96 72.2187H55.21C55.49 72.2187 55.71 71.9987 55.71 71.7187V60.4687C55.71 60.1887 55.49 59.9687 55.21 59.9687ZM54.71 71.2187H44.46V60.9687H54.71V71.2187ZM48.37 50.6187C48.46 50.7187 48.6 50.7787 48.74 50.7787C48.88 50.7787 49.01 50.7187 49.11 50.6187L52.24 47.1987C52.43 46.9987 52.41 46.6787 52.21 46.4887C52.01 46.2987 51.69 46.3187 51.5 46.5187L48.74 49.5287L47.82 48.5187C47.63 48.3187 47.32 48.2987 47.11 48.4887C46.91 48.6787 46.89 48.9887 47.08 49.1987L48.37 50.6087V50.6187ZM41.08 33.0187C41.36 33.0187 41.58 32.7987 41.58 32.5187V29.0087C41.58 28.7287 41.36 28.5087 41.08 28.5087C40.8 28.5087 40.58 28.7287 40.58 29.0087V32.5187C40.58 32.7987 40.8 33.0187 41.08 33.0187ZM47.09 68.5887C47.19 68.6887 47.32 68.7387 47.44 68.7387C47.56 68.7387 47.7 68.6887 47.79 68.5887L49.57 66.8087L51.35 68.5887C51.45 68.6887 51.58 68.7387 51.7 68.7387C51.82 68.7387 51.96 68.6887 52.05 68.5887C52.25 68.3887 52.25 68.0787 52.05 67.8787L50.27 66.0987L52.05 64.3187C52.25 64.1187 52.25 63.8087 52.05 63.6087C51.85 63.4087 51.54 63.4087 51.34 63.6087L49.56 65.3887L47.78 63.6087C47.58 63.4087 47.27 63.4087 47.07 63.6087C46.87 63.8087 46.87 64.1187 47.07 64.3187L48.85 66.0987L47.07 67.8787C46.87 68.0787 46.87 68.3887 47.07 68.5887H47.09ZM110.76 112.819C110.48 112.819 110.26 113.039 110.26 113.319V118.379C110.26 118.659 110.48 118.879 110.76 118.879C111.04 118.879 111.26 118.659 111.26 118.379V113.319C111.26 113.039 111.04 112.819 110.76 112.819ZM35.78 34.2387C35.88 34.3387 36.01 34.3887 36.13 34.3887C36.25 34.3887 36.39 34.3387 36.48 34.2387C36.68 34.0387 36.68 33.7287 36.48 33.5287L34 31.0487C33.8 30.8487 33.49 30.8487 33.29 31.0487C33.09 31.2487 33.09 31.5587 33.29 31.7587L35.77 34.2387H35.78ZM63.64 50.6287H73.21C73.49 50.6287 73.71 50.4087 73.71 50.1287C73.71 49.8487 73.49 49.6287 73.21 49.6287H63.64C63.36 49.6287 63.14 49.8487 63.14 50.1287C63.14 50.4087 63.36 50.6287 63.64 50.6287ZM55.2 42.1887H43.95C43.67 42.1887 43.45 42.4087 43.45 42.6887V53.9387C43.45 54.2187 43.67 54.4387 43.95 54.4387H55.2C55.48 54.4387 55.7 54.2187 55.7 53.9387V42.6887C55.7 42.4087 55.48 42.1887 55.2 42.1887ZM54.7 53.4387H44.45V43.1887H54.7V53.4387ZM79.59 45.9987H63.64C63.36 45.9987 63.14 46.2187 63.14 46.4987C63.14 46.7787 63.36 46.9987 63.64 46.9987H79.59C79.87 46.9987 80.09 46.7787 80.09 46.4987C80.09 46.2187 79.87 45.9987 79.59 45.9987ZM31.12 39.3487H34.63C34.91 39.3487 35.13 39.1287 35.13 38.8487C35.13 38.5687 34.91 38.3487 34.63 38.3487H31.12C30.84 38.3487 30.62 38.5687 30.62 38.8487C30.62 39.1287 30.84 39.3487 31.12 39.3487ZM116.94 33.7487L112.08 34.7487L108.01 14.8687L117.17 12.9887C117.44 12.9287 117.62 12.6687 117.56 12.3987C117.5 12.1287 117.24 11.9587 116.97 12.0087L107.32 13.9887C107.19 14.0187 107.08 14.0887 107 14.1987C106.93 14.3087 106.9 14.4487 106.93 14.5787L107.37 16.7487H84.32C81.74 16.7487 74.76 25.5487 74.76 30.9387V34.6387H41.08C38.76 34.6387 36.88 36.5287 36.88 38.8387V66.0987H28.57C28.41 66.0987 28.26 66.1787 28.17 66.2987L12.09 87.9887C12.09 87.9887 12.09 88.0087 12.08 88.0287C12.05 88.0787 12.02 88.1387 12.01 88.2087C12.01 88.2287 12 88.2487 12 88.2687C12 88.2687 12 88.2887 12 88.2987V99.3487C12 99.6287 12.22 99.8487 12.5 99.8487H16.03V113.319C16.03 113.599 16.25 113.819 16.53 113.819C16.81 113.819 17.03 113.599 17.03 113.319V99.8487H110.28V109.469C110.28 109.749 110.5 109.969 110.78 109.969C111.06 109.969 111.28 109.749 111.28 109.469V99.8487H114.81C115.09 99.8487 115.31 99.6287 115.31 99.3487V88.2987C115.31 88.2987 115.31 88.2787 115.31 88.2687C115.31 88.2487 115.31 88.2287 115.3 88.2087C115.29 88.1387 115.27 88.0787 115.23 88.0287C115.23 88.0187 115.23 87.9987 115.22 87.9887L99.14 66.2987C99.05 66.1687 98.9 66.0987 98.74 66.0987H90.43V45.7087C93.5 42.8787 94.84 41.7187 96.58 40.2087L97.61 39.3087C98.98 38.1187 99.94 37.9287 101.26 37.6787C102.36 37.4687 103.72 37.2087 105.65 36.2687C108.24 34.8987 109.77 33.2087 110.55 32.1187L111.23 35.4187C111.26 35.5487 111.33 35.6587 111.44 35.7387C111.52 35.7887 111.62 35.8187 111.72 35.8187C111.75 35.8187 111.79 35.8187 111.82 35.8087L117.17 34.7087C117.44 34.6487 117.61 34.3887 117.56 34.1187C117.5 33.8487 117.24 33.6787 116.97 33.7287L116.94 33.7487ZM37.88 38.8487C37.88 37.0787 39.32 35.6487 41.08 35.6487H86.2C87.33 35.6487 88.36 36.2487 88.94 37.1987C88.43 37.6487 84.72 40.9787 82.85 42.8487C81.11 44.5887 81.4 46.7287 82.58 47.9587C83.2 48.5987 84.1 49.0287 85.13 49.0287C86.16 49.0287 87.29 48.6087 88.4 47.5787C88.75 47.2487 89.08 46.9487 89.4 46.6487V76.9587H37.88V38.8387V38.8487ZM81.46 34.6487C81.51 34.3287 81.57 33.8987 81.65 33.3387C81.76 32.5087 81.91 31.4787 81.94 31.3187C82.21 30.1187 83.05 29.0087 84.23 28.2687C85.43 27.5187 86.82 27.2487 88.03 27.5387C90.09 28.0187 90.84 29.4887 91.12 30.6387C91.58 32.5787 90.97 34.9287 89.66 36.4787C88.88 35.3487 87.59 34.6487 86.2 34.6487H81.45H81.46ZM114.3 98.8587H12.99V88.8087H114.3V98.8587ZM98.47 67.1187L113.81 87.8087H13.48L28.82 67.1187H36.88V76.9587H33.42C32.36 76.9587 31.51 76.0987 31.51 75.0487C31.51 74.7687 31.29 74.5487 31.01 74.5487C30.73 74.5487 30.51 74.7687 30.51 75.0487C30.51 76.6587 31.82 77.9587 33.42 77.9587H93.87C95.48 77.9587 96.78 76.6487 96.78 75.0487C96.78 74.7687 96.56 74.5487 96.28 74.5487C96 74.5487 95.78 74.7687 95.78 75.0487C95.78 76.1087 94.92 76.9587 93.87 76.9587H90.41V67.1187H98.47ZM105.18 35.3987C103.39 36.2687 102.15 36.5087 101.06 36.7187C99.69 36.9787 98.51 37.2087 96.94 38.5787L95.91 39.4787C93.83 41.2787 92.33 42.5887 87.74 46.8487C85.98 48.4787 84.25 48.2387 83.32 47.2687C82.5 46.4187 82.24 44.8987 83.58 43.5587C85.55 41.5887 89.64 37.9387 89.67 37.9087C89.74 37.8487 89.8 37.7787 89.86 37.7187C89.91 37.6787 89.95 37.6387 89.99 37.5887C91.8 35.8287 92.7 32.8887 92.11 30.4087C91.63 28.3987 90.27 27.0387 88.28 26.5687C86.81 26.2187 85.14 26.5287 83.72 27.4187C82.31 28.2987 81.31 29.6387 80.98 31.0887C80.94 31.2687 80.84 32.0087 80.67 33.1887C80.6 33.6887 80.52 34.2687 80.47 34.6387H75.78V30.9387C75.78 25.9987 82.52 17.7487 84.34 17.7487H107.6L110.26 30.7287C109.94 31.3187 108.49 33.6487 105.19 35.3887L105.18 35.3987ZM79.6 63.7887H63.65C63.37 63.7887 63.15 64.0087 63.15 64.2887C63.15 64.5687 63.37 64.7887 63.65 64.7887H79.6C79.88 64.7887 80.1 64.5687 80.1 64.2887C80.1 64.0087 79.88 63.7887 79.6 63.7887ZM73.21 67.4287H63.64C63.36 67.4287 63.14 67.6487 63.14 67.9287C63.14 68.2087 63.36 68.4287 63.64 68.4287H73.21C73.49 68.4287 73.71 68.2087 73.71 67.9287C73.71 67.6487 73.49 67.4287 73.21 67.4287Z"
        fill="url(#paint0_linear_1848_54)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1848_54"
          x1="41.4"
          y1="128.249"
          x2="110.69"
          y2="8.2287"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
