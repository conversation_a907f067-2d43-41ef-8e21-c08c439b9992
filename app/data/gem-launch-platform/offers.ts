import type { BigNumber } from 'ethers'
import { parseEther } from 'ethers/lib/utils'

import { isDevelopment } from '~/environment'
import type { GlpAffiliateCode } from '~/tenset-web3'

export interface GlpAffiliateCodeWithParameter extends GlpAffiliateCode {
  parameter: string
}

export enum GlpTokenId {
  '10SET' = 1,
  // '10SETx' = 2,
}

export interface GlpSubscribeOffer {
  id: number
  price: BigNumber
}

export const glpSubscribeOffers: GlpSubscribeOffer[] = [
  {
    id: 1,
    price: parseEther('1000'),
  },
  {
    id: 2,
    price: parseEther('2500'),
  },
  {
    id: 3,
    price: parseEther('5000'),
  },
]

export interface GlpUpgradeOffer extends GlpSubscribeOffer {
  isRegularNft?: boolean
}

export const glpUpgradeOffers: GlpUpgradeOffer[] = isDevelopment
  ? [
      ...glpSubscribeOffers,
      // Tier 4 - Regular lifetime NFT
      {
        id: 4,
        price: parseEther('5000'),
        isRegularNft: true,
      },
    ]
  : glpSubscribeOffers

export const glpAffiliateCodes: GlpAffiliateCodeWithParameter[] = [
  {
    parameter: 'koszarytradingu',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x4ee6a34ec41df5adb5398ec4f83d06f737864fd3bfbfd4729ca3da54938a18b51cefb560ff22a6f667ba9c94ee2f29114cdd9bd849be8a879849dc5637d16a291b',
  },
  {
    parameter: '1000x',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x059d088b06007d7d94ef4dd78c824d3bf09c8e5d3f22b5e53d5da980e6583d1f6d974cdac811747bd733c4fa78a27cbd70f41acbc1782d8d3b75ab48c0b521821c',
  },
  {
    parameter: 'SMC25',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x764638fea9f5ae9d5a8946027b0ead98414dcc9690afe0038146ea12a91d8ab2117fd894db19122c591ce5fee679586bd5e1b2c5b3ea578bb099d879474168761c',
  },
  {
    parameter: 'QKSA',
    affiliate: '0xe11C6AAA0413512345deA2e4aa34c297c3349602',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0xd57a38fb57025d432e297925d5ecb16ca0f09065f54d4c34df409b2f2aa7af0a544cc08998b2527a725c771f83a51325549d4270ea5acd85e2af87c5a573043c1b',
  },
  {
    parameter: 'riella_tenset',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x3f071b4fb9d88eeae9b2b8a94b66bf633053a71984dd475ce1160a4f596f64db453fd5040d7123b2f926b89512a2bfecb546693869d7b34c525188f6d65e07c31b',
  },
  {
    parameter: 'CryptosSherlock',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x76bbc220687765c2fa46d4bc46b61906abedecc710b32998d88e36a3f918548a17d7b08c2170871eec8e055e8a82614d9e5c2192708f27cc746543c7d5ac913e1b',
  },
  {
    parameter: 'MAM',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x653c107f6547133f284f0fe5c566d4968cd1ef6e8c470ccfb3d9222a69a86344489c1f4b9c1d9628ba6f16f924716ef03440442b0792bed42c84319a0022c0221b',
  },
  {
    parameter: 'nftblack_tenset',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x1c5dea942260e2cd5a9de26c03e9475a79e529eb9a45e23c3148b8efc83903816992dbee0692cdf00fcfdfda491cebecfa00756ead40bc5cd87444e93e43779e1c',
  },
  {
    parameter: 'KriptoSensei',
    affiliate: '0x6469EaBd407C20EBb7BFF9B7ec734886D70E277D',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0xeec710ee37c6851f04fe2dcb8930358c9abbcd5eb960750836975fda0dce417f34cfb4ec0dc2e31ffadc857349a929d2b5568dabb9d65edb2cfcca4531f925611c',
  },
  {
    parameter: 'nuhbatuhann',
    affiliate: '0x7A15312eC9DE22316A04e150c09182914C8B361C',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0xaa0edffc2e3846dde1b9d8dae5cb782b2972a546485f1c2ff5cd523cf26428ad0745d1709b075e639daf55393c87dcf4146818839fe38c0de90a117a1253a2851c',
  },
  {
    parameter: 'mete_tenset',
    affiliate: '0xeC56f708097287cc8F545A485B02E1cB756d8131',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x51c003750ab7ed6d424a38feaecce0fe34bbe40d6df679bd28d4dba5c325de991fa81708d0b690b38f5d65a2d658df6b69f556bd0dba4dc6d532d66ea2d3f4781b',
  },
  {
    parameter: 'akincnr',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x6202591718877ed64874b0fe943af825d693609d61fdc5618ec399f03fac4bfa459e291c89369bba99cb934859d6ac49ffc9a6d2ce6321e4f45e93e06e83c9f31c',
  },
  {
    parameter: 'cryptoivanov',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0xf6c1bf8b4229ef9d234505d2a670a5b7d8c95af675a14d3c199e4f0391cd256b5de3a3dac7e0b29369a7719810804df2b01d97e89a1d2c58a634b41ffab561781c',
  },
  {
    parameter: 'CoinHunters',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x21fa1f171d76fd5ab9e3832cbaf57c35cff54e849a009383408042c30d0143324b00279059588e6c31ba5cfb53a005c4dd9dd82895752b7aebb06d9de9a4c4e71c',
  },
  {
    parameter: 'genelpatron01',
    affiliate: '******************************************',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x5b4822feb5ee0576ce56ec415066f591de47991cfc33193cac1e564a1ec31e0321b26190810cff82a2dc29347166d47c2cc7ab7f17f151ff2f389277ab704a1b1c',
  },
  {
    parameter: 'drekzenn',
    affiliate: '0x18a3AbBB6951C50dbdc25f58cc2F9EaCb3710a6D',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x9da8b7d82f5b11605be378548cc25d9b9320a5d3c41c4aa44624d469b72ead324452fb9bbfb5bf7611c0621accf68688dafdc212a2d782e9a85b72276ce20e6c1b',
  },
  {
    parameter: 'vemutlu',
    affiliate: '0x764540eEbDeAc9fD9fD1071376AB929217bB47dd',
    commissionNumerator: 5,
    commissionDenominator: 100,
    bonusNumerator: 25,
    bonusDenominator: 100,
    signature:
      '0x6e1482185c7ebceee4d60952d8240890a6bd2e8f8beb150a9c26d3ae89d690fe733c9af6c4d1254409453b26df04b59037e1499488db243c0cf45a2f4cd238b01b',
  },
]
