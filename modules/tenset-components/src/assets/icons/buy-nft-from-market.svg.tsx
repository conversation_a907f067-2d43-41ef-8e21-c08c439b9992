import { AssetProps } from '.'

export default function BuyNftFromMarket({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2010_30)">
        <path
          d="M29.0126 61.5748V75.4879L40.505 82.1246L51.9974 75.4879V61.5748L40.505 54.9381L29.0126 61.5748Z"
          fill="#15121A"
        />
        <path
          d="M124.228 60.3621L105.211 28.7778C104.412 27.4585 103.094 26.5523 101.602 26.2857C97.5275 25.5394 88.8583 23.1406 82.5594 15.6377C80.8948 13.6387 77.9918 13.2256 75.7679 14.5582L65.7936 20.5686C63.6762 21.8479 62.744 24.4067 63.4498 26.7788C64.0624 28.7512 64.382 30.8701 64.5285 33.0157H15.4295C12.3134 33.0157 9.7832 35.5478 9.7832 38.6662V112.363C9.7832 115.481 12.3134 118 15.4295 118H65.5805C68.6966 118 71.2135 115.481 71.2135 112.363V66.6656L82.5594 85.4962C84.0775 88.0283 87.3668 88.8412 89.897 87.322L122.417 67.7051C124.947 66.1725 125.759 62.8941 124.228 60.3488V60.3621ZM60.134 79.4859L40.505 90.8402L20.8894 79.4859V56.8039L40.505 45.4628L60.134 56.8039V79.4859ZM78.8041 32.1095C76.5269 33.4955 73.5706 32.7492 72.1856 30.4703C70.814 28.1781 71.5597 25.2196 73.8369 23.8336C76.1141 22.461 79.0837 23.2073 80.4554 25.4861C81.827 27.765 81.0946 30.7368 78.8041 32.1095Z"
          fill="#15121A"
        />
        <path
          d="M59.8676 99.6627H47.6162C47.2433 99.6627 46.9503 99.9559 46.9503 100.329C46.9503 100.702 47.2433 100.995 47.6162 100.995H59.8676C60.2405 100.995 60.5335 100.702 60.5335 100.329C60.5335 99.9559 60.2405 99.6627 59.8676 99.6627ZM100.178 54.2187C98.6195 51.5267 94.4114 51.8732 91.1487 53.3391C88.9382 54.312 86.1949 54.6851 85.1162 52.8194C84.1574 51.1669 85.2894 48.7147 87.6198 47.3688C89.311 46.3959 91.2553 46.2227 92.5736 46.9423C92.8932 47.1155 93.3061 47.0089 93.4792 46.6758C93.6523 46.3559 93.5324 45.9428 93.2128 45.7695C92.2673 45.2498 91.0955 45.0766 89.8837 45.2232L88.765 43.2908C88.5786 42.9709 88.1791 42.8643 87.8595 43.0509C87.5399 43.2375 87.4334 43.6373 87.6198 43.9571L88.512 45.5163C87.9793 45.6896 87.4467 45.9161 86.9406 46.2093C86.661 46.3692 86.408 46.5558 86.1549 46.7424L85.316 45.2765C85.1296 44.9566 84.7301 44.85 84.4105 45.0366C84.0909 45.2232 83.9843 45.623 84.1708 45.9428L85.1562 47.6353C83.5049 49.3944 82.9456 51.7133 83.9577 53.4724C85.2228 55.6713 88.1791 56.0844 91.708 54.5252C94.5179 53.2592 97.927 52.966 99.0323 54.8584C100.124 56.7507 98.6328 59.656 95.7164 61.3351C93.6923 62.5079 91.3618 62.8144 89.7904 62.1214C89.4575 61.9748 89.058 62.1214 88.9115 62.4679C88.765 62.8011 88.9115 63.2009 89.2578 63.3475C89.9902 63.6673 90.8291 63.8272 91.7214 63.8272C92.547 63.8272 93.3993 63.6806 94.2649 63.4141L95.1971 65.0399C95.3169 65.2532 95.5433 65.3731 95.7697 65.3731C95.8762 65.3731 95.9961 65.3465 96.1026 65.2798C96.4222 65.0933 96.5287 64.6935 96.3423 64.3736L95.5033 62.9343C95.7963 62.8011 96.0893 62.6678 96.3689 62.4945C96.875 62.2014 97.3277 61.8682 97.7539 61.5217L98.6328 63.0543C98.7526 63.2675 98.979 63.3874 99.2054 63.3874C99.3119 63.3874 99.4318 63.3608 99.5383 63.2942C99.8579 63.1076 99.9644 62.7078 99.778 62.3879L98.7393 60.5888C100.577 58.5765 101.23 56.0578 100.164 54.192L100.178 54.2187ZM122.67 57.8968L103.653 26.3126C102.761 24.8333 101.283 23.8072 99.5916 23.5007C95.2237 22.701 86.9939 20.2889 80.9481 13.0792C79.0971 10.8669 75.8078 10.3605 73.3043 11.8664L63.33 17.8768C60.9729 19.3027 59.8943 22.168 60.7066 24.8466C60.7732 25.0465 60.8264 25.2731 60.8797 25.4863C59.4548 26.2593 57.8435 27.9651 56.1389 29.7375L55.6728 30.2306H13.9647C10.1294 30.2306 7 33.3491 7 37.2005V109.578C7 113.416 10.1161 116.548 13.9647 116.548H62.8106C66.6459 116.548 69.7753 113.429 69.7753 109.578V66.9457L79.8694 83.7106C80.6951 85.0833 82.0134 86.0561 83.5715 86.4426C84.0509 86.5626 84.5436 86.6159 85.0363 86.6159C86.115 86.6159 87.1803 86.3227 88.1258 85.7496L120.632 66.1461C122.004 65.3198 122.976 64.0005 123.362 62.4412C123.748 60.882 123.509 59.2562 122.67 57.8835V57.8968ZM61.1993 26.8456C61.4124 27.9118 61.5722 29.0312 61.6787 30.2306H57.5106C58.8156 28.858 60.1206 27.5386 61.1993 26.8456ZM68.4303 109.578C68.4303 112.683 65.9001 115.215 62.7973 115.215H13.9647C10.8619 115.215 8.33168 112.683 8.33168 109.578V37.2005C8.33168 34.0954 10.8619 31.5633 13.9647 31.5633H55.5264C55.6329 31.6299 55.7394 31.6699 55.8593 31.6699C55.9791 31.6699 56.0857 31.6299 56.1922 31.5633H62.7973C64.3021 31.5633 65.6737 32.163 66.6858 33.1358C62.5043 36.2143 57.2309 38.6398 53.8351 38.0934C53.4889 38.0401 53.1293 38.2799 53.0761 38.6531C53.0228 39.0129 53.2625 39.3594 53.6354 39.4127C54.0216 39.466 54.4211 39.506 54.8472 39.506C58.6025 39.506 63.5564 37.1472 67.5381 34.1753C68.0974 35.0549 68.4303 36.081 68.4303 37.1872V109.564V109.578ZM119.94 65.0133L87.4334 84.6169C85.2095 85.9495 82.333 85.2299 81.0014 83.0177L69.762 64.347V37.1872C69.762 35.7745 69.3359 34.4552 68.6034 33.3491C69.6954 32.4562 70.6808 31.5366 71.5331 30.6171C71.9593 30.857 72.3987 31.0569 72.8781 31.1635C73.3176 31.2701 73.7703 31.3234 74.2098 31.3234C75.1952 31.3234 76.1674 31.0569 77.033 30.5371C79.6297 28.9779 80.4687 25.5929 78.8973 22.9942C77.3392 20.3955 73.9568 19.5559 71.36 21.1285C70.9871 21.3551 70.6542 21.6349 70.3479 21.9414C70.3213 21.9681 70.2947 21.9814 70.2813 22.0081C69.6022 22.701 69.1095 23.5406 68.8698 24.5135C68.5102 25.9394 68.7366 27.4187 69.4957 28.6714C69.762 29.1112 70.0949 29.4977 70.4678 29.8308C69.6821 30.6571 68.7632 31.4967 67.7512 32.2963C66.5393 31.0569 64.8614 30.2973 63.0237 30.2306C62.8905 28.5781 62.6508 27.0322 62.3046 25.6063C62.3046 25.6063 62.3046 25.5929 62.3046 25.5796C62.2114 25.1931 62.1048 24.8067 61.9983 24.4335C61.3591 22.3412 62.1981 20.1023 64.0358 18.9962L74.01 12.9859C75.9676 11.8131 78.5111 12.2129 79.9493 13.9188C86.2748 21.4483 94.8375 23.9671 99.3785 24.7933C100.697 25.0332 101.842 25.8328 102.535 26.9789L121.551 58.5632C122.883 60.7754 122.164 63.6673 119.953 65L119.94 65.0133ZM72.2922 22.1547C72.8914 21.8481 73.5306 21.6749 74.1965 21.6749C74.5294 21.6749 74.8756 21.7149 75.2086 21.7948C76.2872 22.0614 77.1928 22.741 77.7654 23.6872C78.9506 25.6596 78.3114 28.2183 76.3538 29.4044C75.4083 29.9774 74.2897 30.1507 73.211 29.8841C72.9314 29.8175 72.6784 29.7242 72.4253 29.6043C73.4774 28.2983 74.1965 27.0189 74.4095 25.8995C74.6093 24.8466 74.5294 23.1142 72.3055 22.1547H72.2922ZM73.0779 25.6596C72.9047 26.6058 72.2655 27.7119 71.3334 28.858C71.067 28.6048 70.8273 28.3249 70.6276 28.0051C70.055 27.0589 69.8818 25.9394 70.1482 24.86C70.308 24.2203 70.6276 23.6472 71.0404 23.1542C73.1311 23.7272 73.211 24.8466 73.0646 25.6596H73.0779ZM58.6691 77.3671V54.6851V54.6585C58.6691 54.5518 58.6425 54.4452 58.5892 54.3519C58.5359 54.2587 58.456 54.192 58.3628 54.1254C58.3628 54.1254 58.3628 54.1121 58.3362 54.0987L38.7073 42.7577C38.5075 42.6378 38.2412 42.6378 38.0414 42.7577L18.4125 54.0987C18.4125 54.0987 18.4125 54.1121 18.3992 54.1121C18.3059 54.1654 18.226 54.232 18.1728 54.3386C18.1195 54.4319 18.0929 54.5385 18.0929 54.6451V54.6718V77.3538C18.0929 77.5937 18.226 77.8069 18.4258 77.9269L38.0547 89.2679H38.0814C38.1746 89.3212 38.2678 89.3478 38.3877 89.3478C38.5075 89.3478 38.6007 89.3078 38.6939 89.2679H38.7206L58.3495 77.9269C58.5493 77.8069 58.6824 77.5937 58.6824 77.3538L58.6691 77.3671ZM38.3743 44.117L56.6716 54.6851L49.8268 58.6565L38.7073 52.233C38.7073 52.233 38.654 52.2197 38.6274 52.2063C38.5874 52.193 38.5341 52.1664 38.4942 52.1664C38.4542 52.1664 38.4143 52.1664 38.3743 52.1664C38.3344 52.1664 38.2811 52.1664 38.2412 52.1664C38.2012 52.1664 38.1613 52.193 38.1213 52.2063C38.0947 52.2063 38.0681 52.2063 38.0414 52.233L26.9219 58.6565L20.0771 54.6851L38.3743 44.117ZM37.7085 87.5487L19.4112 76.9807V55.8445L26.2161 59.8026V73.3691C26.2161 73.3691 26.2161 73.4224 26.2294 73.4491C26.2294 73.4891 26.2294 73.5424 26.2561 73.5824C26.2694 73.6223 26.296 73.6623 26.3093 73.7023C26.336 73.7423 26.3493 73.7689 26.3759 73.8089C26.4026 73.8489 26.4425 73.8755 26.4825 73.9022C26.5091 73.9155 26.5224 73.9422 26.5357 73.9555L37.6952 80.4056V87.5754L37.7085 87.5487ZM37.7085 78.8464L27.5478 72.9693V60.5622L37.7085 66.4659V78.8331V78.8464ZM38.3743 65.3198L28.2403 59.4294L38.3743 53.579L48.5084 59.4294L38.3743 65.3198ZM49.2009 60.5622V72.9693L39.0402 78.8464V66.4792L49.2009 60.5755V60.5622ZM57.3374 76.9807L39.0402 87.5487V80.379L50.1996 73.9288C50.1996 73.9288 50.2396 73.8889 50.2529 73.8755C50.2929 73.8489 50.3328 73.8222 50.3594 73.7823C50.3861 73.7556 50.4127 73.7156 50.426 73.6756C50.4527 73.6357 50.466 73.609 50.4793 73.5557C50.4926 73.5157 50.5059 73.4624 50.5059 73.4224C50.5059 73.3958 50.5192 73.3691 50.5192 73.3425V59.7759L57.3241 55.8179V76.954L57.3374 76.9807Z"
          fill="url(#paint0_linear_2010_30)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2010_30"
          x1="22.7404"
          y1="122.691"
          x2="85.1192"
          y2="14.7319"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2010_30">
          <rect
            width="118"
            height="107"
            fill="white"
            transform="translate(7 11)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
