import { AssetProps } from '.'

export default function Tiers({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2010_40)">
        <path
          d="M68.3335 22.9156L55.254 45.8338C54.5613 47.0533 55.4342 48.5672 56.8197 48.5672H82.9786C84.3641 48.5672 85.237 47.0393 84.5442 45.8338L71.4648 22.9156C70.772 21.6961 69.0263 21.6961 68.3335 22.9156Z"
          fill="#15121A"
        />
        <path
          d="M87.2044 52.3238H52.5937C51.9425 52.3238 51.3467 52.6742 51.0281 53.2349L37.9763 76.1111C37.2836 77.3306 38.1564 78.8444 39.542 78.8444H100.256C101.642 78.8444 102.515 77.3166 101.822 76.1111L88.77 53.2349C88.4514 52.6742 87.8556 52.3238 87.2044 52.3238Z"
          fill="#15121A"
        />
        <path
          d="M104.15 82.587H98.4689V103.361L91.5412 99.9544L84.6135 103.361V82.587H35.6487C34.8035 82.587 34.0276 83.0496 33.5981 83.7785L20.1307 107.398C19.2163 108.996 20.3524 110.986 22.1813 110.986H117.645C119.46 110.986 120.61 108.996 119.695 107.398L106.214 83.7785C105.798 83.0356 105.009 82.587 104.163 82.587H104.15Z"
          fill="#15121A"
        />
        <path
          d="M119.127 105.982L105.466 82.0544C104.953 81.1433 103.983 80.5826 102.958 80.5826H34.2633C33.2241 80.5826 32.2681 81.1433 31.7555 82.0544L18.108 105.982C17.5815 106.907 17.5815 108 18.108 108.911C18.6345 109.823 19.5766 110.383 20.6158 110.383H116.606C117.659 110.383 118.601 109.837 119.113 108.911C119.64 107.986 119.64 106.893 119.113 105.968L119.127 105.982ZM84.004 81.9983H96.4877V100.964L90.5437 98.0481C90.3497 97.9499 90.1281 97.9499 89.9341 98.0481L83.9901 100.964V81.9983H84.004ZM117.922 108.225C117.645 108.701 117.16 108.996 116.606 108.996H20.6158C20.0616 108.996 19.5766 108.715 19.2995 108.225C19.0224 107.748 19.0224 107.173 19.2995 106.697L32.947 82.7693C33.2241 82.2927 33.7229 81.9983 34.2633 81.9983H82.6184V102.085C82.6184 102.323 82.7431 102.548 82.9371 102.674C83.145 102.8 83.3944 102.814 83.6022 102.716L90.2389 99.4498L96.8756 102.716C96.9726 102.758 97.0696 102.786 97.1804 102.786C97.3051 102.786 97.4298 102.744 97.5545 102.674C97.7623 102.548 97.8732 102.323 97.8732 102.085V81.9983H102.944C103.485 81.9983 103.983 82.2927 104.26 82.7693L117.922 106.697C118.199 107.173 118.199 107.748 117.922 108.239V108.225ZM15.7248 87.5772C15.3923 87.3809 14.9767 87.4931 14.7827 87.8295L11.0972 94.2914C10.9032 94.6278 11.014 95.0484 11.3466 95.2446C11.4574 95.3147 11.5821 95.3427 11.6929 95.3427C11.9285 95.3427 12.164 95.2166 12.2887 94.9923L15.9742 88.5444C16.1682 88.2079 16.0574 87.7874 15.7248 87.5912V87.5772ZM93.0238 95.7492C93.2732 95.9315 93.5919 95.9315 93.8413 95.7492C94.0768 95.567 94.1877 95.2586 94.0907 94.9643L93.0377 91.6702L95.8087 89.6377C96.0443 89.4555 96.1551 89.1471 96.0581 88.8527C95.9611 88.5584 95.6979 88.3621 95.3931 88.3621H91.9708L90.9178 85.0681C90.8208 84.7737 90.5576 84.5775 90.2527 84.5775C89.9479 84.5775 89.6847 84.7737 89.5877 85.0681L88.5347 88.3621H85.1124C84.8076 88.3621 84.5443 88.5584 84.4474 88.8527C84.3504 89.1471 84.4474 89.4555 84.6967 89.6377L87.4678 91.6702L86.4148 94.9643C86.3178 95.2586 86.4148 95.567 86.6642 95.7492C86.7889 95.8333 86.9275 95.8894 87.066 95.8894C87.2046 95.8894 87.357 95.8474 87.4678 95.7492L90.2389 93.7167L93.01 95.7492H93.0238ZM88.3823 93.3523L88.9365 91.6282C89.0335 91.3338 88.9365 91.0254 88.6871 90.8432L87.2323 89.7779H89.0335C89.3383 89.7779 89.6015 89.5816 89.6985 89.2873L90.2527 87.5632L90.807 89.2873C90.9039 89.5816 91.1672 89.7779 91.472 89.7779H93.2732L91.8184 90.8432C91.5829 91.0254 91.472 91.3338 91.569 91.6282L92.1232 93.3523L90.6684 92.287C90.419 92.1047 90.1003 92.1047 89.8509 92.287L88.3961 93.3523H88.3823ZM42.5904 32.6015L46.8578 31.4521C46.8578 31.4521 46.9271 31.4521 46.9686 31.4521L46.262 32.6996L44.7656 35.3209L44.1422 36.4142C43.9482 36.7506 44.059 37.1711 44.3916 37.3674C44.5024 37.4375 44.6271 37.4655 44.7379 37.4655C44.9735 37.4655 45.209 37.3393 45.3337 37.1151L45.5415 36.7506L45.9572 36.0357L48.1602 32.167C48.1602 32.167 48.2018 32.2371 48.2156 32.2651L49.3518 36.5824C49.4349 36.8908 49.712 37.101 50.0168 37.101C50.0722 37.101 50.1415 37.101 50.1969 37.073C50.571 36.9749 50.7789 36.5824 50.6819 36.218L49.5457 31.9006C49.4349 31.4801 49.2132 31.1157 48.9222 30.8213L49.0054 30.6811C49.1994 30.3447 49.0885 29.9242 48.756 29.728C48.4235 29.5317 48.0078 29.6439 47.8138 29.9803L47.7307 30.1205C47.3289 30.0223 46.9132 30.0083 46.4837 30.1205L42.2163 31.2699C41.8422 31.368 41.6343 31.7605 41.7313 32.1249C41.8283 32.5034 42.2163 32.7136 42.5765 32.6155L42.5904 32.6015ZM55.5312 47.9784H81.6901C82.5907 47.9784 83.4082 47.5018 83.8516 46.7169C84.3088 45.9319 84.3088 44.9787 83.8516 44.1938L70.7721 21.2616C70.3149 20.4766 69.5113 20 68.6107 20C67.7101 20 66.9065 20.4766 66.4493 21.2616L53.3698 44.1798C52.9126 44.9647 52.9126 45.9179 53.3698 46.7029C53.827 47.5018 54.6307 47.9644 55.5312 47.9644V47.9784ZM54.5614 44.8806L67.6547 21.9624C67.8625 21.612 68.2227 21.4017 68.6246 21.4017C69.0264 21.4017 69.3866 21.612 69.5944 21.9624L82.6739 44.8806C82.8678 45.2311 82.8678 45.6516 82.6739 46.002C82.466 46.3524 82.1058 46.5627 81.704 46.5627H55.5451C55.1433 46.5627 54.7831 46.3524 54.5752 46.002C54.3674 45.6516 54.3813 45.2311 54.5752 44.8806H54.5614ZM36.1199 51.8192C36.2308 51.8892 36.3554 51.9173 36.4663 51.9173C36.7018 51.9173 36.9374 51.7911 37.0621 51.5669L40.7476 45.1189C40.9416 44.7825 40.8307 44.362 40.4982 44.1657C40.1657 43.9695 39.75 44.0816 39.556 44.4181L35.8705 50.866C35.6765 51.2024 35.7874 51.6229 36.1199 51.8192ZM38.2536 78.2557H98.9678C99.8684 78.2557 100.686 77.7791 101.129 76.9941C101.573 76.2092 101.586 75.256 101.129 74.471L88.0775 51.5949C87.6341 50.8099 86.8028 50.3333 85.916 50.3333H51.3054C50.4186 50.3333 49.5873 50.8239 49.1439 51.5949L36.0922 74.471C35.635 75.256 35.635 76.2092 36.0922 76.9941C36.5494 77.7931 37.353 78.2557 38.2536 78.2557ZM37.2838 75.1719L50.3355 52.2957C50.5295 51.9453 50.9036 51.7351 51.3054 51.7351H85.916C86.3178 51.7351 86.6781 51.9453 86.8859 52.2957L99.9376 75.1719C100.132 75.5223 100.132 75.9429 99.9376 76.2933C99.7298 76.6437 99.3696 76.854 98.9678 76.854H38.2536C37.8518 76.854 37.4916 76.6437 37.2838 76.2933C37.0898 75.9429 37.0898 75.5223 37.2838 75.1719ZM27.8621 66.299C27.973 66.3691 28.0977 66.3971 28.2085 66.3971C28.444 66.3971 28.6796 66.2709 28.8043 66.0467L32.4898 59.5987C32.6838 59.2623 32.5729 58.8418 32.2404 58.6456C31.9079 58.4493 31.4784 58.5615 31.2983 58.8979L27.6127 65.3458C27.4188 65.6822 27.5296 66.1027 27.8621 66.299ZM19.6043 80.7648C19.7152 80.8349 19.8399 80.8629 19.9507 80.8629C20.1863 80.8629 20.4218 80.7367 20.5465 80.5125L24.232 74.0645C24.426 73.7281 24.3152 73.3076 23.9826 73.1114C23.6501 72.9151 23.2344 73.0273 23.0405 73.3637L19.3549 79.8116C19.161 80.148 19.2718 80.5685 19.6043 80.7648Z"
          fill="url(#paint0_linear_2010_40)"
        />
        <path
          d="M119.127 105.982L105.466 82.0544C104.953 81.1433 103.983 80.5826 102.958 80.5826H34.2633C33.2241 80.5826 32.2681 81.1433 31.7555 82.0544L18.108 105.982C17.5815 106.907 17.5815 108 18.108 108.911C18.6345 109.823 19.5766 110.383 20.6158 110.383H116.606C117.659 110.383 118.601 109.837 119.113 108.911C119.64 107.986 119.64 106.893 119.113 105.968L119.127 105.982ZM84.004 81.9983H96.4877V100.964L90.5437 98.0481C90.3497 97.9499 90.1281 97.9499 89.9341 98.0481L83.9901 100.964V81.9983H84.004ZM117.922 108.225C117.645 108.701 117.16 108.996 116.606 108.996H20.6158C20.0616 108.996 19.5766 108.715 19.2995 108.225C19.0224 107.748 19.0224 107.173 19.2995 106.697L32.947 82.7693C33.2241 82.2927 33.7229 81.9983 34.2633 81.9983H82.6184V102.085C82.6184 102.323 82.7431 102.548 82.9371 102.674C83.145 102.8 83.3944 102.814 83.6022 102.716L90.2389 99.4498L96.8756 102.716C96.9726 102.758 97.0696 102.786 97.1804 102.786C97.3051 102.786 97.4298 102.744 97.5545 102.674C97.7623 102.548 97.8732 102.323 97.8732 102.085V81.9983H102.944C103.485 81.9983 103.983 82.2927 104.26 82.7693L117.922 106.697C118.199 107.173 118.199 107.748 117.922 108.239V108.225ZM15.7248 87.5772C15.3923 87.3809 14.9767 87.4931 14.7827 87.8295L11.0972 94.2914C10.9032 94.6278 11.014 95.0484 11.3466 95.2446C11.4574 95.3147 11.5821 95.3427 11.6929 95.3427C11.9285 95.3427 12.164 95.2166 12.2887 94.9923L15.9742 88.5444C16.1682 88.2079 16.0574 87.7874 15.7248 87.5912V87.5772ZM93.0238 95.7492C93.2732 95.9315 93.5919 95.9315 93.8413 95.7492C94.0768 95.567 94.1877 95.2586 94.0907 94.9643L93.0377 91.6702L95.8087 89.6377C96.0443 89.4555 96.1551 89.1471 96.0581 88.8527C95.9611 88.5584 95.6979 88.3621 95.3931 88.3621H91.9708L90.9178 85.0681C90.8208 84.7737 90.5576 84.5775 90.2527 84.5775C89.9479 84.5775 89.6847 84.7737 89.5877 85.0681L88.5347 88.3621H85.1124C84.8076 88.3621 84.5443 88.5584 84.4474 88.8527C84.3504 89.1471 84.4474 89.4555 84.6967 89.6377L87.4678 91.6702L86.4148 94.9643C86.3178 95.2586 86.4148 95.567 86.6642 95.7492C86.7889 95.8333 86.9275 95.8894 87.066 95.8894C87.2046 95.8894 87.357 95.8474 87.4678 95.7492L90.2389 93.7167L93.01 95.7492H93.0238ZM88.3823 93.3523L88.9365 91.6282C89.0335 91.3338 88.9365 91.0254 88.6871 90.8432L87.2323 89.7779H89.0335C89.3383 89.7779 89.6015 89.5816 89.6985 89.2873L90.2527 87.5632L90.807 89.2873C90.9039 89.5816 91.1672 89.7779 91.472 89.7779H93.2732L91.8184 90.8432C91.5829 91.0254 91.472 91.3338 91.569 91.6282L92.1232 93.3523L90.6684 92.287C90.419 92.1047 90.1003 92.1047 89.8509 92.287L88.3961 93.3523H88.3823ZM42.5904 32.6015L46.8578 31.4521C46.8578 31.4521 46.9271 31.4521 46.9686 31.4521L46.262 32.6996L44.7656 35.3209L44.1422 36.4142C43.9482 36.7506 44.059 37.1711 44.3916 37.3674C44.5024 37.4375 44.6271 37.4655 44.7379 37.4655C44.9735 37.4655 45.209 37.3393 45.3337 37.1151L45.5415 36.7506L45.9572 36.0357L48.1602 32.167C48.1602 32.167 48.2018 32.2371 48.2156 32.2651L49.3518 36.5824C49.4349 36.8908 49.712 37.101 50.0168 37.101C50.0722 37.101 50.1415 37.101 50.1969 37.073C50.571 36.9749 50.7789 36.5824 50.6819 36.218L49.5457 31.9006C49.4349 31.4801 49.2132 31.1157 48.9222 30.8213L49.0054 30.6811C49.1994 30.3447 49.0885 29.9242 48.756 29.728C48.4235 29.5317 48.0078 29.6439 47.8138 29.9803L47.7307 30.1205C47.3289 30.0223 46.9132 30.0083 46.4837 30.1205L42.2163 31.2699C41.8422 31.368 41.6343 31.7605 41.7313 32.1249C41.8283 32.5034 42.2163 32.7136 42.5765 32.6155L42.5904 32.6015ZM55.5312 47.9784H81.6901C82.5907 47.9784 83.4082 47.5018 83.8516 46.7169C84.3088 45.9319 84.3088 44.9787 83.8516 44.1938L70.7721 21.2616C70.3149 20.4766 69.5113 20 68.6107 20C67.7101 20 66.9065 20.4766 66.4493 21.2616L53.3698 44.1798C52.9126 44.9647 52.9126 45.9179 53.3698 46.7029C53.827 47.5018 54.6307 47.9644 55.5312 47.9644V47.9784ZM54.5614 44.8806L67.6547 21.9624C67.8625 21.612 68.2227 21.4017 68.6246 21.4017C69.0264 21.4017 69.3866 21.612 69.5944 21.9624L82.6739 44.8806C82.8678 45.2311 82.8678 45.6516 82.6739 46.002C82.466 46.3524 82.1058 46.5627 81.704 46.5627H55.5451C55.1433 46.5627 54.7831 46.3524 54.5752 46.002C54.3674 45.6516 54.3813 45.2311 54.5752 44.8806H54.5614ZM36.1199 51.8192C36.2308 51.8892 36.3554 51.9173 36.4663 51.9173C36.7018 51.9173 36.9374 51.7911 37.0621 51.5669L40.7476 45.1189C40.9416 44.7825 40.8307 44.362 40.4982 44.1657C40.1657 43.9695 39.75 44.0816 39.556 44.4181L35.8705 50.866C35.6765 51.2024 35.7874 51.6229 36.1199 51.8192ZM38.2536 78.2557H98.9678C99.8684 78.2557 100.686 77.7791 101.129 76.9941C101.573 76.2092 101.586 75.256 101.129 74.471L88.0775 51.5949C87.6341 50.8099 86.8028 50.3333 85.916 50.3333H51.3054C50.4186 50.3333 49.5873 50.8239 49.1439 51.5949L36.0922 74.471C35.635 75.256 35.635 76.2092 36.0922 76.9941C36.5494 77.7931 37.353 78.2557 38.2536 78.2557ZM37.2838 75.1719L50.3355 52.2957C50.5295 51.9453 50.9036 51.7351 51.3054 51.7351H85.916C86.3178 51.7351 86.6781 51.9453 86.8859 52.2957L99.9376 75.1719C100.132 75.5223 100.132 75.9429 99.9376 76.2933C99.7298 76.6437 99.3696 76.854 98.9678 76.854H38.2536C37.8518 76.854 37.4916 76.6437 37.2838 76.2933C37.0898 75.9429 37.0898 75.5223 37.2838 75.1719ZM27.8621 66.299C27.973 66.3691 28.0977 66.3971 28.2085 66.3971C28.444 66.3971 28.6796 66.2709 28.8043 66.0467L32.4898 59.5987C32.6838 59.2623 32.5729 58.8418 32.2404 58.6456C31.9079 58.4493 31.4784 58.5615 31.2983 58.8979L27.6127 65.3458C27.4188 65.6822 27.5296 66.1027 27.8621 66.299ZM19.6043 80.7648C19.7152 80.8349 19.8399 80.8629 19.9507 80.8629C20.1863 80.8629 20.4218 80.7367 20.5465 80.5125L24.232 74.0645C24.426 73.7281 24.3152 73.3076 23.9826 73.1114C23.6501 72.9151 23.2344 73.0273 23.0405 73.3637L19.3549 79.8116C19.161 80.148 19.2718 80.5685 19.6043 80.7648Z"
          fill="url(#paint1_linear_2010_40)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2010_40"
          x1="48.1186"
          y1="126.924"
          x2="100.071"
          y2="37.9742"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2010_40"
          x1="48.1186"
          y1="126.924"
          x2="100.071"
          y2="37.9742"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2010_40">
          <rect
            width="109"
            height="91"
            fill="white"
            transform="translate(11 20)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
