import type { PostDataType, PostsPaginationType } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

interface FetchPost {
  slug: string
}

export async function fetchPost({
  slug,
}: FetchPost): Promise<PostDataType | undefined> {
  const POST_URL = `${getServerEnvironment().STRAPI_URL}/api/posts`

  const parameters = decodeURIComponent(
    new URLSearchParams({
      populate: 'cover',
      'filters[slug][$eq]': slug || '',
    }).toString()
  )

  return await fetch(`${POST_URL}?${parameters}`)
    .then<PostsPaginationType>(response => response.json())
    .then(({ data }) => data[0])
    .catch(() => {
      return undefined
    })
}
