import { Meta } from '@storybook/react'

import { blankCover } from '../../assets/images/'

import { Image } from './index'

export default {
  title: 'Components/Image',
  component: Image,
} as Meta<typeof Image>

export const Default = {
  args: {
    src: blankCover,
    width: '100px',
  },
}

export const Ratio = {
  args: {
    src: blankCover,
    ratio: 16 / 9,
    width: '150px',
  },
}

export const Circle = {
  args: {
    src: blankCover,
    isCircle: true,
    width: '100px',
  },
}
