import type { TFunction } from 'i18next'
import { useTranslation } from 'react-i18next'

import type { GlpNft } from '~/api/types'
import { BSC_NETWORK, MAINNET_NETWORK } from '~/config'
import { tiersPrivileges } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import {
  Button,
  ButtonVariant,
  H3,
  Icon,
  IconName,
  Image,
  Table,
  TextNumeric,
  TextS,
  TextSNumeric,
  Video,
} from '~/tenset-components'

const getOpenSeaLink = ({ contractAddress, id, chainId }: GlpNft) => {
  const chainName =
    {
      [MAINNET_NETWORK]: 'ethereum',
      [BSC_NETWORK]: 'bsc',
    }[chainId] ?? 'ethereum'

  return `https://opensea.io/assets/${chainName}/${contractAddress}/${id}`
}

function adaptNft(nft: GlpNft, t: TFunction) {
  const openSeaLink = getOpenSeaLink(nft)

  const tier = ['TGLP Genesis', 'TGLP Sumeragi'].includes(nft.collection)
    ? 5
    : 4

  const privileges = tiersPrivileges(t)[tier - 1]

  return {
    level: t('gem-launch-platform:tiers-privileges.tier', { tier }),
    image: nft.isImageVideo ? (
      <div className="w-[96px] aspect-square rounded-md overflow-hidden">
        <Video
          src={nft.image}
          autoPlay
          muted
          loop
          playsInline
          controls={false}
        />
      </div>
    ) : (
      <Image src={nft.image} alt={nft.tokenName} ratio={1} width="96px" />
    ),
    collection: nft.collection,
    poolWeight: `${privileges.poolWeight}x`,
    claimBack: privileges.claimBack ? (
      <Icon name={IconName.Success} color="#ffffff" />
    ) : (
      ''
    ),
    tradableVesting: privileges.tradableVesting ? (
      <Icon name={IconName.Success} color="#ffffff" />
    ) : (
      ''
    ),
    goToButton: (
      <div className="flex items-center justify-end">
        <Button to={openSeaLink} variant={ButtonVariant.Secondary}>
          {t('nfts-table.actions.go-to-nft')}
          <Icon name={IconName.LinkOut16} />
        </Button>
      </div>
    ),
  }
}

interface TglpNftsTableProps {
  nfts: GlpNft[]
}

export function TglpNftsTable({ nfts }: TglpNftsTableProps) {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
    Namespace.GEM_LAUNCH_PLATFORM,
  ])

  if (nfts.length === 0) return null

  const headers = {
    level: t('nfts-table.headers.level'),
    image: t('nfts-table.headers.image'),
    collection: t('nfts-table.headers.collection'),
    poolWeight: t('gem-launch-platform:tiers-privileges.pool-weight'),
    claimBack: t('gem-launch-platform:tiers-privileges.claim-back'),
    tradableVesting: t('gem-launch-platform:tiers-privileges.tradable-vesting'),
    goToButton: '',
  }

  const nftsAdapted = nfts.map(nft => adaptNft(nft, t))

  return (
    <section className="flex flex-col gap-6">
      <H3 isBold>{t('nfts-table.title')}</H3>

      <Table
        headers={headers}
        items={nftsAdapted}
        typography={{
          header: TextS,
          mainRow: TextNumeric,
          row: TextSNumeric,
        }}
      />
    </section>
  )
}
