import type { TagColor } from '~/tenset-components'

export enum GemStatus {
  PREVIOUS = 'previous',
  CURRENT = 'current',
  FUTURE = 'future',
}

export type GemsPaginationType = {
  data: Array<GemDataType>
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export type GemDataType = {
  id: number
  attributes: GemAttributesType
}

export type GemAttributesType = {
  name: string
  title: string
  logo: GemImageType
  presaleLink?: string
  status: GemStatus
  card: {
    description: string
    cover: GemImageType
    readMoreLink?: string
  }
  tags?: Array<{
    label: string
    color: TagColor
    tableVisibility?: boolean
    tooltipText?: string
  }>
  launchDataTitle?: string
  launchData: GemDataField[]
  tokenDataTitle?: string
  tokenData: GemDataField[]
  aboutTheGemTitle?: string
  about: string
  sidebar?: Array<{
    title: string
    items: Array<{
      label: string
      link: string
      prefixIcon?: string
      suffixIcon?: string
    }>
  }>
  createdAt: string
  updatedAt: string
}

type GemImageType = {
  data: {
    attributes: {
      url: string
      alternativeText: string
    }
  }
}

export type GemDataField = {
  label: string
  data?: string
  key?: string
  showOnCard?: boolean
  tokenPrice?: {
    initialValue: number
    initialValueCurrency: string
    gotValue: number
    gotValueCurrency: string
  }
  currency?: {
    currency: string
    value: number
  }
  number?: {
    suffix?: string
    value: number
  }
}
