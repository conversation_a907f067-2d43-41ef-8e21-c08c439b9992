import { getServerEnvironment } from '~/environment/environment.server'

const TICKETS_AMOUNT = 50

export async function areTicketsLeft(): Promise<boolean> {
  const result = await fetch(
    `${getServerEnvironment().STRAPI_URL}/api/signed-messages`,
    {
      headers: {
        Authorization: `Bearer ${getServerEnvironment().STRAPI_TOKEN}`,
      },
    }
  )

  const { meta } = await result.json()

  return meta.pagination.total < TICKETS_AMOUNT
}

export async function fetchSignedMessage(address: string): Promise<boolean> {
  const result = await fetch(
    `${getServerEnvironment().STRAPI_URL}/api/signed-messages?filters[address][$eq]=${address}`,
    {
      headers: {
        Authorization: `Bearer ${getServerEnvironment().STRAPI_TOKEN}`,
      },
    }
  )

  const { data } = await result.json()

  return data && data.length > 0
}

export async function postSignedMessage({
  address,
  message,
  signature,
}: {
  address: string
  message: string
  signature: string
}): Promise<boolean> {
  const result = await fetch(
    `${getServerEnvironment().STRAPI_URL}/api/signed-messages`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getServerEnvironment().STRAPI_TOKEN}`,
      },
      body: JSON.stringify({
        data: {
          address,
          message,
          signature,
        },
      }),
    }
  )

  const { data } = await result.json()

  return !!data
}
