import { ContractTransaction } from 'ethers'

import { infinityVaultBridgeAbi } from '../../assets'
import { config } from '../../config'
import { Utils } from '../../types'
import { Contract } from '../base'

export class InfinityVaultBridgeContract extends Contract {
  constructor(chain: Utils.Network) {
    super(config.infinity.vaultBridge[chain]!, chain, infinityVaultBridgeAbi)
  }

  public async withdrawDeposit(network: Utils.Network, id: number) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.withdrawDeposit(
        network,
        id
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
