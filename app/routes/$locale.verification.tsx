import type {
  ActionFunction,
  LoaderFunctionArgs,
  MetaFunction,
} from '@remix-run/node'
import { json } from '@remix-run/node'
import { useFetcher } from '@remix-run/react'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { InfoCircleIcon } from 'modules/tenset-components/src/assets/icons'
import { fetchVerifiedMedias } from '~/api/fetchers'
import type { VerifiedMediaScopes } from '~/api/types'
import { verificationIllustration } from '~/assets/images'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import { HowToModal } from '~/sections/verification/how-to-modal'
import { NonVerifiedModal } from '~/sections/verification/non-verified-modal'
import { VerifiedModal } from '~/sections/verification/verified-modal'
import {
  But<PERSON>,
  But<PERSON>Variant,
  Hero,
  HeroImage,
  Input,
  TextS,
} from '~/tenset-components'
import {
  formDataToObject,
  isFormOk,
  schema,
  translateValidations,
  validate,
  validateExistanceAndNoEmptiness,
  withCache,
} from '~/utils'
import normalizePhraseToVerify from '~/utils/normalize-phrase-to-verify'

const verificationFormSchema = schema({
  phrase: [validateExistanceAndNoEmptiness],
})

export interface MediaUrls {
  type: string
  url: string
}

export interface VerifiedMediaResponse {
  phrase: string
  mediaUrls: MediaUrls[]
  mediaScopes: VerifiedMediaScopes
  verified?: boolean
}

export const action: ActionFunction = async ({ request }) => {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.FORM_VALIDATORS)

  const data = formDataToObject(
    await request.formData(),
    Object.keys(verificationFormSchema)
  )

  const validations = validate(data, verificationFormSchema)
  const formOk = isFormOk(validations)

  if (!formOk) {
    return json({
      validations: translateValidations(t, validations),
      ok: false,
    })
  }

  const phraseToVerify = normalizePhraseToVerify(data.phrase)

  const verifiedMedias = await fetchVerifiedMedias(phraseToVerify)

  const mediaUrls: MediaUrls[] =
    !verifiedMedias || verifiedMedias.length === 0
      ? []
      : verifiedMedias.map(media => {
          const {
            attributes: {
              name: mediaName,
              media_source: {
                data: { attributes: mediaSourceAttributes },
              },
            },
          } = media

          const { name, baseUrl } = mediaSourceAttributes

          return {
            type: name,
            url: `${baseUrl}${mediaName}`,
          }
        })

  const mediaScopes: VerifiedMediaScopes =
    !verifiedMedias || verifiedMedias.length === 0
      ? []
      : verifiedMedias.map(media => {
          const {
            attributes: {
              media_scopes: { data: scopes },
            },
          } = media

          return scopes?.map(scope => {
            return {
              name: scope.attributes.name,
              description: scope.attributes.description,
            }
          })
        })[0]

  return json({
    ...verifiedMedias,
    ok: typeof verifiedMedias !== 'boolean',
    phrase: data.phrase,
    verified: verifiedMedias && verifiedMedias.length > 0,
    mediaUrls,
    mediaScopes,
  })
}

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.VERIFICATION)

  const title = t('meta.title')
  const description = t('meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export const handle = {
  i18n: [Namespace.VERIFICATION, Namespace.COMMON],
}

export default function Verification() {
  const { t } = useTranslation([Namespace.VERIFICATION])

  const [verifiedMedia, setVerifiedMedia] = useState<VerifiedMediaResponse>({
    phrase: '',
    mediaUrls: [],
    mediaScopes: [],
  })

  const fetcher = useFetcher<VerifiedMediaResponse>()
  const ref = useRef<HTMLFormElement | null>(null)
  const isLoading = fetcher.state === 'submitting'

  const [isOpenHowTo, setIsOpenHowTo] = useState<boolean>(false)
  const [isOpenNonVerified, setIsOpenNonVerified] = useState<boolean>(false)
  const [isOpenVerified, setIsOpenVerified] = useState<boolean>(false)

  const isDone = fetcher.state === 'idle' && fetcher.data !== null

  useEffect(() => {
    if (isDone && fetcher.data) {
      setVerifiedMedia({
        phrase: fetcher.data.phrase,
        mediaUrls: fetcher.data.mediaUrls,
        mediaScopes: fetcher.data.mediaScopes,
      })

      fetcher.data.verified
        ? setIsOpenVerified(true)
        : setIsOpenNonVerified(true)
    }
  }, [isDone])

  return (
    <div className="container">
      <Hero
        title={t('hero.title')}
        description={t('hero.description')}
        rightContent={<HeroImage src={verificationIllustration} />}
        leftContent={
          <>
            <fetcher.Form
              ref={ref}
              className="mb-2 flex flex-row"
              method="post"
            >
              <Input name="phrase" placeholder={t('input-placeholder')} />

              <Button
                className="ml-4 max-h-[40px]"
                variant={ButtonVariant.Primary}
                type="submit"
                disabled={isLoading}
                loading={isLoading}
              >
                {t('common:verify')}
              </Button>
            </fetcher.Form>

            <div>
              <TextS className="text-neutral-100">
                {t('common:eg')} @TenseT_io
              </TextS>
            </div>

            <Button
              onClick={() => setIsOpenHowTo(true)}
              className="mt-8"
              variant={ButtonVariant.Secondary}
            >
              <span>{t('how-to-use-verification')}</span>
              <InfoCircleIcon />
            </Button>
          </>
        }
      />

      <HowToModal
        isOpen={isOpenHowTo}
        onDismiss={() => setIsOpenHowTo(false)}
      />

      <NonVerifiedModal
        isOpen={isOpenNonVerified}
        onDismiss={() => setIsOpenNonVerified(false)}
        verifiedPhrase={verifiedMedia.phrase}
        onClickHowTo={() => {
          setIsOpenNonVerified(false)
          setIsOpenHowTo(true)
        }}
      />

      <VerifiedModal
        isOpen={isOpenVerified}
        onDismiss={() => setIsOpenVerified(false)}
        verifiedPhrase={verifiedMedia.phrase}
        mediaButtons={verifiedMedia.mediaUrls}
        mediaScopes={verifiedMedia.mediaScopes}
      />
    </div>
  )
}
