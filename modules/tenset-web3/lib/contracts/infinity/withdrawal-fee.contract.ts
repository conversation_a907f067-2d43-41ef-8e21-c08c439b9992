import { config, infinityWithdrawalFeeAbi } from '../..'
import { Utils } from '../../types'
import { Contract } from '../base'

export class InfinityWithdrawalFeeContract extends Contract {
  constructor(network: Utils.Network) {
    super(
      config.infinity.withdrawalFee[network]!,
      network,
      infinityWithdrawalFeeAbi
    )
  }

  public async transfer(
    id: string,
    overrides: Utils.ContractMethodOverrides = {}
  ) {
    this.wallet!.asBusy()

    try {
      const tx = await this.adapter!.transfer(id, overrides)

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
