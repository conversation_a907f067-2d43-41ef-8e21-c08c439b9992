[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "affiliateCodeNonce", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "manager_", "type": "address", "internalType": "address"}, {"name": "token_", "type": "address", "internalType": "contract IERC20"}, {"name": "tglpMembership_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "invalidateAllPreviousAffiliateCodes", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "manager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "subscribe", "inputs": [{"name": "quantity", "type": "uint256", "internalType": "uint256"}, {"name": "tierId", "type": "uint16", "internalType": "uint16"}, {"name": "tokenId", "type": "uint16", "internalType": "uint16"}, {"name": "subscriber", "type": "address", "internalType": "address"}, {"name": "affiliateCode", "type": "tuple", "internalType": "struct ITGLPSubscribe.AffiliateCode", "components": [{"name": "affiliate", "type": "address", "internalType": "address"}, {"name": "commissionNumerator", "type": "uint16", "internalType": "uint16"}, {"name": "commissionDenominator", "type": "uint16", "internalType": "uint16"}, {"name": "bonusNumerator", "type": "uint16", "internalType": "uint16"}, {"name": "bonusDenominator", "type": "uint16", "internalType": "uint16"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tglpMembership", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ITGLPMembership"}], "stateMutability": "view"}, {"type": "function", "name": "token", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateManager", "inputs": [{"name": "manager_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "withdraw<PERSON>oin", "inputs": [{"name": "to", "type": "address", "internalType": "address payable"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawToken", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "token_", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AffiliateCodeNonceUpdated", "inputs": [], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "ManagerUpdated", "inputs": [{"name": "manager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MembershipAddressUpdated", "inputs": [{"name": "membership", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SubscriptionsCreated", "inputs": [{"name": "subscriber", "type": "address", "indexed": true, "internalType": "address"}, {"name": "quantity", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "price", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "affiliateCode", "type": "tuple", "indexed": false, "internalType": "struct ITGLPSubscribe.AffiliateCode", "components": [{"name": "affiliate", "type": "address", "internalType": "address"}, {"name": "commissionNumerator", "type": "uint16", "internalType": "uint16"}, {"name": "commissionDenominator", "type": "uint16", "internalType": "uint16"}, {"name": "bonusNumerator", "type": "uint16", "internalType": "uint16"}, {"name": "bonusDenominator", "type": "uint16", "internalType": "uint16"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}], "anonymous": false}, {"type": "event", "name": "TokenUpdated", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "contract IERC20"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidSignature", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "UnacceptableV<PERSON>ue", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "WithdrawToZeroAddress", "inputs": []}, {"type": "error", "name": "YouHaveNoRewards", "inputs": []}]