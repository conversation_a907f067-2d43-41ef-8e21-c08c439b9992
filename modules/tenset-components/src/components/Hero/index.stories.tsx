import { Meta } from '@storybook/react'

import { blankCover } from '../../assets/images/'
import { Button, ButtonVariant } from '../Button'
import { GateLogo, KangaLogo, PancakeLogo } from '../../assets/icons'
import { HeroImage } from '../../components/HeroImage'

import { Hero } from './index'

type HeroType = typeof Hero

const markets = [
  {
    title: 'Kanga Exchange',
    logo: <KangaLogo />,
  },
  {
    title: 'Pancake Swap',
    logo: <PancakeLogo />,
  },
  {
    title: 'Gate',
    logo: <GateLogo />,
  },
]

export default {
  title: 'Components/Hero',
  component: Hero,
  args: {
    title: 'The less assets out there the more pricy they get',
    description:
      'We regularly purchase 10set assets from the market and then burn them for deflation.',
    rightContent: <HeroImage src={blankCover} alt={'Blank cover'} />,
    leftContent: (
      <div className="flex flex-col gap-5 md:flex-row">
        {markets.map(({ title, logo }) => (
          <Button key={title} variant={ButtonVariant.Secondary} reverseChildren>
            {title} {logo}
          </Button>
        ))}
      </div>
    ),
  },
} as Meta<HeroType>

export const Preview = {}

export const Centered = {
  args: {
    isCentered: true,
  },
}
