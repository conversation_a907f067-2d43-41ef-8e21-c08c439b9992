import type { TFunction } from 'i18next'
import { isAddress } from 'ethers/lib/utils'

export const formDataToObject = (data: FormData, keys: string[]) => {
  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
  const returnValueObject: any = {}
  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
  let temporary: any = null
  let length = 0

  for (const key of keys) {
    temporary = data.getAll(key)
    length = temporary.length

    if (length === 1) {
      returnValueObject[key] = temporary[0]

      continue
    }

    returnValueObject[key] = temporary
  }

  return returnValueObject
}

type Validator<T> = (v: T | null | undefined) => boolean

export const validateIfEmailAlike: Validator<string> = value => {
  return !!(value && /^\S+@\S+\.\S+$/.test(value))
}

export const validateIfEthAddress: Validator<string> = value => {
  return isAddress(value || '')
}

export const validateExistanceAndNoEmptiness: Validator<string> = value => {
  return !!(value && value.length > 0)
}

export const validateIfDate: Validator<string> = value => {
  return !!(value && Date.parse(value))
}

/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
const validatorFunctionToTranslation: any = {
  validateExistanceAndNoEmptiness: 'missingKey',
  validateIfEmailAlike: 'notAnEmail',
}

/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
const partialSchemaValidator = (validators: Validator<any>[]) => {
  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
  return (value: any) => {
    for (const validator of validators) {
      if (!validator(value)) {
        return validatorFunctionToTranslation[validator.name]
      }
    }

    return null
  }
}

/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
export const validate = (data: any, schema: any) => {
  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
  const validations: any = {}

  for (const [key, validator] of Object.entries(schema)) {
    const value = data[key]

    if (value === undefined) {
      console.warn(
        `Schema/Data mismatch for key: ${key}, and schema: ${schema}!`
      )
    }

    /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
    const tr = (validator as (x: any) => string)(value)

    if (tr) {
      validations[key] = tr
    }
  }

  return validations
}

export const schema = (x: Record<string, unknown>) => {
  for (const [k, vx] of Object.entries(x)) {
    /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
    x[k] = partialSchemaValidator(vx as any)
  }

  return x
}

export function isFormOk(x: Record<string, unknown>) {
  return Object.keys(x).length === 0
}

/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
export const translateValidations = (t: TFunction, validations: any) => {
  return Object.fromEntries(
    Object.entries(validations).map(([k, v]) => [k, t(v as string)])
  )
}
