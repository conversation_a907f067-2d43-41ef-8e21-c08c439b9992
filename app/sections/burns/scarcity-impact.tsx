import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import { H2, TextL } from '~/tenset-components'

export function BurnsScarcityImpact() {
  const { t } = useTranslation([Namespace.BURNS])

  return (
    <div className="flex flex-col items-center justify-center text-center max-w-screen-lg mx-auto gap-4">
      <H2 isBold>{t('scarcity-impact.title')}</H2>

      <TextL>{t('scarcity-impact.description')}</TextL>
    </div>
  )
}
