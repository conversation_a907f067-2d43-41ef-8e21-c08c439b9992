import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  Modal,
  ModalContent,
  ModalDescription,
  ModalHeader,
  ModalMain,
  ModalTitle,
} from '~/tenset-components'

interface HowToModalProps {
  isOpen: boolean
  onDismiss: () => void
}

export function HowToModal({ isOpen, onDismiss }: HowToModalProps) {
  const { t } = useTranslation([Namespace.VERIFICATION])

  return (
    <Modal open={isOpen} onClose={onDismiss}>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{t('how-to-use-verification')}</ModalTitle>

          <ModalDescription>{t('how-to-modal.description')}</ModalDescription>
        </ModalHeader>

        <ModalMain>
          <ol className="list-decimal pl-[19px]">
            <li>{t('how-to-modal.point-one')}</li>
            <li>{t('how-to-modal.point-two')}</li>
            <li>{t('how-to-modal.point-three')}</li>
          </ol>
        </ModalMain>
      </ModalContent>
    </Modal>
  )
}
