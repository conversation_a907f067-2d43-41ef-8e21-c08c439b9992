import clsx from 'clsx'
import type { HTMLAttributes } from 'react'

import { H3 } from '~/tenset-components'

interface SocialsContainerProps extends HTMLAttributes<HTMLDivElement> {
  title: string
}

export default function SocialsContainer({
  title,
  children,
  className,
}: SocialsContainerProps) {
  return (
    <div className={clsx('flex flex-col gap-8 md:items-start', className)}>
      <H3 isBold>{title}</H3>

      <div className="flex flex-col gap-4">{children}</div>
    </div>
  )
}
