import { initializeApp, type FirebaseApp } from 'firebase/app'
import { getFirestore } from 'firebase/firestore'
import { getAuth, signInWithCustomToken } from 'firebase/auth'

import { FirestoreClient } from './firestore.client'

export interface FirebaseClientOptions {
  apiKey: string
  authDomain: string
  databaseURL: string
  projectId: string
  storageBucket: string
  messagingSenderId: string
  appId: string
  measurementId?: string
  apiHost?: string
}

export class FirebaseClient {
  protected app: FirebaseApp | null = null

  protected _firestore: FirestoreClient | null = null

  constructor(protected options: FirebaseClientOptions) {
    //
  }

  async connect() {
    const { apiHost, ...options } = this.options

    this.app = initializeApp(options)

    const auth = getAuth(this.app)

    return new Promise(resolve => {
      if (auth.currentUser) return resolve(true)

      fetch(`${apiHost}/auth`)
        .then(async response => await response.text())
        .then(token =>
          signInWithCustomToken(auth, token).then(() => resolve(true))
        )
    })
  }

  firestore() {
    if (!this._firestore) {
      this._firestore = new FirestoreClient(getFirestore(this.app!))
    }

    return this._firestore
  }
}
