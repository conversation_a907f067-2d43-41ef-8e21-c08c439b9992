import { Meta } from '@storybook/react'

import {
  <PERSON>ton,
  ButtonVariant,
  H3,
  Icon,
  IconName,
  Modal,
  ModalClose,
  ModalContent,
  ModalDescription,
  ModalFooter,
  ModalHeader,
  ModalMain,
  ModalTitle,
  ModalTrigger,
  TextS,
} from '../'

type ModalType = typeof Modal

export default {
  title: 'Components/Modal',
  component: Modal,
  args: {
    children: (
      <>
        <ModalTrigger>Open</ModalTrigger>

        <ModalContent>
          <ModalHeader>
            <ModalTitle>Title</ModalTitle>

            <ModalDescription>Description</ModalDescription>
          </ModalHeader>

          <ModalMain>Content</ModalMain>

          <ModalFooter>
            <Button>Button</Button>

            <ModalClose>Close</ModalClose>
          </ModalFooter>
        </ModalContent>
      </>
    ),
  },
} as Meta<ModalType>

export const Preview = {}

export const LongContent = {
  args: {
    children: (
      <>
        <ModalTrigger>Open</ModalTrigger>

        <ModalContent>
          <ModalHeader>
            <ModalTitle>Title</ModalTitle>

            <ModalDescription>Description</ModalDescription>
          </ModalHeader>

          <ModalMain className="flex justify-between flex-col h-[1000px]">
            <span>Scroll to bottom</span>

            <span>Scroll to top</span>
          </ModalMain>

          <ModalFooter>
            <ModalClose>Close</ModalClose>
          </ModalFooter>
        </ModalContent>
      </>
    ),
  },
}

export const Actions = {
  args: {
    children: (
      <>
        <ModalTrigger>Open</ModalTrigger>

        <ModalContent>
          <ModalHeader>
            <ModalTitle>Title</ModalTitle>
          </ModalHeader>

          <ModalFooter>
            <Button>Button</Button>

            <ModalClose>Close</ModalClose>
          </ModalFooter>
        </ModalContent>
      </>
    ),
  },
}

export const NoContentPadding = {
  args: {
    children: (
      <>
        <ModalTrigger>Open</ModalTrigger>

        <ModalContent withPadding={false}>
          <ModalMain className="grid grid-cols-2 place-items-center">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="p-12">
                {index + 1}
              </div>
            ))}
          </ModalMain>
        </ModalContent>
      </>
    ),
  },
}

export const WithoutXCloseButton = {
  args: {
    children: (
      <>
        <ModalTrigger>Open</ModalTrigger>

        <ModalContent withXCloseButton={false}>
          <ModalHeader>
            <ModalTitle>Title</ModalTitle>
          </ModalHeader>

          <ModalFooter>
            <ModalClose>Close</ModalClose>
          </ModalFooter>
        </ModalContent>
      </>
    ),
  },
}

export const AlwaysCentered = {
  args: {
    children: (
      <>
        <ModalTrigger>Open</ModalTrigger>

        <ModalContent alwaysCentered>
          <ModalMain>Content</ModalMain>
        </ModalContent>
      </>
    ),
  },
}

const supportedProviders = [
  {
    title: 'MetaMask',
    description: 'Connect to your MetaMask Wallet',
    icon: IconName.MetaMask,
  },
  {
    title: 'WalletConnect',
    description: 'Scan with WalletConnect to connect',
    icon: IconName.WalletConnectLogo,
  },
  {
    title: 'Bitget Wallet',
    description: 'Connect to your Bitget Wallet',
    icon: IconName.BitgetLogo,
  },
  {
    title: 'OKX',
    description: 'Connect to your OKX Wallet',
    icon: IconName.OKX,
  },
]

export const ConnectWallet = {
  args: {
    children: (
      <>
        <ModalTrigger>Connect wallet</ModalTrigger>

        <ModalContent withPadding={false}>
          <ModalMain className="grid grid-cols-2 place-items-center">
            {supportedProviders.map(({ title, description, icon }) => (
              <Button
                variant={ButtonVariant.Ghost}
                className="flex w-full flex-col gap-2 py-3 md:py-6"
                key={title}
              >
                <H3 isBold>{title}</H3>

                <TextS>{description}</TextS>

                <div className="order-first">
                  <Icon name={icon} size={40} />
                </div>
              </Button>
            ))}
          </ModalMain>
        </ModalContent>
      </>
    ),
  },
}
