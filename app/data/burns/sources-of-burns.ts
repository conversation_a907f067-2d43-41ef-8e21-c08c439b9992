import type { TFunction } from 'i18next'

import type { CardWithTile } from '~/components/cards-with-title'
import { IconName } from '~/tenset-components'

export const sourcesOfBurns = (t: TFunction): CardWithTile[] => [
  // {
  //   title: t('sources-of-burns.transaction-fee.title'),
  //   description: t('sources-of-burns.transaction-fee.description'),
  //   image: IconName.TransactionFee,
  // },
  {
    title: t('sources-of-burns.launchpad-nft.title'),
    description: t('sources-of-burns.launchpad-nft.description'),
    image: IconName.NftLaunchpad,
  },
  {
    title: t('sources-of-burns.manual-swapback.title'),
    description: t('sources-of-burns.manual-swapback.description'),
    image: IconName.ManualSwapback,
  },
  {
    title: t('sources-of-burns.automatic-swapback.title'),
    description: t('sources-of-burns.automatic-swapback.description'),
    image: IconName.AutomaticSwapback,
  },
  {
    title: t('sources-of-burns.whale-hunt.title'),
    description: t('sources-of-burns.whale-hunt.description'),
    image: IconName.WhaleHunt,
  },
  {
    title: t('sources-of-burns.nft-marketplace.title'),
    description: t('sources-of-burns.nft-marketplace.description'),
    image: IconName.NftMarketplace,
  },
]
