
* Tenset React Components

The following repository consists of React components used across all [[https://www.tenset.io/en][@tenset]] projects.

** Development

*** Storybook environment

#+begin_src bash
  yarn storybook:start
#+end_src

*** Generate a new component (paste script to ~bash~)

#+begin_src bash
  # script settings
  export COMP="YourComponent";

  # ---------------------------------

  # helpers
  export BPATH="src/components";

  # mk comp directory
  mkdir -p $BPATH/$COMP;

  # comp template
  printf "import React from 'react'\n\nexport const $COMP: React.FC<any> = (props) => <div></div>" >> $BPATH/$COMP/index.tsx;

  # comp export append
  echo "export * from './$COMP'" >> $BPATH/index.ts;
#+end_src

** Building

*Storybook*

#+begin_src bash
  yarn storybook:build
#+end_src

*Library*

- once

  #+begin_src bash
  yarn build
  #+end_src

- in watch mode

  #+begin_src bash
  yarn build:watch
  #+end_src

** How to integrate?

To discuss..
