import { ToastStatus, Toast } from './index'

const { SUCCESS, INFO, ERROR } = ToastStatus

export default {
  title: 'Components/Toast',
  component: Toast,
}

export const Success = {
  args: {
    children: 'Action with success',
    status: SUCCESS,
  },
}

export const Info = {
  args: {
    children: 'Info about action',
    status: INFO,
  },
}

export const Error = {
  args: {
    children: 'Action goes wrong',
    status: ERROR,
  },
}

export const SuccessLong = {
  args: {
    children:
      'Action with success with very long description. Action with success with very long description. Action with success with very long description. Action with success with very long description. Action with success with very long description.',
    status: SUCCESS,
  },
}

export const ErrorLong = {
  args: {
    children:
      'Action with error with very long description. Action with error with very long description.',
    status: ERROR,
    txHash: '0x70f657164e5b75689b64b7fd1fa275f334f28e1',
    txLink:
      'https://bscscan.com/token/0xebc76079da0c245fae7225b58a57a54809b40618?a=0x70f657164e5b75689b64b7fd1fa275f334f28e18',
  },
}
