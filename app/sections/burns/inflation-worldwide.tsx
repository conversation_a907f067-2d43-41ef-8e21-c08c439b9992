import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  CurrencyFormatter,
  H2,
  NumberFormatter,
  TextNumeric,
} from '~/tenset-components'
import type { UseCountriesInflation } from '~/hooks/simulated-stats'
import { useCountriesInflation } from '~/hooks/simulated-stats'
import { BuybacksDataSection } from '~/sections/homepage/buybacks/data'
import { InflationWorldwideMap } from '~/components/burns'

interface BurnsInflationWorldwideProps {
  countriesInflationApiData: UseCountriesInflation
}

export function BurnsInflationWorldwide({
  countriesInflationApiData,
}: BurnsInflationWorldwideProps) {
  const { t } = useTranslation([Namespace.BURNS])

  const countriesInflation = useCountriesInflation(countriesInflationApiData)

  const countriesInflationData = countriesInflation.map(
    ({ country, value, inflationPercentage }) => ({
      label: country,
      data: [
        {
          value: value,
          formatter: ({ value }: { value: number }) => (
            <CurrencyFormatter value={value} currency="USD" />
          ),
        },
        {
          value: inflationPercentage,
          formatter: ({ value }: { value: number }) => (
            <TextNumeric>
              <NumberFormatter value={value} suffix="% Inflation" />
            </TextNumeric>
          ),
        },
      ],
    })
  )

  return (
    <div className="flex flex-col gap-8 lg:gap-12">
      <H2 isBold>{t('inflation-worldwide.title')}</H2>

      <InflationWorldwideMap />

      <BuybacksDataSection burnsData={countriesInflationData} wider />
    </div>
  )
}
