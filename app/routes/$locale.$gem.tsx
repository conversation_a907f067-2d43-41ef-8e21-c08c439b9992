import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useLoaderData, useParams } from '@remix-run/react'
import { useTranslation } from 'react-i18next'

import IsYourProjectNextGemBlock from '~/components/blocks/is-your-project-next-gem'
import { GemsWithHeader } from '~/components/gems-with-header'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { Button, ButtonVariant, Icon, IconName } from '~/tenset-components'
import { withCache } from '~/utils'
import {
  GemsDetailsAboutTheGemSection,
  GemsDetailsHeroSection,
} from '~/sections/gem'
import { getGemsList } from '~/api/fetchers'

export async function loader({ params }: LoaderFunctionArgs) {
  const gemsList = await getGemsList(1, 100)
  const gem = gemsList && gemsList.find(({ name }) => name === params.gem)

  if (!gem) {
    throw new Response('Not Found', {
      status: 404,
    })
  }

  return withCache(
    json({
      gem,
      gemsList,
    })
  )
}

export const meta: MetaFunction<typeof loader> = ({ data, params }) => {
  if (data === undefined) return []

  return [
    { title: `${data.gem.title} | Tenset` },
    {
      name: 'description',
      content: data.gem.card.description,
    },
  ]
}

export const handle = {
  i18n: [Namespace.GEM, Namespace.COMMON],
}

export default function GemName() {
  const { gem: gemName } = useParams()
  const { gem, gemsList } = useLoaderData<typeof loader>()
  const localizePathname = useLocalizePathname()
  const { t } = useTranslation([Namespace.GEM, Namespace.COMMON])

  const previousGems = gemsList
    .filter(gem => gem.status === 'previous' && gem.name !== gemName)
    .slice(0, 2)

  return (
    <div className="container md:gap-32">
      <GemsDetailsHeroSection {...gem} />

      <GemsDetailsAboutTheGemSection {...gem} />

      <GemsWithHeader header={t('more-gems-launched')} gems={previousGems}>
        <div>
          <Button
            className="w-full md:w-auto"
            to={localizePathname('/gem-launch-platform')}
            variant={ButtonVariant.Secondary}
          >
            {t('common:gem-launch-platform')}
            <Icon name={IconName.ChevronRight16} />
          </Button>
        </div>
      </GemsWithHeader>

      <IsYourProjectNextGemBlock />
    </div>
  )
}
