import {
  Infinity<PERSON>ftCollection,
  type InfinityNftLock,
  type InfinityNftLockResponse,
  type InfinityNftLocksData,
} from '~/api/types'

export function infinityNftLocksAdapter(
  response: InfinityNftLockResponse[]
): InfinityNftLocksData {
  const formattedNftLocks = response.map(
    ({
      nftId,
      nftAddress,
      unlockTime,
      withdrawed,
      unlockavailable,
      network,
    }): InfinityNftLock => ({
      collection: InfinityNftCollection.get(nftAddress) ?? 'Unknown Collection',
      nftId,
      nftAddress,
      releaseDate: unlockTime,
      isWithdrawn: withdrawed,
      isUnlocked: unlockavailable && !withdrawed,
      network,
    })
  )

  return {
    lockedNftLocks: formattedNftLocks.filter(({ isWithdrawn }) => !isWithdrawn),
    withdrawnNftLocks: formattedNftLocks.filter(
      ({ isWithdrawn }) => isWithdrawn
    ),
  }
}
