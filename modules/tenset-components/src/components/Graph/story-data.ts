export const graphTokenPriceData = [
  {
    timestamp: 1_666_357_200_000,
    price: 0.976_705,
  },
  {
    timestamp: 1_666_359_000_000,
    price: 0.966_082,
  },
  {
    timestamp: 1_666_360_800_000,
    price: 0.967_358,
  },
  {
    timestamp: 1_666_362_600_000,
    price: 0.969_498,
  },
  {
    timestamp: 1_666_364_400_000,
    price: 0.967_371,
  },
  {
    timestamp: 1_666_366_200_000,
    price: 0.966_16,
  },
  {
    timestamp: 1_666_368_000_000,
    price: 0.969_698,
  },
  {
    timestamp: 1_666_369_800_000,
    price: 0.955_837,
  },
  {
    timestamp: 1_666_371_600_000,
    price: 0.962_768,
  },
  {
    timestamp: 1_666_373_400_000,
    price: 0.962_845,
  },
  {
    timestamp: 1_666_375_200_000,
    price: 0.963_615,
  },
  {
    timestamp: 1_666_377_000_000,
    price: 0.962_631,
  },
  {
    timestamp: 1_666_378_800_000,
    price: 0.962_491,
  },
  {
    timestamp: 1_666_380_600_000,
    price: 0.962_387,
  },
  {
    timestamp: 1_666_382_400_000,
    price: 0.955_481,
  },
  {
    timestamp: 1_666_384_200_000,
    price: 0.957_111,
  },
  {
    timestamp: 1_666_386_000_000,
    price: 0.956_234,
  },
  {
    timestamp: 1_666_387_800_000,
    price: 0.958_581,
  },
  {
    timestamp: 1_666_389_600_000,
    price: 0.958_282,
  },
  {
    timestamp: 1_666_391_400_000,
    price: 0.958_587,
  },
  {
    timestamp: 1_666_393_200_000,
    price: 0.957_754,
  },
  {
    timestamp: 1_666_395_000_000,
    price: 0.957_578,
  },
  {
    timestamp: 1_666_396_800_000,
    price: 0.957_618,
  },
  {
    timestamp: 1_666_398_600_000,
    price: 0.957_39,
  },
  {
    timestamp: 1_666_400_400_000,
    price: 0.960_522,
  },
  {
    timestamp: 1_666_402_200_000,
    price: 0.959_673,
  },
  {
    timestamp: 1_666_404_000_000,
    price: 0.955_237,
  },
  {
    timestamp: 1_666_405_800_000,
    price: 0.959_068,
  },
  {
    timestamp: 1_666_407_600_000,
    price: 0.960_36,
  },
  {
    timestamp: 1_666_409_400_000,
    price: 0.958_668,
  },
  {
    timestamp: 1_666_411_200_000,
    price: 0.959_596,
  },
  {
    timestamp: 1_666_413_000_000,
    price: 0.958_952,
  },
  {
    timestamp: 1_666_414_800_000,
    price: 0.961_411,
  },
  {
    timestamp: 1_666_416_600_000,
    price: 0.958_074,
  },
  {
    timestamp: 1_666_418_400_000,
    price: 0.960_373,
  },
  {
    timestamp: 1_666_420_200_000,
    price: 0.958_045,
  },
  {
    timestamp: 1_666_422_000_000,
    price: 0.957_978,
  },
  {
    timestamp: 1_666_423_800_000,
    price: 0.9591,
  },
  {
    timestamp: 1_666_425_600_000,
    price: 0.959_501,
  },
  {
    timestamp: 1_666_427_400_000,
    price: 0.958_192,
  },
  {
    timestamp: 1_666_429_200_000,
    price: 0.957_24,
  },
  {
    timestamp: 1_666_431_000_000,
    price: 0.958_579,
  },
  {
    timestamp: 1_666_432_800_000,
    price: 0.959_176,
  },
  {
    timestamp: 1_666_434_600_000,
    price: 0.959_307,
  },
  {
    timestamp: 1_666_436_400_000,
    price: 0.959_28,
  },
  {
    timestamp: 1_666_438_200_000,
    price: 0.959_541,
  },
  {
    timestamp: 1_666_440_000_000,
    price: 0.958_825,
  },
]

export const graphBurnedMonthlyData = [
  {
    date: new Date('2024-01-01T00:00:00Z'),
    burned: 140_000,
  },
  {
    date: new Date('2024-02-01T00:00:00Z'),
    burned: 60_000,
  },
  {
    date: new Date('2024-03-01T00:00:00Z'),
    burned: 75_000,
  },
  {
    date: new Date('2024-04-01T00:00:00Z'),
    burned: 160_000,
  },
  {
    date: new Date('2024-05-01T00:00:00Z'),
    burned: 100_000,
  },
  {
    date: new Date('2024-06-01T00:00:00Z'),
    burned: 190_000,
  },
]
