import type {
  GemAttributesType,
  GemDataType,
  GemsPaginationType,
} from '~/api/types'
import { renderMarkdown } from '~/utils'
import { getServerEnvironment } from '~/environment/environment.server'

export async function getGemsList(
  page: number,
  pageSize: number
): Promise<GemAttributesType[] | undefined> {
  const GEMS_LIST_URL = `${getServerEnvironment().STRAPI_URL}/api/gems`

  const parameters = decodeURIComponent(
    new URLSearchParams({
      'pagination[page]': page.toString(),
      'pagination[pageSize]': pageSize.toString(),
      'sort[0]': 'publishedAt:asc',
    }).toString()
  )

  const gems = await fetch(`${GEMS_LIST_URL}?${parameters}`)
    .then<GemsPaginationType>(response => response.json())
    .catch(error => {
      console.warn(error)
    })

  if (!gems || !gems.data) return undefined

  return gems.data.map((gem: GemDataType) => {
    gem.attributes.about = gem.attributes.about
      ? renderMarkdown(gem.attributes.about)
      : ''

    return gem.attributes
  })
}
