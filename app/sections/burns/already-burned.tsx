import { Trans, useTranslation } from 'react-i18next'

import {
  TextNumeric,
  H2,
  Table,
  H3,
  Text,
  CryptoCurrencyFormatter,
  NumberFormatter,
  AnimatedNumber,
  IconName,
  CurrencyFormatter,
} from '~/tenset-components'
import { Namespace } from '~/i18n'
import type { BurnData } from '~/sections/homepage/buybacks/data'
import { BuybacksDataSection } from '~/sections/homepage/buybacks/data'
import { Utils } from '~/tenset-web3'
import type { SimulatedStats } from '~/hooks/simulated-stats'
import { OTHER_BURNS_ANIMATION_DURATION } from '~/hooks/simulated-stats'
import {
  CardsWithTitle,
  type CardWithTile,
} from '~/components/cards-with-title'

interface BurnSummary {
  headers: {
    type: string
    value: string
  }
  items: {
    type: string
    value: JSX.Element
  }[]
}

type BurnsAlreadyBurnedProps = Pick<
  SimulatedStats,
  'totalBurned' | 'marketplaceBurns' | 'otherBurns' | 'currentCirculation'
> & {
  tensetPrice: number
}

export function BurnsAlreadyBurned({
  totalBurned,
  marketplaceBurns,
  otherBurns,
  currentCirculation,
  tensetPrice,
}: BurnsAlreadyBurnedProps) {
  const { t } = useTranslation([Namespace.BURNS])

  const TOTAL_BURNED_USD = totalBurned * tensetPrice

  const circulationSupplyPercentage = (totalBurned / currentCirculation) * 100

  const burnSummary: BurnSummary = {
    headers: {
      type: 'Type',
      value: '',
    },
    items: [
      {
        type: t('already-burned.total-burned'),
        value: (
          <div className="flex justify-end">
            <AnimatedNumber
              value={totalBurned}
              duration={OTHER_BURNS_ANIMATION_DURATION}
              formatter={({ value }) => (
                <CryptoCurrencyFormatter
                  value={value}
                  currency="10SET"
                  precision={[4, 4]}
                />
              )}
            />
          </div>
        ),
      },
      {
        type: t('already-burned.total-burned-value'),
        value: (
          <div className="flex justify-end">
            <AnimatedNumber
              value={TOTAL_BURNED_USD}
              duration={OTHER_BURNS_ANIMATION_DURATION}
              formatter={({ value }) => (
                <CryptoCurrencyFormatter value={value} precision={[4, 4]} />
              )}
            />
          </div>
        ),
      },
      {
        type: t('already-burned.summary.circulation-supply-percentage'),
        value: (
          <div className="flex justify-end">
            <AnimatedNumber
              value={circulationSupplyPercentage}
              duration={OTHER_BURNS_ANIMATION_DURATION}
              formatter={({ value }) => (
                <NumberFormatter value={value} precision={[0, 2]} suffix="%" />
              )}
            />
          </div>
        ),
      },
    ],
  }

  const burnData: BurnData[] = [
    {
      label: t('already-burned.data.marketplace'),
      data: [
        {
          value: marketplaceBurns,
          formatter: ({ value }: { value: number }) => (
            <CryptoCurrencyFormatter
              value={value}
              currency="10SET"
              precision={[4, 4]}
            />
          ),
        },
      ],
    },
    {
      label: t('already-burned.data.other'),
      data: [
        {
          value: otherBurns,
          formatter: ({ value }: { value: number }) => (
            <CryptoCurrencyFormatter
              value={value}
              currency="10SET"
              precision={[4, 4]}
            />
          ),
          address: '',
          network: Utils.Network.SMART_CHAIN,
        },
      ],
    },
  ]

  const realTimeConversions = [
    {
      title: 'already-burned.lamborghini-huracan',
      cost: 250_000,
      icon: IconName.Lamborghini,
    },
    {
      title: 'already-burned.rolex-submariner',
      cost: 15_000,
      icon: IconName.Rolex,
    },
    {
      title: 'already-burned.iphone-15-pro',
      cost: 1000,
      icon: IconName.IPhone,
    },
  ]

  const realItemsConversionsMap: CardWithTile[] = realTimeConversions.map(
    ({ title, cost, icon: image }) => {
      const amount = TOTAL_BURNED_USD / cost

      return {
        title: (
          <Trans t={t} i18nKey={title}>
            <NumberFormatter value={amount} precision={[0, 0]} />
          </Trans>
        ),
        description: (
          <Trans t={t} i18nKey={'already-burned.each-cost'}>
            <CurrencyFormatter value={cost} precision={[0, 0]} />
          </Trans>
        ),
        image,
      }
    }
  )

  return (
    <div className="flex flex-col gap-8 lg:gap-12">
      <H2 isBold>{t('already-burned.title')}</H2>

      <CardsWithTitle cards={realItemsConversionsMap} />

      <div className="grid gap-12 md:grid-cols-2">
        <Table
          headers={burnSummary.headers}
          items={burnSummary.items}
          isRaw
          typography={{
            header: Text,
            mainRow: Text,
            row: TextNumeric,
          }}
        />

        <div className="flex flex-col gap-4 md:mt-6">
          <H3 isBold>{t('already-burned.description.title')}</H3>

          <Text>{t('already-burned.description.text')}</Text>
        </div>
      </div>

      <BuybacksDataSection burnsData={burnData} />
    </div>
  )
}
