import type { LoaderFunctionArgs, TypedResponse } from '@remix-run/node'
import { json, redirect } from '@remix-run/node'
import { useFetcher, useLoaderData } from '@remix-run/react'
import clsx from 'clsx'
import { ethers } from 'ethers'
import { isAddress } from 'ethers/lib/utils'
import type { TFunction } from 'i18next'
import type { ChangeEvent, Dispatch, SetStateAction } from 'react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import {
  fetchGlpSubscriptionInfoForMany,
  fetchInfinityNftLocks,
} from '~/api/fetchers'
import type { GlpLock, GlpNft, InfinityNftLock } from '~/api/types'
import { formatLock, formatNft } from '~/components/gem-launch-platform'
import { SwitchNetworkButton } from '~/components/switch-network/button'
import { BSC_NETWORK } from '~/config'
import { detectLocale, Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { getSession } from '~/sessions.server'
import type { CheckboxProps } from '~/tenset-components'
import {
  Button,
  ButtonVariant,
  CheckboxGroup,
  ExplorerButton,
  ExplorerType,
  H2,
  H3,
  Icon,
  IconName,
  Input,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Text,
  TextL,
  TextS,
} from '~/tenset-components'
import type { Utils } from '~/tenset-web3'
import {
  chains,
  config,
  ERC721Contract,
  getNetworkExplorer,
  getNetworkName,
  GlpLifetimeMembershipContract,
  GlpMembershipContract,
  parseEthersError,
  useErc721MultisenderContract,
  useGlpContracts,
  useWallet,
} from '~/tenset-web3'
import { getRpcLifetimeNfts, getRpcLocks } from '~/utils'

export const loader = async ({
  request,
}: LoaderFunctionArgs): Promise<
  TypedResponse<GlpTransferLoaderData | never>
> => {
  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  const locale = detectLocale(request)

  const baseUrl = `/${locale}/gem-launch-platform`
  const panelUrl = `${baseUrl}/panel`

  if (!walletAddress) return redirect(baseUrl)

  const { gemLaunchPlatform, utils } = config
  const { rpcUrls } = chains[BSC_NETWORK]

  const glpMembershipContract = new GlpMembershipContract(
    gemLaunchPlatform.membership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const glpLifetimeMembershipContract = new GlpLifetimeMembershipContract(
    gemLaunchPlatform.lifetimeMembership[BSC_NETWORK]!,
    BSC_NETWORK,
    undefined,
    rpcUrls[0]
  )

  const [
    membershipBalance,
    lifetimeMembershipBalance,
    { nfts: nftsApi },
    { lockedNftLocks: infinityLockedNfts },
  ] = await Promise.all([
    glpMembershipContract.balanceOf(walletAddress),
    glpLifetimeMembershipContract.balanceOf(walletAddress),
    fetchGlpSubscriptionInfoForMany(walletAddress),
    fetchInfinityNftLocks(walletAddress),
  ])

  const jsonRpcProvider = new ethers.providers.JsonRpcProvider({
    url: rpcUrls[0],
    skipFetchSetup: true,
  })

  const [lifetimeMembershipNfts, locksResponse, { timestamp: blockTimestamp }] =
    await Promise.all([
      getRpcLifetimeNfts({
        walletAddress,
        glpLifetimeMembershipContract,
        lifetimeMembershipBalance,
      }),
      getRpcLocks({
        walletAddress,
        glpMembershipContract,
        membershipBalance,
      }),
      jsonRpcProvider.getBlock('latest'),
    ])

  const locks = locksResponse.filter(
    ({ timestamps }) => timestamps.unlock > blockTimestamp * 1000
  )

  const nfts = [...nftsApi, ...lifetimeMembershipNfts]

  if (locks.length === 0 && nfts.length === 0) return redirect(panelUrl)

  const nftsForApprovalCheck = [
    ...new Map(
      nfts.map(({ collection, contractAddress, chainId }) => [
        collection,
        {
          collection,
          contractAddress,
          chainId,
        },
      ])
    ).values(),
  ]

  const nftsApprovals: GlpNftApproval[] = await Promise.all(
    nftsForApprovalCheck.map(async ({ contractAddress, chainId }) => {
      const rpcUrl = chains[chainId].rpcUrls[0]

      const nftContract = new ERC721Contract(
        contractAddress,
        chainId,
        undefined,
        rpcUrl
      )

      const isApprovedForAll = await nftContract.isApprovedForAll(
        walletAddress,
        utils.erc721Multisender[chainId]!
      )

      return {
        contractAddress,
        chainId,
        isApprovedForAll,
      }
    })
  )

  return json({
    locks,
    nfts,
    nftsApprovals,
    infinityLockedNfts,
  })
}

type GlpNftApproval = {
  isApprovedForAll: boolean
} & Pick<GlpNft, 'contractAddress' | 'chainId'>

type GlpTransferLoaderData = {
  locks: GlpLock[]
  nfts: GlpNft[]
  nftsApprovals: GlpNftApproval[]
  infinityLockedNfts: InfinityNftLock[]
}

export type GlpNftWithInfinityLock = GlpNft & {
  infinityLock?: InfinityNftLock
}

enum TransferStep {
  Wallet,
  Subscriptions,
  Allowance,
  Transfer,
}

type TransferStepData = {
  [key in TransferStep]: {
    title: string
    description: string
    icon: IconName
    action: JSX.Element
    note: string[]
  }
}

enum TransferSubscriptionType {
  LOCKS = 'locks',
  NFTS = 'nfts',
}

type TransferSubscriptionTypeData = {
  [key in TransferSubscriptionType]: {
    data: GlpLock[] | GlpNft[]
    label: string
  }
}

export default function GlpTransfer() {
  const { t } = useTranslation([
    Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION,
    Namespace.GEM_LAUNCH_PLATFORM,
  ])

  const { wallet } = useWallet()

  const fetcher = useFetcher({ key: 'root' })

  const [currentStep, setCurrentStep] = useState<TransferStep>(
    TransferStep.Wallet
  )

  const {
    locks: locksApi,
    nfts: nftsApi,
    nftsApprovals: nftsApprovalsApi,
    infinityLockedNfts: infinityLockedNftsApi,
  } = useLoaderData<GlpTransferLoaderData>()

  const [locks, setLocks] = useState(
    locksApi.sort((a, b) => a.timestamps.unlock - b.timestamps.unlock)
  )

  useEffect(() => {
    setLocks(locksApi.sort((a, b) => a.timestamps.unlock - b.timestamps.unlock))
  }, [locksApi])

  const [nfts, setNfts] = useState<GlpNftWithInfinityLock[]>(
    nftsApi.map(nft => {
      const infinityLock = infinityLockedNftsApi?.find(
        ({ nftId, nftAddress }) =>
          nft.id === nftId &&
          nft.contractAddress?.toLowerCase() === nftAddress?.toLowerCase()
      )

      return {
        ...nft,
        infinityLock,
      }
    })
  )

  useEffect(() => {
    setNfts(
      nftsApi.map(nft => {
        const infinityLock = infinityLockedNftsApi?.find(
          ({ nftId, nftAddress }) =>
            nft.id === nftId &&
            nft.contractAddress?.toLowerCase() === nftAddress?.toLowerCase()
        )

        return {
          ...nft,
          infinityLock,
        }
      })
    )
  }, [nftsApi, infinityLockedNftsApi])

  const [nftsApprovals, setNftsApprovals] = useState(nftsApprovalsApi)

  useEffect(() => {
    setNftsApprovals(nftsApprovalsApi)
  }, [nftsApprovalsApi])

  const [destinationAddress, setDestinationAddress] = useState('')

  const [subscriptionType, setSubscriptionType] =
    useState<TransferSubscriptionType>(
      locks.length > 0
        ? TransferSubscriptionType.LOCKS
        : TransferSubscriptionType.NFTS
    )

  useEffect(() => {
    setSubscriptionType(
      locks.length > 0
        ? TransferSubscriptionType.LOCKS
        : TransferSubscriptionType.NFTS
    )
  }, [locks.toString(), nfts.toString()])

  const [chain, setChain] = useState<Utils.Network>(
    locks.length > 0 ? BSC_NETWORK : nfts[0]?.chainId || BSC_NETWORK
  )

  const [checkedSubscriptions, setCheckedSubscriptions] = useState<string[]>([])

  const [errorMessage, setErrorMessage] = useState<string>()

  // TODO: TMP wallet & chain change event via root fetcher
  // wallet has been changed, so we are going back to first step
  useEffect(() => {
    if (
      fetcher.state === 'submitting' &&
      fetcher.formAction.includes('/api/wallet')
    ) {
      setCurrentStep(TransferStep.Wallet)
      setDestinationAddress('')
    }
  }, [fetcher.state])

  const [nftsToApprove, setNftsToApprove] = useState<GlpNftApproval[]>([])

  useEffect(() => {
    if (subscriptionType !== TransferSubscriptionType.NFTS) {
      setNftsToApprove([])

      return
    }

    const checkedNfts = checkedSubscriptions.map(subscription => {
      const [chainId, contractAddress] = subscription.split('-')

      return {
        chainId: Number.parseInt(chainId),
        contractAddress,
      }
    })

    const nftsToApprove = nftsApprovals.filter(
      ({ isApprovedForAll }) => !isApprovedForAll
    )

    setNftsToApprove(
      nftsToApprove.filter(({ chainId, contractAddress }) =>
        checkedNfts.some(
          ({
            chainId: checkedChainId,
            contractAddress: checkedContractAddress,
          }) =>
            chainId === checkedChainId &&
            contractAddress === checkedContractAddress
        )
      )
    )
  }, [nftsApprovals, checkedSubscriptions])

  const [isApprovalNeeded, setIsApprovalNeeded] = useState<boolean>(false)

  useEffect(() => {
    setIsApprovalNeeded(
      subscriptionType === TransferSubscriptionType.NFTS &&
        nftsToApprove.length > 0
    )
  }, [subscriptionType, nftsToApprove])

  const steps: TransferStepData = {
    [TransferStep.Wallet]: {
      title: t('transfer.wallet.title'),
      description: t('transfer.wallet.description'),
      icon: IconName.Wallet,
      action: (
        <GlpTransferWallet
          t={t}
          destinationAddress={destinationAddress}
          setDestinationAddress={setDestinationAddress}
          onDestinationConfirm={onDestinationConfirm}
        />
      ),
      note: [t('transfer.note-1'), t('transfer.note-2')],
    },
    [TransferStep.Subscriptions]: {
      title: t('transfer.subscriptions.title'),
      description: t('transfer.subscriptions.description'),
      icon: IconName.Subscription,
      action: (
        <GlpTransferSubscriptions
          t={t}
          subscriptionType={subscriptionType}
          setSubscriptionType={setSubscriptionType}
          locks={locks}
          nfts={nfts}
          chain={chain}
          setChain={setChain}
          checkedSubscriptions={checkedSubscriptions}
          setCheckedSubscriptions={setCheckedSubscriptions}
          onSubscriptionsSelection={onSubscriptionsSelection}
        />
      ),
      note: [t('transfer.subscriptions.note')],
    },
    [TransferStep.Allowance]: {
      title: t('transfer.allowance.title'),
      description: t('transfer.allowance.description'),
      icon: IconName.Security,
      action: (
        <GlpTransferAllowance
          t={t}
          chain={chain}
          nftsToApprove={nftsToApprove}
          setNftsApprovals={setNftsApprovals}
          setErrorMessage={setErrorMessage}
          onNftApproval={onNftApproval}
        />
      ),
      note: [t('transfer.allowance.note')],
    },
    [TransferStep.Transfer]: {
      title: t('transfer.transfer.title'),
      description: t('transfer.transfer.description'),
      icon: IconName.Transfer,
      action: (
        <GlpTransferTransfer
          t={t}
          chain={chain}
          subscriptionType={subscriptionType}
          destinationAddress={destinationAddress}
          selectedSubscriptions={checkedSubscriptions}
          setErrorMessage={setErrorMessage}
          onTransfer={onTransfer}
        />
      ),
      note: [t('transfer.note-1'), t('transfer.note-2')],
    },
  }

  function onDestinationConfirm() {
    setCurrentStep(TransferStep.Subscriptions)
  }

  function onSubscriptionsSelection() {
    setCurrentStep(
      isApprovalNeeded ? TransferStep.Allowance : TransferStep.Transfer
    )
  }

  function onNftApproval() {
    setCurrentStep(TransferStep.Transfer)
  }

  const [transferTransactionAddress, setTransferTransactionAddress] =
    useState<string>()

  function onTransfer(transactionAddress: string) {
    if (subscriptionType === TransferSubscriptionType.NFTS) {
      setNfts(
        nfts.filter(
          ({ chainId, contractAddress, id }) =>
            !checkedSubscriptions.includes(
              `${chainId}-${contractAddress}-${id}`
            )
        )
      )

      fetcher.submit(
        {},
        {
          method: 'post',
          action: `/api/gem-launch-platform/reset-cache?walletAddress=${wallet?.account}`,
        }
      )
    }

    if (subscriptionType === TransferSubscriptionType.LOCKS) {
      setLocks(
        locks.filter(({ id }) => !checkedSubscriptions.includes(`lock-${id}`))
      )
    }

    setCheckedSubscriptions([])
    setTransferTransactionAddress(transactionAddress)
  }

  const localizePathname = useLocalizePathname()

  const DASHBOARD_URL = localizePathname('/gem-launch-platform/panel')

  if (transferTransactionAddress) {
    function onTransferMore() {
      setTransferTransactionAddress(undefined)
      setCurrentStep(TransferStep.Wallet)
    }

    return (
      <div className="flex flex-col gap-4 md:items-center md:justify-center">
        <div className="flex flex-col-reverse items-center justify-center gap-6 text-center">
          <div className="flex flex-col items-center gap-4">
            <H3 isBold>{t('you-transferred-successfully.title')}</H3>

            <Text>{t('you-transferred-successfully.description')}</Text>

            <div className="flex flex-wrap gap-1">
              <Text>{t('you-transferred-successfully.track')}</Text>

              <ExplorerButton
                address={transferTransactionAddress}
                explorerType={ExplorerType.TRANSACTION}
                explorerUrl={getNetworkExplorer(chain)}
                variant={ButtonVariant.Link}
              />
            </div>
          </div>

          <Icon name={IconName.TransferCompleted} />
        </div>

        {(locks.length > 0 || nfts.length > 0) && (
          <div className="flex flex-row-reverse gap-2">
            <Button onClick={onTransferMore}>
              {t('you-transferred-successfully.transfer-more')}
            </Button>

            <Button variant={ButtonVariant.Secondary} to={DASHBOARD_URL}>
              {t('you-transferred-successfully.back-to-panel')}
            </Button>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center justify-center gap-4 md:max-w-[720px] md:pt-32">
      <div className="flex flex-col gap-6 text-center">
        <H2 isBold>{t('transfer.title')}</H2>

        <div className="grid grid-cols-2 grid-rows-2 md:flex md:flex-row md:items-center mx-auto md:justify-center gap-2 md:gap-4 md:align-middle w-fit">
          {Object.values(steps).map(({ title }, index) => {
            const isCurrentStep = currentStep === index
            const isStepDone = currentStep > index
            const isFutureStep = currentStep < index

            return (
              <div
                key={title}
                className="flex items-center md:justify-center gap-4"
              >
                <div
                  className={clsx(
                    'flex h-12 w-12 items-center justify-center rounded-full',
                    isCurrentStep && 'bg-neutral-700 text-white',
                    isStepDone && 'bg-green-900 text-green-600',
                    isFutureStep && 'border border-neutral-400 text-neutral-100'
                  )}
                >
                  {isStepDone ? (
                    <Icon name={IconName.Success} />
                  ) : (
                    <TextS isBold>{index + 1}</TextS>
                  )}
                </div>

                <TextS
                  className={clsx(
                    isStepDone && 'text-green-600',
                    isCurrentStep && 'text-white',
                    isFutureStep && 'text-neutral-100',
                    index === TransferStep.Allowance &&
                      !isApprovalNeeded &&
                      'line-through'
                  )}
                  isBold
                >
                  {title}
                </TextS>
              </div>
            )
          })}
        </div>

        <div className="flex items-center justify-center">
          <Icon name={steps[currentStep].icon} />
        </div>

        <TextL>{steps[currentStep].description}</TextL>

        <div className="relative mx-auto w-full flex flex-col gap-4 items-stretch justify-center md:w-[492px]">
          {steps[currentStep].action}

          {errorMessage && (
            <TextS className="text-red-500">{errorMessage}</TextS>
          )}

          {steps[currentStep].note.length > 0 && (
            <div>
              {steps[currentStep].note.map((note, index) => (
                <TextS key={index} className="text-neutral-100">
                  {note}
                </TextS>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface GlpTransferWalletProps {
  t: TFunction
  destinationAddress: string
  setDestinationAddress: Dispatch<SetStateAction<string>>
  onDestinationConfirm: () => void
}

function GlpTransferWallet({
  t,
  destinationAddress,
  setDestinationAddress,
  onDestinationConfirm,
}: GlpTransferWalletProps) {
  const { wallet } = useWallet()

  const [inputErrorMessage, setInputErrorMessage] = useState<string>()

  useEffect(() => {
    if (!wallet || !wallet.account) return

    const isValid =
      isAddress(destinationAddress) &&
      destinationAddress.toLowerCase() !== wallet.account.toLocaleLowerCase()

    setInputErrorMessage(
      isValid ? undefined : t('transfer.wallet.invalid-address')
    )
  }, [destinationAddress])

  function handleAddressChange(event: ChangeEvent<HTMLInputElement>) {
    const { value } = event.currentTarget

    setDestinationAddress(value)
  }

  const isInvalid =
    destinationAddress.length === 0 || Boolean(inputErrorMessage)

  return (
    <>
      <Input
        label={t('wallet-address')}
        placeholder={t('transfer.wallet.transfer-destination')}
        onChange={handleAddressChange}
        value={destinationAddress}
        errorMessage={inputErrorMessage}
      />

      <Button onClick={onDestinationConfirm} disabled={isInvalid}>
        {t('transfer.wallet.button')}
      </Button>
    </>
  )
}

interface GlpTransferSubscriptionsProps
  extends Pick<GlpTransferLoaderData, 'locks'> {
  nfts: GlpNftWithInfinityLock[]
  t: TFunction
  subscriptionType: TransferSubscriptionType
  setSubscriptionType: Dispatch<SetStateAction<TransferSubscriptionType>>
  chain: Utils.Network
  setChain: Dispatch<SetStateAction<Utils.Network>>
  checkedSubscriptions: string[]
  setCheckedSubscriptions: Dispatch<SetStateAction<string[]>>
  onSubscriptionsSelection: () => void
}

function GlpTransferSubscriptions({
  t,
  subscriptionType,
  setSubscriptionType,
  chain,
  setChain,
  locks,
  nfts,
  checkedSubscriptions,
  setCheckedSubscriptions,
  onSubscriptionsSelection,
}: GlpTransferSubscriptionsProps) {
  const subscriptionTypeData: TransferSubscriptionTypeData = {
    [TransferSubscriptionType.LOCKS]: {
      data: locks,
      label: t('locks'),
    },
    [TransferSubscriptionType.NFTS]: {
      data: nfts,
      label: t('nfts'),
    },
  }

  const [subscriptionOptions, setSubscriptionOptions] = useState<
    CheckboxProps[]
  >([])
  const [chainsOptions, setChainOptions] = useState<Utils.Network[]>([])

  useEffect(() => {
    if (subscriptionType === TransferSubscriptionType.LOCKS) {
      setChainOptions([BSC_NETWORK])

      return
    }

    const nftChains = [...new Set(nfts.map(({ chainId }) => chainId))]

    setChainOptions(nftChains)
  }, [subscriptionType, locks, nfts])

  useEffect(() => {
    if (chainsOptions.length === 0) return

    setChain(chainsOptions[0])
  }, [chainsOptions])

  useEffect(() => {
    setCheckedSubscriptions([])

    if (subscriptionType === TransferSubscriptionType.LOCKS) {
      setSubscriptionOptions(locks.map(lock => formatLock({ t, ...lock })))

      return
    }

    const nftsByChain = nfts.filter(({ chainId }) => chainId === chain)

    setSubscriptionOptions(
      nftsByChain.map(nft => {
        const isNotWithdrawnFromInfinity = Boolean(
          nft?.infinityLock && !nft.infinityLock?.isWithdrawn
        )

        return formatNft({ t, isNotWithdrawnFromInfinity, ...nft })
      })
    )
  }, [subscriptionType, chain])

  const isInvalid = checkedSubscriptions.length === 0

  return (
    <>
      <div className="flex flex-col md:grid md:grid-cols-2 gap-4">
        <div>
          <TextS isBold className="text-start">
            {t('transfer.subscriptions.subscription-type')}
          </TextS>

          <Select
            disabled={Object.values(subscriptionTypeData).every(
              ({ data }) => !data || data.length === 0
            )}
            value={subscriptionType}
            onValueChange={value =>
              setSubscriptionType(value as TransferSubscriptionType)
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>

            <SelectContent>
              <SelectGroup>
                {Object.entries(subscriptionTypeData).map(
                  ([type, { data, label }]) => (
                    <SelectItem
                      key={type}
                      value={type}
                      disabled={Boolean(!data || data.length === 0)}
                    >
                      {label}
                    </SelectItem>
                  )
                )}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div>
          <TextS isBold className="text-start">
            {t('chain')}
          </TextS>

          <Select
            value={chain.toString()}
            onValueChange={value => setChain(Number.parseInt(value))}
            disabled={chainsOptions.length < 2}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>

            <SelectContent>
              <SelectGroup>
                {chainsOptions.map(chain => (
                  <SelectItem key={chain} value={chain.toString()}>
                    {getNetworkName(chain)}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      {subscriptionOptions && subscriptionOptions.length > 0 && (
        <div className="grid">
          <CheckboxGroup
            title={
              <Text isBold tag="span">
                {t('subscriptions')}
              </Text>
            }
            name="subscriptions"
            options={subscriptionOptions}
            checked={checkedSubscriptions}
            onCheckedChange={setCheckedSubscriptions}
          />
        </div>
      )}

      <Button onClick={onSubscriptionsSelection} disabled={isInvalid}>
        {t('transfer.subscriptions.button')}
      </Button>
    </>
  )
}

interface GlpTransferAllowanceProps {
  t: TFunction
  chain: Utils.Network
  nftsToApprove: GlpNftApproval[]
  setNftsApprovals: Dispatch<SetStateAction<GlpNftApproval[]>>
  setErrorMessage: Dispatch<SetStateAction<string | undefined>>
  onNftApproval: () => void
}

function GlpTransferAllowance({
  t,
  chain,
  nftsToApprove,
  setNftsApprovals,
  setErrorMessage,
  onNftApproval,
}: GlpTransferAllowanceProps) {
  const { wallet } = useWallet()

  const { erc721MultisenderContract } = useErc721MultisenderContract(
    wallet?.chain
  )

  async function approveNfts() {
    if (!wallet || wallet?.isProcessing || !erc721MultisenderContract) return

    setErrorMessage(undefined)

    for (const { contractAddress, chainId } of nftsToApprove) {
      await wallet
        .interact(new ERC721Contract(contractAddress, chainId))
        .setApprovalForAll(erc721MultisenderContract.address, true)
        .then(() => {
          setNftsApprovals(previousApprovals =>
            previousApprovals.map(approval =>
              approval.contractAddress === contractAddress
                ? { ...approval, isApprovedForAll: true }
                : approval
            )
          )
        })
        .catch(error => {
          const parsedError = parseEthersError(error)

          setErrorMessage(parsedError)
        })
    }
  }

  useEffect(() => {
    if (nftsToApprove.every(({ isApprovedForAll }) => isApprovedForAll)) {
      onNftApproval()
    }
  }, [nftsToApprove])

  if (!wallet?.isOnChain(chain)) {
    return <SwitchNetworkButton id={chain} name={getNetworkName(chain)} />
  }

  return (
    <Button
      loading={wallet?.isProcessing}
      disabled={wallet?.isProcessing}
      onClick={approveNfts}
    >
      {t('transfer.allowance.button')}
    </Button>
  )
}

interface GlpTransferTransferProps {
  t: TFunction
  chain: Utils.Network
  subscriptionType: TransferSubscriptionType
  destinationAddress: string
  selectedSubscriptions: string[]
  setErrorMessage: Dispatch<SetStateAction<string | undefined>>
  onTransfer: (transactionAddress: string) => void
}

function GlpTransferTransfer({
  t,
  chain,
  subscriptionType,
  destinationAddress,
  selectedSubscriptions,
  setErrorMessage,
  onTransfer,
}: GlpTransferTransferProps) {
  const { wallet } = useWallet()

  const { glpMembershipContract } = useGlpContracts(wallet!.chain)
  const { erc721MultisenderContract } = useErc721MultisenderContract(
    wallet!.chain
  )

  async function transferMembership() {
    if (!wallet || wallet?.isProcessing || !glpMembershipContract) return

    setErrorMessage(undefined)

    const selectedSubscriptionsIds = selectedSubscriptions.map(subscription => {
      const [, id] = subscription.split('-')

      return Number.parseInt(id)
    })

    await wallet
      .interact(glpMembershipContract)
      .batchSafeTransferFrom(
        wallet.account!,
        destinationAddress,
        selectedSubscriptionsIds
      )
      .then(onTransfer)
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)
      })
  }

  async function transferNfts() {
    if (!wallet || wallet?.isProcessing || !erc721MultisenderContract) return

    setErrorMessage(undefined)

    const [collections, ids] = selectedSubscriptions.reduce(
      (accumulator, subscription) => {
        const [, contractAddress, id] = subscription.split('-')

        accumulator[0].push(contractAddress)
        accumulator[1].push(Number.parseInt(id))

        return accumulator
      },
      [[], []] as [string[], number[]]
    )

    await wallet
      .interact(erc721MultisenderContract)
      .batchSafeTransferFrom(destinationAddress, collections, ids)
      .then(onTransfer)
      .catch(error => {
        const parsedError = parseEthersError(error)

        setErrorMessage(parsedError)
      })
  }

  if (!wallet?.isOnChain(chain)) {
    return <SwitchNetworkButton id={chain} name={getNetworkName(chain)} />
  }

  return (
    <Button
      loading={wallet?.isProcessing}
      disabled={wallet?.isProcessing}
      onClick={
        subscriptionType === TransferSubscriptionType.LOCKS
          ? transferMembership
          : transferNfts
      }
    >
      {t('transfer.transfer.button')}
    </Button>
  )
}
