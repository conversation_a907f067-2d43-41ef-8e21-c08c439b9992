import { getServerEnvironment } from '~/environment/environment.server'

export async function fetchRecaptcha(token: string): Promise<boolean> {
  const result = await fetch(getServerEnvironment().RECAPTCHA_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `secret=${getServerEnvironment().RECAPTCHA_SECRET}&response=${token}`,
  })

  const { success } = await result.json()

  return success
}
