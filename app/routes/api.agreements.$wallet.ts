import type { LoaderFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import { cors } from 'remix-utils/cors'

import { database } from '~/database'
import { getAgreement } from '~/routes/api.agreements.helpers'

export async function loader({ request, params }: LoaderFunctionArgs) {
  await getAgreement(request)

  const { wallet } = params
  const walletLower = wallet!.toLowerCase()

  const agreement = await database!.agreementSignature.findFirst({
    where: {
      wallet: {
        equals: walletLower,
        mode: 'insensitive',
      },
    },
  })

  return await cors(request, json({ agreement }))
}
