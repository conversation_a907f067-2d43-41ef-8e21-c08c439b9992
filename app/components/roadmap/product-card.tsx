import clsx from 'clsx'

import { Image, Text, H3 } from '~/tenset-components'

export interface productCardProps {
  image: string
  title: string
  description: string
  className?: string
}

export function ProductCard({
  className,
  title,
  description,
  image,
}: productCardProps) {
  return (
    <div
      className={clsx(
        'rounded-[12px] bg-neutral-800 space-y-6 p-6 sm:p-8',
        className
      )}
    >
      <Image src={image} />
      <div className="space-y-4">
        <H3 isBold className="mb-2">
          {title}
        </H3>
        <Text>{description}</Text>
      </div>
    </div>
  )
}
