import { AssetProps } from './index'

export default function LineMonoLogo({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.76192 0H19.2359C21.8565 0 24 2.14351 24 4.76192V19.2359C24 21.8565 21.8565 24 19.2359 24H4.76192C2.14351 24 0 21.8565 0 19.2359V4.76192C0 2.14351 2.14351 0 4.76192 0Z"
        fill={color ?? 'currentColor'}
      />
      <path
        d="M11.9988 3.95117C16.7799 3.95117 20.6562 7.05519 20.6562 10.8844C20.6562 12.2214 20.1835 13.4707 19.3641 14.5297C19.3235 14.5896 19.27 14.6559 19.2037 14.7286L19.1994 14.735C18.9192 15.0687 18.6047 15.3832 18.2581 15.6741C15.8622 17.8861 11.9218 20.5216 11.4019 20.1152C10.9505 19.7622 12.1464 18.0337 10.7666 17.7471C10.6703 17.7342 10.574 17.7235 10.4778 17.7107V17.7085C6.4218 17.1331 3.34131 14.2965 3.34131 10.8844C3.34131 7.05519 7.21759 3.95117 11.9988 3.95117ZM6.97586 13.0942H7.00581H7.01436H8.72147C8.96962 13.0942 9.17284 12.891 9.17284 12.6429V12.6065C9.17284 12.3583 8.96962 12.1551 8.72147 12.1551H7.46574V9.29496C7.46574 9.04681 7.26251 8.84359 7.01436 8.84359H6.97586C6.72771 8.84359 6.52448 9.04681 6.52448 9.29496V12.6429C6.52448 12.891 6.72771 13.0942 6.97586 13.0942ZM17.7961 10.9957V10.9593C17.7961 10.709 17.5928 10.5058 17.3447 10.5058H16.0868V9.79126H17.3447C17.5928 9.79126 17.7961 9.58804 17.7961 9.33989V9.30352C17.7961 9.05323 17.5928 8.85 17.3447 8.85H15.6355H15.6269H15.5991C15.3488 8.85 15.1456 9.05323 15.1456 9.30352V12.6514C15.1456 12.8996 15.3488 13.1028 15.5991 13.1028H15.6269H15.6355H17.3447C17.5928 13.1028 17.7961 12.8996 17.7961 12.6514V12.6129C17.7961 12.3648 17.5928 12.1615 17.3447 12.1615H16.0868V11.447H17.3447C17.5928 11.447 17.7961 11.2438 17.7961 10.9957ZM14.4375 12.9595C14.5188 12.8782 14.568 12.7669 14.568 12.6429V9.29496C14.568 9.04681 14.3648 8.84359 14.1166 8.84359H14.0802C13.8299 8.84359 13.6267 9.04681 13.6267 9.29496V11.2609L11.9966 9.07248C11.9175 8.93557 11.7699 8.84359 11.603 8.84359H11.5666C11.3185 8.84359 11.1153 9.04681 11.1153 9.29496V12.6429C11.1153 12.891 11.3185 13.0942 11.5666 13.0942H11.603C11.8512 13.0942 12.0544 12.891 12.0544 12.6429V10.6405L13.6995 12.8867C13.708 12.9017 13.7208 12.9167 13.7315 12.9317C13.7765 12.9937 13.8385 13.0343 13.907 13.06C13.9604 13.0835 14.0182 13.0942 14.0781 13.0942H14.1166C14.1936 13.0942 14.2642 13.075 14.3284 13.0429C14.3733 13.0215 14.4118 12.9937 14.4375 12.9595ZM9.99003 13.0942H10.0264C10.2745 13.0942 10.4778 12.891 10.4778 12.6429V9.29496C10.4778 9.04681 10.2745 8.84359 10.0264 8.84359H9.99003C9.74188 8.84359 9.53865 9.04681 9.53865 9.29496V12.6429C9.53865 12.891 9.74188 13.0942 9.99003 13.0942Z"
        fill={color ?? '#15121A'}
      />
    </svg>
  )
}
