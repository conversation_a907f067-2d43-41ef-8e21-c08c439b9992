import { AssetProps } from './index'

export default function Transfer({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2232_127)">
        <path
          d="M98.5289 74.9073C97.1231 74.9073 95.981 76.057 95.981 77.4719C95.981 78.8869 97.1231 80.0366 98.5289 80.0366C99.9346 80.0366 101.077 78.8869 101.077 77.4719C101.077 76.057 99.9346 74.9073 98.5289 74.9073Z"
          fill="#15121A"
        />
        <path
          d="M53.4745 92.4489C56.4202 91.749 58.2446 88.7781 57.5493 85.8131C56.8541 82.8481 53.9025 81.0117 50.9568 81.7116C48.0111 82.4114 46.1868 85.3823 46.882 88.3473C47.5773 91.3123 50.5288 93.1487 53.4745 92.4489Z"
          fill="#15121A"
        />
        <path
          d="M76.2127 52.0136C79.2393 52.0136 81.6929 49.5439 81.6929 46.4974C81.6929 43.451 79.2393 40.9813 76.2127 40.9813C73.1861 40.9813 70.7325 43.451 70.7325 46.4974C70.7325 49.5439 73.1861 52.0136 76.2127 52.0136Z"
          fill="#15121A"
        />
        <path
          d="M33.4034 54.125C31.9977 54.125 30.8555 55.2747 30.8555 56.6896C30.8555 58.1046 31.9977 59.2543 33.4034 59.2543C34.8092 59.2543 35.9513 58.1046 35.9513 56.6896C35.9513 55.2747 34.8092 54.125 33.4034 54.125Z"
          fill="#15121A"
        />
        <path
          d="M35.1716 54.8547C34.908 53.3955 33.645 52.2901 32.1295 52.2901C30.4162 52.2901 29.0324 53.694 29.0324 55.4074C29.0324 57.1209 30.4272 58.5248 32.1295 58.5248C33.645 58.5248 34.908 57.4194 35.1716 55.9602H52.8203C53.1278 55.9602 53.3694 55.717 53.3694 55.4074C53.3694 55.0979 53.1278 54.8547 52.8203 54.8547H35.1716ZM32.1295 57.4194C31.0312 57.4194 30.1307 56.5129 30.1307 55.4074C30.1307 54.302 31.0312 53.3955 32.1295 53.3955C33.2277 53.3955 34.1283 54.302 34.1283 55.4074C34.1283 56.5129 33.2277 57.4194 32.1295 57.4194ZM117.957 73.4262L94.1799 53.9372C93.3232 53.2297 92.1701 53.086 91.1707 53.5724C90.1713 54.0478 89.5453 55.0427 89.5453 56.1591V63.2008C89.5453 64.1736 88.7655 64.9585 87.7991 64.9585H41.2009C39.6304 64.9585 38.3565 66.2408 38.3565 67.8216V74.8632C38.3565 75.5597 37.9831 76.1455 37.3571 76.444C36.7311 76.7425 36.0392 76.654 35.512 76.2229L11.7352 56.7119C11.3289 56.3802 11.0982 55.8828 11.0982 55.3522C11.0982 54.8216 11.3289 54.3241 11.7352 53.9925L35.512 34.5035C36.0502 34.0614 36.7421 33.984 37.3571 34.2824C37.9831 34.5809 38.3565 35.1668 38.3565 35.8632V42.9049C38.3565 44.4857 39.6304 45.768 41.2009 45.768H68.9314C69.2059 48.8522 71.7978 51.2841 74.9387 51.2841C78.2664 51.2841 80.9681 48.5648 80.9681 45.2153C80.9681 41.8658 78.2664 39.1464 74.9387 39.1464C71.7978 39.1464 69.2169 41.5784 68.9314 44.6625H41.2009C40.2345 44.6625 39.4547 43.8777 39.4547 42.9049V35.8632C39.4547 34.7467 38.8287 33.7629 37.8293 33.2765C36.8299 32.8012 35.6768 32.9338 34.8202 33.6413L11.0433 53.1413C10.3734 53.683 10 54.501 10 55.3632C10 56.2255 10.3844 57.0324 11.0433 57.5852L34.8202 77.0741C35.6768 77.7816 36.8299 77.9142 37.8293 77.4389C38.8287 76.9636 39.4547 75.9687 39.4547 74.8522V67.8105C39.4547 66.8377 40.2345 66.0529 41.2009 66.0529H87.7991C89.3696 66.0529 90.6435 64.7705 90.6435 63.1898V56.1481C90.6435 55.4517 91.0169 54.8658 91.6429 54.5673C92.2689 54.2688 92.9608 54.3573 93.488 54.7884L117.265 74.2773C117.671 74.609 117.902 75.1064 117.902 75.637C117.902 76.1676 117.671 76.6651 117.265 76.9967L93.488 96.4857C92.9498 96.9279 92.2579 97.0052 91.6429 96.7068C91.0169 96.4083 90.6435 95.8114 90.6435 95.126V88.0843C90.6435 86.5035 89.3696 85.2212 87.7991 85.2212H56.9386C56.6641 82.137 54.0722 79.7051 50.9313 79.7051C47.6036 79.7051 44.902 82.4245 44.902 85.7739C44.902 89.1234 47.6036 91.8428 50.9313 91.8428C54.0722 91.8428 56.6531 89.4108 56.9386 86.3267H87.7991C88.7655 86.3267 89.5453 87.1115 89.5453 88.0843V95.126C89.5453 96.2425 90.1713 97.2263 91.1707 97.7127C91.566 97.9006 91.9834 97.9891 92.4007 97.9891C93.0377 97.9891 93.6637 97.768 94.1908 97.3479L117.968 77.859C118.638 77.3173 119.011 76.4993 119.011 75.637C119.011 74.7748 118.627 73.9678 117.968 73.4151L117.957 73.4262ZM74.9387 40.2408C77.6624 40.2408 79.8698 42.4738 79.8698 45.2042C79.8698 47.9347 77.6514 50.1677 74.9387 50.1677C72.2261 50.1677 70.0077 47.9347 70.0077 45.2042C70.0077 42.4738 72.2261 40.2408 74.9387 40.2408ZM50.9423 90.7595C48.2186 90.7595 46.0112 88.5265 46.0112 85.796C46.0112 83.0656 48.2296 80.8326 50.9423 80.8326C53.6549 80.8326 55.8734 83.0656 55.8734 85.796C55.8734 88.5265 53.6549 90.7595 50.9423 90.7595ZM94.2128 76.7425C94.4764 78.2017 95.7393 79.3071 97.2549 79.3071C98.9682 79.3071 100.352 77.9032 100.352 76.1898C100.352 74.4763 98.9572 73.0724 97.2549 73.0724C95.7393 73.0724 94.4764 74.1779 94.2128 75.637H76.5641C76.2566 75.637 76.015 75.8802 76.015 76.1898C76.015 76.4993 76.2566 76.7425 76.5641 76.7425H94.2128ZM97.2549 74.1779C98.3531 74.1779 99.2537 75.0843 99.2537 76.1898C99.2537 77.2952 98.3531 78.2017 97.2549 78.2017C96.1567 78.2017 95.2561 77.2952 95.2561 76.1898C95.2561 75.0843 96.1567 74.1779 97.2549 74.1779Z"
          fill="url(#paint0_linear_2232_127)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2232_127"
          x1="49.3718"
          y1="91.887"
          x2="79.1386"
          y2="40.6525"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2232_127">
          <rect
            width="109"
            height="65"
            fill="white"
            transform="translate(10 33)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
