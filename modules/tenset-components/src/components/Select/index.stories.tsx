import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '.'

type SelectType = typeof Select

const meta: Meta<SelectType> = {
  title: 'Components/Select',
  component: Select,
}

export default meta

type SelectStory = StoryObj<SelectType>

export const Default: SelectStory = {
  args: {
    children: (
      <>
        <SelectTrigger>
          <SelectValue placeholder="Select option" />
        </SelectTrigger>

        <SelectContent>
          <SelectGroup>
            <SelectLabel>Options</SelectLabel>
            <SelectItem value="1">Option 1</SelectItem>
            <SelectItem value="2">Option 2</SelectItem>
          </SelectGroup>
        </SelectContent>
      </>
    ),
  },
}
