import { AssetProps } from './index'

export default function TelegramCommunity({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_964_1119)">
        <path
          d="M23.237 1.63601C23.2367 1.49634 23.2286 1.3568 23.213 1.21801C23.1842 1.03628 23.1438 0.856578 23.092 0.680011C23.0425 0.497016 22.938 0.333648 22.7926 0.212023C22.6471 0.0903975 22.4679 0.0163876 22.279 1.12037e-05C21.8978 -0.00961188 21.5171 0.0320956 21.147 0.124011C20.385 0.346011 19.631 0.604011 18.891 0.894011C16.822 1.70601 14.755 2.52301 12.701 3.37401C10.771 4.17401 8.853 5.00901 6.941 5.85401C5.332 6.56501 3.741 7.30901 2.141 8.04601C1.79706 8.19925 1.46307 8.37393 1.141 8.56901C1.0291 8.61828 0.933868 8.69889 0.8668 8.80112C0.799732 8.90334 0.763705 9.0228 0.76307 9.14507C0.762435 9.26733 0.797221 9.38716 0.863224 9.49007C0.929227 9.59299 1.02362 9.67459 1.135 9.72501C1.46084 9.92603 1.811 10.0846 2.177 10.197C3.114 10.475 4.064 10.708 5.007 10.966C5.45776 11.0849 5.92792 11.1114 6.38917 11.0439C6.85043 10.9763 7.29326 10.8162 7.691 10.573C8.881 9.89901 10.046 9.17301 11.211 8.45901C12.959 7.38101 14.698 6.28901 16.441 5.20401C16.597 5.10401 16.753 5.00401 16.914 4.91901C16.993 4.87851 17.084 4.86795 17.1702 4.88927C17.2564 4.91059 17.332 4.96235 17.383 5.03501C17.49 5.19501 17.413 5.31101 17.293 5.41801C17.254 5.45201 17.217 5.48701 17.179 5.51801C16.379 6.25301 15.569 6.98401 14.771 7.72601C14.211 8.24701 13.665 8.78501 13.114 9.31601C12.283 10.0553 11.5313 10.879 10.871 11.774C10.7599 11.8886 10.676 12.0266 10.6255 12.178C10.5751 12.3294 10.5593 12.4902 10.5795 12.6485C10.5997 12.8068 10.6552 12.9586 10.7421 13.0925C10.8289 13.2264 10.9447 13.339 11.081 13.422C11.422 13.7 11.781 13.953 12.142 14.213C13.897 15.495 15.711 16.687 17.567 17.813C17.9745 18.1065 18.4355 18.3174 18.924 18.434C19.1893 18.5093 19.4719 18.495 19.7283 18.3934C19.9846 18.2918 20.2003 18.1086 20.342 17.872C20.5069 17.5871 20.6238 17.2769 20.688 16.954C21.13 14.708 21.575 12.464 21.974 10.211C22.328 8.21101 22.635 6.20301 22.943 4.19601C23.073 3.34601 23.155 2.48901 23.26 1.63401H23.232"
          fill={color ?? 'currentColor'}
        />
        <path
          d="M4.568 14.493C3.42686 14.4278 2.30623 14.8165 1.4505 15.5742C0.594775 16.332 0.0733722 17.3974 0 18.538C0.073881 19.6784 0.595468 20.7433 1.45111 21.5008C2.30675 22.2583 3.42708 22.6469 4.568 22.582C4.91373 22.5811 5.25848 22.5452 5.597 22.475C6.17922 23.1432 6.91985 23.6545 7.751 23.962C7.46376 23.2202 7.3802 22.415 7.509 21.63C8.00402 21.2786 8.40935 20.8156 8.69217 20.2784C8.975 19.7412 9.12739 19.145 9.137 18.538C9.06338 17.3974 8.54173 16.3321 7.68581 15.5745C6.82989 14.817 5.70915 14.4286 4.568 14.494M2.159 19.311C2.00592 19.311 1.85627 19.2656 1.72899 19.1806C1.6017 19.0955 1.5025 18.9746 1.44392 18.8332C1.38533 18.6918 1.37001 18.5362 1.39987 18.386C1.42974 18.2359 1.50345 18.098 1.6117 17.9897C1.71995 17.8815 1.85786 17.8078 2.008 17.7779C2.15814 17.748 2.31377 17.7633 2.4552 17.8219C2.59663 17.8805 2.71751 17.9797 2.80256 18.107C2.88761 18.2343 2.933 18.3839 2.933 18.537C2.933 18.7423 2.85145 18.9392 2.7063 19.0843C2.56115 19.2295 2.36428 19.311 2.159 19.311ZM4.568 19.311C4.41496 19.3108 4.26541 19.2653 4.13826 19.1801C4.01111 19.0949 3.91206 18.974 3.85363 18.8325C3.7952 18.6911 3.78002 18.5355 3.80999 18.3854C3.83997 18.2353 3.91377 18.0975 4.02205 17.9894C4.13034 17.8812 4.26825 17.8076 4.41837 17.7778C4.56848 17.748 4.72406 17.7634 4.86543 17.822C5.0068 17.8806 5.12762 17.9798 5.21263 18.1071C5.29763 18.2344 5.343 18.384 5.343 18.537C5.343 18.6387 5.32295 18.7395 5.28399 18.8334C5.24503 18.9274 5.18793 19.0128 5.11595 19.0847C5.04397 19.1566 4.95853 19.2135 4.8645 19.2524C4.77048 19.2912 4.66973 19.3111 4.568 19.311ZM6.977 19.311C6.82392 19.311 6.67427 19.2656 6.54699 19.1806C6.4197 19.0955 6.3205 18.9746 6.26192 18.8332C6.20333 18.6918 6.18801 18.5362 6.21787 18.386C6.24774 18.2359 6.32145 18.098 6.4297 17.9897C6.53794 17.8815 6.67586 17.8078 6.826 17.7779C6.97614 17.748 7.13177 17.7633 7.2732 17.8219C7.41463 17.8805 7.53551 17.9797 7.62056 18.107C7.70561 18.2343 7.751 18.3839 7.751 18.537C7.751 18.6387 7.73098 18.7393 7.69208 18.8332C7.65319 18.9271 7.59617 19.0124 7.5243 19.0843C7.45243 19.1562 7.3671 19.2132 7.2732 19.2521C7.17929 19.291 7.07864 19.311 6.977 19.311Z"
          fill={color ?? 'currentColor'}
        />
      </g>
      <defs>
        <clipPath id="clip0_964_1119">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
