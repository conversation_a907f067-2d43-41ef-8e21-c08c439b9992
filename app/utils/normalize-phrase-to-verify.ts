export default function normalizePhraseToVerify(phrase: string) {
  let normalizedPhrase = phrase.replaceAll(' ', '')

  const emailRegexExp =
    /^[\w!#$%&'*+./=?^`{|}~-]+@[\da-z](?:[\da-z-]{0,61}[\da-z])?(?:\.[\da-z](?:[\da-z-]{0,61}[\da-z])?)*$/gi

  if (emailRegexExp.test(normalizedPhrase)) {
    return normalizedPhrase
  }

  if (normalizedPhrase[0] === '@') {
    return normalizedPhrase.replace('@', '')
  }

  // If there is / at the end of the url we're removing it
  if (
    normalizedPhrase.slice(0, 8) === 'https://' &&
    normalizedPhrase.at(-1) === '/'
  ) {
    normalizedPhrase = normalizedPhrase.replace(/\/+$/, '')
  }

  normalizedPhrase = normalizedPhrase
    .replace('https://www.', '')
    .replace('https://', '')
    .replace('t.me/', '')
    .replace('twitter.com/', '')
    .replace('linkedin.com/in/', '')

  return normalizedPhrase
}
