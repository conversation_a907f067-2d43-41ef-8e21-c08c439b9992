import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  Button,
  ButtonVariant,
  Card,
  H1,
  Icon,
  IconName,
  TextL,
  TextS,
} from '~/tenset-components'
import urls from '~/utils/urls'

export function CallToActionContactSection() {
  const { t } = useTranslation([Namespace.CONTACT_PAGE])
  const { supportLink, launchYourProjectLink } = urls

  return (
    <div className="flex flex-col items-center md:text-center">
      <Card
        isLarge
        title={<H1 isBold>{t('hero.title')}</H1>}
        action={
          <div>
            <Button
              variant={ButtonVariant.Secondary}
              className="w-full my-2 md:w-auto md:mx-3 md:my-0"
              to={launchYourProjectLink}
            >
              <Icon name={IconName.Telegram} />
              <TextS> {t('callToAction.launchYourProjectBtn')} </TextS>
            </Button>
            <Button
              variant={ButtonVariant.Secondary}
              className="w-full my-2 md:w-auto md:mx-3 md:my-0"
              to={supportLink}
            >
              <Icon name={IconName.Telegram} />
              <TextS>{t('callToAction.getHelpBtn')}</TextS>
            </Button>
          </div>
        }
      >
        <TextL>{t('callToAction.description')}</TextL>
      </Card>
    </div>
  )
}
