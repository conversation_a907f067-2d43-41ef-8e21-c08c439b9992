import type { Dispatch, SetStateAction } from 'react'
import { useTranslation } from 'react-i18next'

import type { Voting } from './voting'

import { TokensAvailableOnExchanges } from '~/components/blocks/available-on-exchanges'
import { Namespace } from '~/i18n'
import { Card, Modal, ModalContent, ModalMain, Text } from '~/tenset-components'

interface GovernanceNoRightsToVoteModalProps {
  isOpen: boolean
  setIsOpen: Dispatch<SetStateAction<boolean>>
  voting: Voting
}

export function GovernanceNoRightsToVoteModal({
  isOpen,
  setIsOpen,
  voting,
}: GovernanceNoRightsToVoteModalProps) {
  const { t } = useTranslation([Namespace.GOVERNANCE, Namespace.COMMON])

  return (
    <Modal open={isOpen} onClose={() => setIsOpen(false)}>
      <ModalContent>
        <ModalMain>
          <Card title={t('no-rights-to-vote.title')} limitWidth={false}>
            <p>{t('no-rights-to-vote.available-to')}</p>

            <ul className="mt-4 flex list-disc flex-col gap-4 pl-4">
              {voting.require('infinity') && (
                <li>
                  <Text>{t('no-rights-to-vote.infinity-stakers')}</Text>
                </li>
              )}

              {voting.require('tglp') && (
                <li>
                  <Text>{t('no-rights-to-vote.tglp-subscribers')}</Text>
                </li>
              )}

              {voting.require('balance') && (
                <li>
                  <Text>{t('no-rights-to-vote.tenset-holders')}</Text>

                  <Text className="mt-4">
                    {t('no-rights-to-vote.description')}
                  </Text>

                  <div className="mt-4 flex flex-col flex-wrap gap-4 md:flex-row">
                    <TokensAvailableOnExchanges />
                  </div>
                </li>
              )}
            </ul>
          </Card>
        </ModalMain>
      </ModalContent>
    </Modal>
  )
}
