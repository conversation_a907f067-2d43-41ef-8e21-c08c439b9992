import { AssetProps } from './index'

export function LifetimeAccess({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '112'}
      height={size ? (size * 117) / 112 : '117'}
      viewBox="0 0 112 117"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M44.9223 46.4806C44.6317 46.2699 44.209 46.3358 43.9977 46.6387L35.2925 58.8486C35.2132 58.9539 35.1736 59.0988 35.1736 59.2305V65.3684C35.1736 65.7372 35.4642 66.027 35.8341 66.027C36.2039 66.027 36.4945 65.7372 36.4945 65.3684V59.4413L45.0808 47.4026C45.2922 47.0997 45.2262 46.6914 44.9223 46.4806ZM58.9907 64.2488L44.2354 77.8022C43.9712 78.0525 43.9448 78.4608 44.1958 78.7374C44.3279 78.8823 44.4996 78.9481 44.6846 78.9481C44.8431 78.9481 45.0016 78.8954 45.1337 78.7769L59.8889 65.2235C60.1531 64.9733 60.1796 64.565 59.9286 64.2884C59.6776 64.0118 59.2681 64.0118 58.9907 64.2488ZM7.77664 60.4028H5.24038V57.8739C5.24038 57.5051 4.94976 57.2153 4.57989 57.2153C4.21002 57.2153 3.9194 57.5051 3.9194 57.8739V60.4028H1.38314C1.01327 60.4028 0.722656 60.6926 0.722656 61.0614C0.722656 61.4302 1.01327 61.7199 1.38314 61.7199H3.9194V64.2488C3.9194 64.6176 4.21002 64.9074 4.57989 64.9074C4.94976 64.9074 5.24038 64.6176 5.24038 64.2488V61.7199H7.77664C8.14651 61.7199 8.43712 61.4302 8.43712 61.0614C8.43712 60.6926 8.14651 60.4028 7.77664 60.4028ZM86.6122 27.7641C86.9821 27.7641 87.2727 27.4743 87.2727 27.1055V25.301C87.2727 24.9322 86.9821 24.6425 86.6122 24.6425C86.2423 24.6425 85.9517 24.9322 85.9517 25.301V27.1055C85.9517 27.4743 86.2423 27.7641 86.6122 27.7641ZM89.0031 28.831C88.6333 28.831 88.3427 29.1207 88.3427 29.4895C88.3427 29.8583 88.6333 30.1481 89.0031 30.1481H90.8129C91.1828 30.1481 91.4734 29.8583 91.4734 29.4895C91.4734 29.1207 91.1828 28.831 90.8129 28.831H89.0031ZM62.9932 55.3977L48.238 68.951C47.9738 69.2013 47.9474 69.6096 48.1983 69.8862C48.3304 70.0311 48.5022 70.0969 48.6871 70.0969C48.8456 70.0969 49.0041 70.0443 49.1362 69.9257L63.8915 56.3724C64.1557 56.1221 64.1821 55.7138 63.9311 55.4372C63.6801 55.1606 63.2706 55.1606 62.9932 55.3977ZM86.6122 34.3366C86.9821 34.3366 87.2727 34.0468 87.2727 33.6781V31.8736C87.2727 31.5048 86.9821 31.215 86.6122 31.215C86.2423 31.215 85.9517 31.5048 85.9517 31.8736V33.6781C85.9517 34.0468 86.2423 34.3366 86.6122 34.3366ZM87.2991 19.3476C91.8829 19.3476 95.2117 22.6668 95.2117 27.2372C95.2117 27.606 95.5023 27.8958 95.8722 27.8958C96.2421 27.8958 96.5327 27.606 96.5327 27.2372C96.5327 22.6668 99.8615 19.3476 104.445 19.3476C104.815 19.3476 105.106 19.0578 105.106 18.689C105.106 18.3202 104.815 18.0304 104.445 18.0304C99.8615 18.0304 96.5327 14.7112 96.5327 10.1408C96.5327 9.77197 96.2421 9.4822 95.8722 9.4822C95.5023 9.4822 95.2117 9.77197 95.2117 10.1408C95.2117 14.7112 91.8829 18.0304 87.2991 18.0304C86.9292 18.0304 86.6386 18.3202 86.6386 18.689C86.6386 19.0578 86.9292 19.3476 87.2991 19.3476ZM95.8722 13.7497C96.7705 16.0416 98.5274 17.7934 100.826 18.689C98.5274 19.5847 96.7705 21.3365 95.8722 23.6283C94.9739 21.3365 93.2171 19.5847 90.9186 18.689C93.2171 17.7934 94.9739 16.0416 95.8722 13.7497ZM97.2724 84.1376C92.5698 84.1376 90.0996 86.9563 88.1313 90.0121C87.9332 90.315 88.0256 90.7233 88.3295 90.9209C88.4351 90.9868 88.5672 91.0263 88.6861 91.0263C88.8975 91.0263 89.122 90.9209 89.2409 90.7233C91.2752 87.5622 93.4284 85.4548 97.2592 85.4548C101.09 85.4548 104.313 88.6027 104.313 92.4751C104.313 96.3475 101.156 99.5087 97.2592 99.5087C92.147 99.5087 89.9807 95.8997 87.6954 92.0668C85.3573 88.1681 82.9531 84.1376 77.2994 84.1376C72.6892 84.1376 68.9244 87.8783 68.9244 92.4751C68.9244 97.0719 72.676 100.826 77.2994 100.826C81.2358 100.826 83.6004 98.9291 85.3308 96.769L85.5026 97.9149C85.5554 98.2442 85.8328 98.4681 86.1499 98.4681C86.1895 98.4681 86.2159 98.4681 86.2555 98.4681C86.6122 98.4154 86.8632 98.073 86.8103 97.7173L86.3876 95.004C86.3876 94.9382 86.348 94.8855 86.3216 94.8328C86.3216 94.8065 86.3084 94.7801 86.2952 94.767C86.2687 94.7274 86.2291 94.7011 86.2027 94.6616C86.1763 94.6352 86.1498 94.6089 86.1234 94.5826C86.0838 94.5562 86.0442 94.5562 86.0045 94.5299C85.9649 94.5167 85.9385 94.4904 85.8989 94.4772C85.8592 94.4772 85.8064 94.4772 85.7668 94.4772C85.7271 94.4772 85.7007 94.4772 85.6611 94.4772L82.9399 94.8855C82.5832 94.9382 82.3322 95.2806 82.3851 95.6363C82.4379 95.9919 82.7814 96.2422 83.138 96.1895L84.2741 96.0182C82.7153 97.9544 80.681 99.535 77.3126 99.535C73.4289 99.535 70.2586 96.387 70.2586 92.5015C70.2586 88.6159 73.4157 85.4811 77.3126 85.4811C82.2134 85.4811 84.3401 89.0242 86.5726 92.7781C88.9503 96.7558 91.4073 100.865 97.2724 100.865C101.883 100.865 105.647 97.1246 105.647 92.5147C105.647 87.9047 101.896 84.1772 97.2724 84.1772V84.1376ZM82.3851 30.1349H84.1948C84.5647 30.1349 84.8553 29.8452 84.8553 29.4764C84.8553 29.1076 84.5647 28.8178 84.1948 28.8178H82.3851C82.0152 28.8178 81.7246 29.1076 81.7246 29.4764C81.7246 29.8452 82.0152 30.1349 82.3851 30.1349ZM89.7165 68.8457V52.7502C89.7165 52.7502 89.7165 52.7239 89.7033 52.7107C89.7033 52.658 89.7033 52.6053 89.69 52.5527C89.6768 52.5 89.6372 52.4604 89.6108 52.4078C89.6108 52.4078 89.6108 52.3814 89.5976 52.3683L53.0067 1.09203C53.0067 1.09203 52.9803 1.07886 52.9803 1.06569C52.8614 0.920801 52.6764 0.81543 52.4783 0.81543C52.2802 0.81543 52.0952 0.920801 51.9763 1.06569C51.9763 1.06569 51.9499 1.07886 51.9499 1.09203L15.359 52.3946C15.359 52.3946 15.359 52.4209 15.3458 52.4341C15.3194 52.4736 15.2798 52.5263 15.2665 52.5658C15.2533 52.6185 15.2533 52.6712 15.2533 52.7239C15.2533 52.737 15.2401 52.7502 15.2401 52.7634V87.8915C15.2401 87.8915 15.2401 87.9047 15.2401 87.9178C15.2401 88.01 15.2665 88.1022 15.3062 88.1944C15.359 88.2866 15.4251 88.3788 15.5175 88.4447L52.1084 113.167C52.1084 113.167 52.1348 113.167 52.1481 113.167C52.2405 113.22 52.3594 113.273 52.4783 113.273C52.5972 113.273 52.7161 113.233 52.8085 113.167C52.8085 113.167 52.835 113.167 52.8482 113.167L66.441 103.987C70.5096 111.31 78.3297 116.276 87.2991 116.276C100.443 116.276 111.129 105.62 111.129 92.5147C111.129 80.2257 101.737 70.0969 89.7297 68.872L89.7165 68.8457ZM53.1388 3.54191L87.9992 52.4341L73.7327 56.9782L53.1388 28.1066V3.54191ZM88.4087 53.6854V68.8061C88.0388 68.793 87.669 68.7535 87.2991 68.7535C82.5964 68.7535 78.0655 70.1365 74.1422 72.7444V58.2295L88.4087 53.6854ZM51.8178 3.54191V28.0934L31.2239 56.9651L16.9574 52.4209L51.8178 3.54191ZM16.5479 53.6854L30.8144 58.2295V79.2774L16.5479 86.8246V53.6986V53.6854ZM51.8178 111.389L17.1687 87.9837L31.4352 80.4365L51.8178 94.2006V111.389ZM52.4783 93.0547L32.1486 79.3169V57.9529L52.4783 29.4369L72.808 57.9529V72.7444L65.186 70.716C64.9615 70.6633 64.7105 70.716 64.552 70.8872C64.3935 71.0585 64.3142 71.2955 64.3802 71.5195L66.7844 80.4497C65.9786 81.8195 65.3049 83.2552 64.7898 84.7435L52.4783 93.0678V93.0547ZM53.1388 111.389V94.2006L64.1953 86.7324C63.7198 88.6159 63.4556 90.5653 63.4556 92.5147C63.4556 96.2026 64.3274 99.6799 65.8333 102.802L53.1256 111.389H53.1388ZM87.2991 114.959C74.882 114.959 64.7898 104.883 64.7898 92.5147C64.7898 88.4315 65.9258 84.4142 68.0658 80.8843C68.1582 80.7263 68.1846 80.5419 68.145 80.3706L65.9654 72.2702L73.0062 74.1537C73.1251 74.2854 73.2968 74.3645 73.4817 74.3645C73.5478 74.3645 73.6006 74.3513 73.6535 74.325H73.7063C73.8913 74.3908 74.1026 74.3513 74.2611 74.2328C78.1051 71.5063 82.6096 70.0574 87.3123 70.0574C99.7294 70.0574 109.822 80.1204 109.822 92.5015C109.822 104.883 99.7162 114.946 87.3123 114.946L87.2991 114.959Z"
        fill="url(#paint0_linear_2932_19482)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2932_19482"
          x1="40.5103"
          y1="106.503"
          x2="96.2758"
          y2="9.63168"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
