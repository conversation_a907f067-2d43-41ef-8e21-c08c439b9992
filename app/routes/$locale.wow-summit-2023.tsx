// import { redirect } from '@remix-run/node'
import { Widget } from '@typeform/embed-react'

// export async function loader({ request }: LoaderFunctionArgs) {
// return redirect('https://external-url.com')
// return redirect('/')
//   return null
// }

export default function WowSummit2023() {
  return (
    <div className="container">
      <Widget
        id="Pmg3qiI7"
        className="h-[70vh] min-h-[400px] lg:h-[80vh] lg:min-h-[600px]"
      />
    </div>
  )
}
