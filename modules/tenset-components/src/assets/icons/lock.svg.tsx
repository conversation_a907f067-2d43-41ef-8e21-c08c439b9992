import { AssetProps } from './index'

export default function Lock({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2040_102)">
        <path
          d="M90.895 58.5445V40.4989C90.895 27.1724 80.031 16.3391 66.6668 16.3391C53.3026 16.3391 42.4535 27.1724 42.4535 40.4989V58.5297C36.203 64.7329 32.3336 73.3105 32.3336 82.7637C32.3336 101.64 47.7367 117 66.6668 117C85.597 117 101 101.64 101 82.7637C101 73.3105 97.1306 64.7329 90.895 58.5297V58.5445ZM47.6325 54.3002V40.4989C47.6325 30.0217 56.1749 21.5034 66.6668 21.5034C77.1588 21.5034 85.716 30.0217 85.716 40.4989V54.3151C80.2691 50.6792 73.7061 48.5422 66.6668 48.5422C59.6275 48.5422 53.0794 50.6644 47.6325 54.3002Z"
          fill="#15121A"
        />
        <path
          d="M64.8214 54.2557C67.143 54.2557 69.4349 54.5525 71.6672 55.1313C71.7267 55.1461 71.8012 55.161 71.8607 55.161C72.1881 55.161 72.4857 54.9384 72.575 54.6119C72.6792 54.2112 72.4411 53.8105 72.0393 53.7066C69.6879 53.0833 67.2621 52.7717 64.8214 52.7717H64.747C64.3303 52.7717 64.0475 53.0982 64.0475 53.5137C64.0475 53.9292 64.4196 54.2557 64.8363 54.2557H64.8214ZM51.4721 104.03C49.4481 102.872 47.5878 101.447 45.9359 99.8002C45.6383 99.5183 45.1769 99.5034 44.8793 99.8002C44.5965 100.097 44.5965 100.557 44.8793 100.854C46.6205 102.59 48.5998 104.089 50.728 105.321C50.847 105.395 50.981 105.425 51.1 105.425C51.353 105.425 51.606 105.291 51.74 105.054C51.9483 104.697 51.8293 104.252 51.4721 104.045V104.03ZM39.0008 87.8687C38.8967 87.468 38.4948 87.2454 38.093 87.3493C37.6912 87.4532 37.468 87.8687 37.5721 88.2546C38.2121 90.629 39.1645 92.8995 40.3998 95.0365C40.5337 95.274 40.7867 95.4075 41.0397 95.4075C41.1736 95.4075 41.2927 95.3778 41.4117 95.3036C41.7689 95.0959 41.888 94.6358 41.6796 94.2945C40.5039 92.2763 39.5961 90.1244 39.0008 87.8687ZM38.0781 80.9235C38.0781 78.6084 38.3758 76.2934 38.9711 74.0822C39.0752 73.6815 38.8371 73.2808 38.4353 73.1769C38.0335 73.0731 37.6317 73.3105 37.5275 73.7112C36.9024 76.0559 36.5899 78.4897 36.5899 80.9384V80.9977C36.5899 81.4132 36.9173 81.71 37.334 81.71C37.7507 81.71 38.0781 81.3539 38.0781 80.9384V80.9235ZM64.8214 107.576C62.4849 107.576 60.1633 107.28 57.931 106.686C57.5291 106.582 57.1273 106.82 57.0231 107.205C56.919 107.606 57.1571 108.007 57.544 108.111C59.8954 108.734 62.351 109.061 64.8214 109.061H64.8512C65.2679 109.061 65.5804 108.734 65.5804 108.318C65.5804 107.903 65.2381 107.576 64.8214 107.576ZM92.0558 73.5331C91.4159 71.1587 90.4485 68.8881 89.2133 66.7511C89.005 66.395 88.5436 66.2763 88.2013 66.484C87.8441 66.6918 87.7251 67.1518 87.9334 67.4931C89.1091 69.5114 90.0169 71.6632 90.6271 73.9041C90.7164 74.2306 91.014 74.4532 91.3415 74.4532C91.401 74.4532 91.4754 74.4532 91.5349 74.4235C91.9367 74.3196 92.16 73.9041 92.0558 73.5183V73.5331ZM90.5229 56.0959V38.6438C90.5229 24.5011 78.9893 13 64.8214 13C50.6536 13 39.105 24.5011 39.105 38.6438V56.0959C32.8545 62.5217 29 71.2774 29 80.9235C29 100.616 45.0728 116.644 64.8214 116.644C84.5701 116.644 100.643 100.616 100.643 80.9235C100.643 71.2774 96.7883 62.5217 90.5378 56.0959H90.5229ZM40.5932 38.6438C40.5932 25.3174 51.4572 14.484 64.8214 14.484C78.1856 14.484 89.0496 25.3174 89.0496 38.6438V54.6416C87.4423 53.1724 85.716 51.8516 83.8706 50.6941V38.6438C83.8706 28.1815 75.3282 19.6632 64.8363 19.6632C54.3444 19.6632 45.802 28.1815 45.802 38.6438V50.6941C43.9566 51.8516 42.2154 53.1724 40.623 54.6416V38.6438H40.5932ZM82.3675 38.6438V49.8036C77.1736 46.8801 71.191 45.2032 64.8214 45.2032C58.4518 45.2032 52.4543 46.8801 47.2753 49.8036V38.6438C47.2753 28.9977 55.148 21.1473 64.8214 21.1473C74.4948 21.1473 82.3675 28.9977 82.3675 38.6438ZM64.8214 115.145C45.8913 115.145 30.4882 99.7854 30.4882 80.9087C30.4882 62.032 45.8913 46.6724 64.8214 46.6724C83.7515 46.6724 99.1546 62.032 99.1546 80.9087C99.1546 99.7854 83.7515 115.145 64.8214 115.145ZM78.8553 56.4966C78.4981 56.2888 78.0517 56.4075 77.8433 56.7637C77.635 57.1199 77.7689 57.5651 78.1112 57.7728C80.1352 58.9304 81.9954 60.355 83.6623 61.9874C83.8111 62.1358 84.0045 62.21 84.1831 62.21C84.3617 62.21 84.5701 62.1358 84.704 61.9874C85.0017 61.6906 84.9868 61.2306 84.704 60.9338C82.9479 59.1975 80.9835 57.6986 78.8553 56.4817V56.4966ZM78.2005 104C76.1765 105.172 74.0186 106.063 71.7565 106.671C71.3547 106.775 71.1315 107.191 71.2356 107.576C71.3249 107.903 71.6226 108.126 71.95 108.126C72.0095 108.126 72.0839 108.126 72.1434 108.096C74.5246 107.458 76.8165 106.508 78.9446 105.276C79.3018 105.068 79.4208 104.623 79.2125 104.267C79.0041 103.911 78.5428 103.792 78.2005 104ZM64.8214 69.6005C60.9223 69.6005 57.7524 72.7614 57.7524 76.6495C57.7524 79.113 59.0322 81.3687 61.1157 82.645V90.629C61.1157 92.6621 62.7826 94.3242 64.8214 94.3242C66.8603 94.3242 68.5271 92.6621 68.5271 90.629V82.645C70.6106 81.3687 71.8904 79.113 71.8904 76.6495C71.8904 72.7614 68.7205 69.6005 64.8214 69.6005ZM67.4258 81.5468C67.1877 81.6804 67.024 81.9326 67.024 82.1998V90.6142C67.024 91.831 66.0269 92.8253 64.8065 92.8253C63.5862 92.8253 62.5891 91.831 62.5891 90.6142V82.1998C62.5891 81.9326 62.4403 81.6804 62.1873 81.5468C60.3568 80.5822 59.2257 78.6975 59.2257 76.6347C59.2257 73.5628 61.7259 71.0696 64.8065 71.0696C67.8871 71.0696 70.3873 73.5628 70.3873 76.6347C70.3873 78.6975 69.2563 80.5822 67.4258 81.5468Z"
          fill="url(#paint0_linear_2040_102)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2040_102"
          x1="42.1112"
          y1="109.075"
          x2="91.0145"
          y2="24.1292"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2040_102">
          <rect
            width="72"
            height="104"
            fill="white"
            transform="translate(29 13)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
