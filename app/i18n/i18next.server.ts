import { resolve } from 'node:path'

import Backend from 'i18next-fs-backend'
import { RemixI18Next } from 'remix-i18next/server'

import { i18nSharedConfig } from './shared-i18n-config'

const i18next = new RemixI18Next({
  detection: {
    supportedLanguages: i18nSharedConfig.supportedLngs,
    fallbackLanguage: i18nSharedConfig.fallbackLng,
  },
  i18next: {
    ...i18nSharedConfig,
    backend: {
      loadPath: resolve('./public/locales/{{lng}}/{{ns}}.json'),
    },
  },
  plugins: [Backend],
})

export default i18next
