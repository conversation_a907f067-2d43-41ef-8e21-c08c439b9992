import { AssetProps } from './index'

export default function Job({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M110.37 16.6342H68.0771C62.7617 16.6342 58.4472 20.9512 58.4472 26.2696V39.5182H45.0573C41.0809 39.5182 37.8214 42.7796 37.8214 46.7582V50.0738H17.3444C13.138 50.0738 9.71619 53.4841 9.71619 57.7063V73.9863C10.5412 74.3246 11.4068 74.6359 12.2589 74.9607H12.1778V106.614C12.1778 109.578 14.5852 111.987 17.5472 111.987H89.5144C92.4764 111.987 94.8839 109.578 94.8839 106.614V74.9607H94.8027C95.6548 74.6359 96.5204 74.3246 97.3454 73.9863V57.7063C97.3454 57.1109 97.2778 56.5154 97.1425 55.9606H110.343C115.658 55.9606 119.973 51.6436 119.973 46.3252V26.2696C119.973 20.9512 115.658 16.6342 110.343 16.6342H110.37ZM42.3929 47.3537C42.3929 45.5674 43.8536 44.1058 45.6389 44.1058H58.4472V46.3387C58.4472 47.6649 58.7177 48.937 59.2046 50.0873H42.3929V47.3537Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M101.254 27.6229C98.5897 27.6229 96.4392 29.7881 96.4392 32.4405C96.4392 35.093 98.6033 37.2582 101.254 37.2582C103.905 37.2582 106.069 35.093 106.069 32.4405C106.069 29.7881 103.905 27.6229 101.254 27.6229ZM101.254 35.9184C99.3471 35.9184 97.7917 34.3622 97.7917 32.454C97.7917 30.5459 99.3471 28.9896 101.254 28.9896C103.161 28.9896 104.717 30.5459 104.717 32.454C104.717 34.3622 103.161 35.9184 101.254 35.9184ZM107.165 12.7638H64.8716C59.1911 12.7638 54.5655 17.392 54.5655 23.0758V35.6478H41.8519C37.4832 35.6478 33.9397 39.1934 33.9397 43.5645V46.2034H14.1389C9.56741 46.2034 5.83447 49.9249 5.83447 54.5126V70.7926C5.83447 70.8873 5.848 70.9685 5.88857 71.0497C5.9021 71.0903 5.94267 71.1309 5.96972 71.1715C5.99677 71.1986 6.0103 71.2392 6.03735 71.2662C6.10497 71.3339 6.1726 71.3745 6.25375 71.4151C6.91648 71.6857 7.61979 71.9293 8.30957 72.2V103.42C8.30957 106.763 11.0281 109.469 14.3553 109.469H86.3224C89.6632 109.469 92.3682 106.749 92.3682 103.42V72.2C93.0444 71.9429 93.7477 71.6993 94.424 71.4151C94.5051 71.3745 94.5863 71.3339 94.6404 71.2662C94.6674 71.2392 94.6674 71.2121 94.6945 71.185C94.7351 71.1444 94.7621 71.1038 94.7892 71.0497C94.8297 70.9685 94.8433 70.8873 94.8433 70.7926V54.5126C94.8433 54.1472 94.8162 53.7953 94.7621 53.4435H107.165C112.845 53.4435 117.471 48.8153 117.471 43.1315V23.0758C117.471 17.392 112.845 12.7638 107.165 12.7638ZM55.0389 46.2169H39.8772V44.16C39.8772 42.739 41.0268 41.5887 42.447 41.5887H54.579V43.145C54.579 44.2141 54.7413 45.2561 55.0524 46.2169H55.0389ZM35.2922 43.578C35.2922 39.9648 38.2407 37.0146 41.8519 37.0146H54.5655V40.2354H42.4334C40.2694 40.2354 38.5112 41.9947 38.5112 44.16V46.2169H35.2922V43.578ZM91.0157 103.434C91.0157 106.019 88.9057 108.13 86.3224 108.13H14.3553C11.772 108.13 9.66208 106.019 9.66208 103.434V72.7278C20.1576 76.5305 32.4654 78.7634 45.5983 79.1964V84.4336C45.5983 84.8126 45.8959 85.1103 46.2746 85.1103H54.5384C54.9171 85.1103 55.2147 84.8126 55.2147 84.4336V79.1964C68.2935 78.7499 80.5608 76.5169 91.0157 72.7278V103.434ZM46.9508 78.5604V73.3503H53.8622V78.5604V83.7435H46.9508V78.5739V78.5604ZM93.4772 70.3325C82.576 74.7577 69.3755 77.3424 55.2147 77.8296V72.6601C55.2147 72.2812 54.9171 71.9835 54.5384 71.9835H46.2746C45.8959 71.9835 45.5983 72.2812 45.5983 72.6601V77.8296C31.3834 77.356 18.1288 74.7577 7.18698 70.3325V54.5126C7.18698 50.6828 10.3113 47.5567 14.1389 47.5567H55.5799C57.2434 51.0346 60.7735 53.4435 64.8716 53.4435H66.8869V63.7826C66.8869 64.0668 67.0627 64.3104 67.3197 64.4186C67.4008 64.4457 67.482 64.4592 67.5631 64.4592C67.7525 64.4592 67.9283 64.378 68.0635 64.2291L77.7746 53.4435H93.3961C93.4502 53.7953 93.4908 54.1607 93.4908 54.5126V70.3325H93.4772ZM116.118 43.1315C116.118 48.0709 112.101 52.0902 107.165 52.0902H77.477C77.2877 52.0902 77.0983 52.1714 76.9766 52.3203L68.2529 62.0233V52.7668C68.2529 52.3879 67.9553 52.0902 67.5766 52.0902H64.8851C59.9485 52.0902 55.9315 48.0709 55.9315 43.1315V23.0758C55.9315 18.1363 59.9485 14.1171 64.8851 14.1171H107.178C112.115 14.1171 116.132 18.1363 116.132 23.0758V43.1315H116.118ZM86.0249 27.6229C83.3605 27.6229 81.21 29.7881 81.21 32.4405C81.21 35.093 83.374 37.2582 86.0249 37.2582C88.6758 37.2582 90.8398 35.093 90.8398 32.4405C90.8398 29.7881 88.6758 27.6229 86.0249 27.6229ZM86.0249 35.9184C84.1179 35.9184 82.5625 34.3622 82.5625 32.454C82.5625 30.5459 84.1179 28.9896 86.0249 28.9896C87.9319 28.9896 89.4873 30.5459 89.4873 32.454C89.4873 34.3622 87.9319 35.9184 86.0249 35.9184ZM44.5163 60.1151H22.8896V57.5845C22.8896 57.2056 22.5921 56.9079 22.2134 56.9079C21.8347 56.9079 21.5371 57.2056 21.5371 57.5845V60.1151H17.1009C16.7222 60.1151 16.4246 60.4129 16.4246 60.7918C16.4246 61.1707 16.7222 61.4684 17.1009 61.4684H21.5371V63.9991C21.5371 64.378 21.8347 64.6757 22.2134 64.6757C22.5921 64.6757 22.8896 64.378 22.8896 63.9991V61.4684H44.5163C44.895 61.4684 45.1926 61.1707 45.1926 60.7918C45.1926 60.4129 44.895 60.1151 44.5163 60.1151ZM70.7956 27.6229C68.1312 27.6229 65.9807 29.7881 65.9807 32.4405C65.9807 35.093 68.1447 37.2582 70.7956 37.2582C73.4465 37.2582 75.6106 35.093 75.6106 32.4405C75.6106 29.7881 73.4465 27.6229 70.7956 27.6229ZM70.7956 35.9184C68.8886 35.9184 67.3332 34.3622 67.3332 32.454C67.3332 30.5459 68.8886 28.9896 70.7956 28.9896C72.7027 28.9896 74.258 30.5459 74.258 32.454C74.258 34.3622 72.7027 35.9184 70.7956 35.9184Z"
        fill="url(#paint0_linear_820_774)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_820_774"
          x1="26.6767"
          y1="117.521"
          x2="92.8981"
          y2="2.91751"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
