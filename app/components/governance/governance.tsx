import clsx from 'clsx'
import { Trans, useTranslation } from 'react-i18next'
import type { Dispatch, SetStateAction } from 'react'
import { useEffect, useState } from 'react'

import type { Voting } from './voting'
import { useVote } from './use-vote'
import { useVoting } from './use-voting'

import {
  Button,
  ButtonVariant,
  H3,
  PollBar,
  TextL,
  TextS,
} from '~/tenset-components'
import { Namespace } from '~/i18n'
import calcTimeLeft from '~/utils/calc-time-left'
import { useWallet } from '~/tenset-web3'
import { ConnectWallet } from '~/components/connect-wallet'
import { WistiaVideo } from '~/components/utils'

interface VotingAnswer {
  title: string
  key: string
  votes: number
  isSelected: boolean
}

export enum VotingStatus {
  ACTIVE = 'active',
  ENDED = 'ended',
}

interface GovernanceVotingProps {
  voting: Voting
  status: VotingStatus
  setNoRightsToVoteModal: Dispatch<SetStateAction<boolean>>
}

export function GovernanceVoting({
  voting: entity,
  status,
  setNoRightsToVoteModal,
}: GovernanceVotingProps) {
  const { t } = useTranslation(Namespace.COMMON)

  const { wallet } = useWallet()
  const { voting, lastVoteAt } = useVoting(entity)
  const { vote, processing, voted, voteable, voteWeight } = useVote(voting)

  const isVotingActive = status === VotingStatus.ACTIVE

  const [truncateDescription, setTruncateDescription] =
    useState(!isVotingActive)

  const { value: endsInValue, unit: endsInUnit } = calcTimeLeft(voting.endsAt)

  const [answers, updateAnswers] = useState<VotingAnswer[]>([])
  const [selectedAnswer, setSelectedAnswer] = useState<VotingAnswer>()

  const [votingError, setVotingError] = useState<boolean>()

  useEffect(() => {
    updateAnswers(
      voting.options.map(option => ({
        title: option.value,
        key: option.key,
        votes: voting.totalVotesByOption(option.key) || 0,
        isSelected: false,
      }))
    )
  }, [lastVoteAt])

  const handleAnswerSelect = (answerIndex: number) => {
    if (!isVotingActive || voted) return

    updateAnswers(previousAnswers =>
      previousAnswers.map((answer, index) => ({
        ...answer,
        isSelected: answerIndex === index,
      }))
    )

    setSelectedAnswer(answers[answerIndex])
  }

  const signMessage = async (message: {
    wallet: string
    option: string
    weight: number
  }) => {
    const stringifiedMessage = JSON.stringify(message)
    const signature = await wallet!.signMessage(stringifiedMessage)

    return { message: stringifiedMessage, signature }
  }

  const getSubmitButtonFunction = () => {
    if (!isVotingActive) return

    if (!voteable) return setNoRightsToVoteModal(true)

    return handleVote()
  }

  const shouldDisableSubmitButton = (): boolean => {
    if (!wallet?.isConnected) return false

    return voted || processing || !selectedAnswer
  }

  const handleVote = async () => {
    const { message, signature } = await signMessage({
      wallet: wallet!.account!,
      option: selectedAnswer!.key,
      weight: voteWeight,
    })

    const canUserVote =
      wallet && isVotingActive && !voted && voteable && !processing

    if (!canUserVote) return
    if (!selectedAnswer) return setVotingError(true)

    await vote(wallet!.account!, message, signature!)
  }

  return (
    <div className="flex flex-col gap-4 lg:gap-6">
      <img className="rounded-[10px]" src={voting.image} alt={voting.title} />

      <div className="flex flex-col gap-3">
        <H3 isBold>{voting.title}</H3>

        {voting.description.length > 0 && (
          <div
            className={clsx('flex gap-2', !truncateDescription && 'flex-col')}
          >
            <TextS
              className={clsx(truncateDescription && 'max-w-[50%] truncate')}
            >
              {voting.description[0]}
            </TextS>

            {truncateDescription ? (
              <Button
                variant={ButtonVariant.Link}
                onClick={() => setTruncateDescription(false)}
              >
                {t('read-more')}
              </Button>
            ) : (
              <>
                {voting.description.slice(1).map((paragraph, index) => (
                  <TextS key={index}>
                    <span
                      dangerouslySetInnerHTML={{ __html: paragraph }}
                    ></span>
                  </TextS>
                ))}

                {voting.wistiaVideoUrl && (
                  <WistiaVideo videoUrl={voting.wistiaVideoUrl} />
                )}
              </>
            )}
          </div>
        )}
      </div>

      <div className="flex flex-col gap-4">
        {answers.map(({ title, isSelected, votes }, index) => (
          <PollBar
            key={title}
            name={title}
            displayProgress={voted || !truncateDescription || !isVotingActive}
            displayVotes={voted || !truncateDescription || !isVotingActive}
            isSelected={isSelected && isVotingActive}
            index={String.fromCodePoint(65 + index)}
            participants={voting.totalVotes()}
            votes={votes}
            myVoteWeight={voted || processing ? 0 : voteWeight}
            onClick={
              isVotingActive ? () => handleAnswerSelect(index) : undefined
            }
          />
        ))}
      </div>

      <div className="flex flex-col gap-4">
        {isVotingActive && (
          <div>
            {wallet?.isConnected ? (
              <div className="flex flex-col gap-2 ">
                {voteWeight > 0 && (
                  <TextL>
                    <Trans
                      t={t}
                      i18nKey={`governance:vote-weight`}
                      values={{
                        voteWeight: voteWeight,
                      }}
                    />
                  </TextL>
                )}
                <Button
                  className="w-full md:w-fit"
                  disabled={shouldDisableSubmitButton()}
                  onClick={getSubmitButtonFunction}
                >
                  {voted ? t('governance:vote-submitted') : t('form.submit')}
                </Button>
              </div>
            ) : (
              <ConnectWallet buttonConnectLabel={t('form.submit')} />
            )}
          </div>
        )}

        {votingError && (
          <TextS className="text-red-100">{t('governance:submit-error')}</TextS>
        )}

        <div className="flex items-center gap-3">
          {/* <TextS className="text-neutral-100">
            {NumberFormatter({ value: voting.participants() })}{' '}
            {t('participants')}
          </TextS> */}

          {isVotingActive && (
            <>
              {/* <div className="h-[2px] w-[2px] bg-neutral-100" /> */}

              <TextS className="text-neutral-100">
                <Trans
                  t={t}
                  i18nKey={`governance:poll-ends-in-${endsInUnit}`}
                  values={{
                    time: endsInValue,
                  }}
                />
              </TextS>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
