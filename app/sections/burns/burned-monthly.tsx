import { Trans, useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  Graph,
  graphBurnedMonthlyArguments,
  H2,
  H3,
  NumberFormatter,
  Text,
} from '~/tenset-components'
import type { BurnedMonthlyApiData } from '~/api/types'

interface BurnsBurnedMonthlyProps {
  burnedMonthly: BurnedMonthlyApiData[]
}

export function BurnsBurnedMonthly({ burnedMonthly }: BurnsBurnedMonthlyProps) {
  const { t } = useTranslation([Namespace.BURNS])

  const burnedMonthlyMap = burnedMonthly.map(({ date, burned }) => ({
    date: new Date(date),
    burned,
  }))

  const averageBurned =
    burnedMonthlyMap.reduce(
      (accumulator, { burned }) => accumulator + burned,
      0
    ) / burnedMonthlyMap.length

  return (
    <div className="flex flex-col gap-8 lg:gap-12">
      <H2 isBold>{t('burned-monthly.title')}</H2>

      <div className="flex flex-col md:grid md:grid-cols-2 gap-12">
        <div className="h-56">
          <Graph data={burnedMonthlyMap} {...graphBurnedMonthlyArguments} />
        </div>

        <div className="flex flex-col gap-4">
          <div>
            <h2 className="text-2xl lg:text-3xl font-semibold">
              <Trans t={t} i18nKey={'burned-monthly.data.title'}>
                <NumberFormatter value={averageBurned} precision={[2, 2]} />
              </Trans>
            </h2>

            <H3 isBold>{t('burned-monthly.data.subtitle')}</H3>
          </div>

          <Text>{t('burned-monthly.data.description')}</Text>
        </div>
      </div>
    </div>
  )
}
