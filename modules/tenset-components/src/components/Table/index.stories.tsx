import { Meta } from '@storybook/react'

import { headers, items } from './story-data'

import { Table } from './index'

type TableType = typeof Table

export default {
  title: 'Components/Table',
  component: Table,
  args: {
    headers,
    items,
    orderColumnLabel: 'Rank',
  },
} as Meta<TableType>

export const Preview = {}

export const WithoutHeaders = {
  args: {
    headers: undefined,
  },
}

export const Raw = {
  args: {
    isRaw: true,
  },
}
