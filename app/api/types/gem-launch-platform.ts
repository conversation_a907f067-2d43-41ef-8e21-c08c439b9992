import type { GlpMembershipAttributes, Utils } from '~/tenset-web3'

export interface GlpLock {
  id: number
  amount: string
  txHash: string
  timestamps: {
    unlock: number
    withdraw?: number
  }
  proofs?: {
    withdraw: Utils.Proof
  }
  membershipAttributes?: GlpMembershipAttributes
}

export interface GlpNft {
  id: number
  chainId: Utils.Network
  collection: string
  contractAddress: string
  tokenName: string
  image: string
  isImageVideo?: boolean
}

export type GlpSubscriptionInfo = {
  locks: GlpLock[]
  nfts: GlpNft[]
}

export type DelegationGlpSubscriptionInfo = GlpSubscriptionInfo & {
  address: string
}

export interface GlpSubscriptionInfoForMany extends GlpSubscriptionInfo {
  assigningAddresses: string[]
  fromDelegation: DelegationGlpSubscriptionInfo[]
}

export interface GlpAssignWalletData {
  subscriptionAddress: string
  assignedAddress: string
  message: string
  signature: string
  date: string
  launchId: string
}

export interface GlpAssignWalletMessage {
  launchName: string
  subscriptionAddress: string
  assignedAddress: string
  date: string
}

export interface GlpReferralLockMessage {
  subscriptionAddress: string
  lockId: number
  unlockDate: string
  date: string
}

export interface GlpReferralLockData {
  subscriptionAddress: string
  lockId: number
  date: string
  signature: string
  message: string
}
