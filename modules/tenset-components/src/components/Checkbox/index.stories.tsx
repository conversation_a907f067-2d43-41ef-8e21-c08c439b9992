import { Meta } from '@storybook/react'

import { TextS } from '../Typo/Text'

import { Checkbox } from './index'

type CheckboxType = typeof Checkbox

export default {
  title: 'Components/Checkbox',
  component: Checkbox,
  args: {
    label: 'I agree to the terms and conditions',
  },
} as Meta<CheckboxType>

export const Preview = {
  args: {
    id: 'terms1',
  },
}

export const WithChildren = {
  args: {
    id: 'terms2',
    children: <TextS>Lorem ipsum dolor sit amet.</TextS>,
  },
}

export const Disabled = {
  args: {
    id: 'terms3',
    disabled: true,
  },
}
