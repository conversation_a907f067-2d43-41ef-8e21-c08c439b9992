import { HrefType } from './data'

enum SupportedLocale {
  EN = 'en',
  KO = 'ko',
  JA = 'ja',
  ES = 'es',
}

const SUPPORTED_LOCALES = Object.values(SupportedLocale)

const currentLocale = SupportedLocale.EN

const navigationData = [
  {
    label: 'Only mobile menu visible',
    href: {
      type: HrefType.Internal,
      url: '/example',
      withLocale: true,
    },
  },
  {
    label: 'Internal link',
    href: {
      type: HrefType.Internal,
      url: '/example',
      withLocale: true,
    },
  },
  {
    label: 'External link',
    href: {
      type: HrefType.External,
      url: 'https://infinity.tenset.io/',
      withLocale: true,
    },
  },
  {
    label: 'Dropdown',
    subItems: [
      {
        label: 'Internal',
        href: {
          type: HrefType.Internal,
          url: '/example',
          withLocale: true,
        },
      },
      {
        label: 'External',
        href: {
          type: HrefType.External,
          url: 'https://marketplace.tenset.io/',
          withLocale: true,
        },
      },
    ],
  },
  {
    label: 'Dropdown 2',
    subItems: [
      {
        label: 'Internal',
        href: {
          type: HrefType.Internal,
          url: '/example',
          withLocale: true,
        },
      },
      {
        label: 'External',
        href: {
          type: HrefType.External,
          url: 'https://coinmarketcap.com/currencies/tenset/',
          withLocale: true,
        },
      },
    ],
  },
]

export { SupportedLocale, navigationData, SUPPORTED_LOCALES, currentLocale }
