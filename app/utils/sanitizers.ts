import type { HTMLElement } from 'node-html-parser'
import { parse } from 'node-html-parser'

const CMS_REPLACEMENTS = [
  {
    match: /<style><\/style>/g,
    into: '',
  },
  {
    match: /!\[(.*?)]\((.*?)\)/g,
    into: '',
  },
  {
    match: /<iframe .*?iframe>/gs,
    into: '',
  },
  {
    match: /<video .*?video>/gs,
    into: '',
  },
  {
    match: /</g,
    into: '\u003C//',
  },
  {
    match: />/g,
    into: '\u003E',
  },
]

const META_REPLACEMENTS = [
  {
    match: /<style><\/style>/g,
    into: '',
  },
  {
    match: /<video .*?video>/gs,
    into: '',
  },
  {
    match: /&nbsp;/g,
    into: '',
  },
  {
    match: /<iframe .*?iframe>/gs,
    into: '',
  },
]

export function sanitizeHTML(html: string) {
  let htmlWithoutBlockedElements = parse(html, {
    lowerCaseTagName: true,
    comment: false,
    blockTextElements: {
      script: false,
      noscript: false,
      style: false,
      pre: false,
    },
  }).toString()

  for (const r of CMS_REPLACEMENTS) {
    htmlWithoutBlockedElements = htmlWithoutBlockedElements.replace(
      r.match,
      r.into
    )
  }

  return htmlWithoutBlockedElements
}

function removeWrappedTags(accumulator: string, node: HTMLElement) {
  if (node.tagName === 'P') {
    accumulator += node.textContent
  }

  if (node.tagName === 'A') {
    return accumulator
  }

  if (node.tagName && node.tagName !== 'P' && node.tagName !== 'A') {
    return accumulator
  }

  if (!node.tagName) {
    accumulator += [...node.childNodes]
      .map(child => removeWrappedTags(accumulator, child as HTMLElement))
      .join(' ')
  }

  return accumulator
}

/*
 * Heuristics to remove all the HTML wrapped tags for
 * - <a> is replaced with the link
 * - <p> is replaced with it's content
 * - only 2 <p> are taken to match 250 characters
 * - otherwise 3 <p> are taken
 * - other tags are ignored
 */
function traverseDescription(node: HTMLElement) {
  const paragraphs = node.querySelectorAll('p').values()

  let chars = 0
  const accumulatedParagraphs = []

  for (const p of paragraphs) {
    if (chars >= 150) {
      break
    }

    chars += p.toString().length
    accumulatedParagraphs.push(p)
  }

  return accumulatedParagraphs.map(p => removeWrappedTags('', p)).join(' \n')
}

export function sanitizeDescriptionToRawString(html: string) {
  let htmlWithoutBlockedElements = traverseDescription(
    parse(html, {
      lowerCaseTagName: true,
      comment: false,
      blockTextElements: {
        script: false,
        noscript: false,
        style: false,
        pre: false,
      },
    })
  )

  for (const r of META_REPLACEMENTS) {
    htmlWithoutBlockedElements = htmlWithoutBlockedElements.replace(
      r.match,
      r.into
    )
  }

  return htmlWithoutBlockedElements
}
