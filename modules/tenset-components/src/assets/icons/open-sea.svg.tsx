import { AssetProps } from './index'

export default function OpenSeaLogo({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_159_235)">
        <g clipPath="url(#clip1_159_235)">
          <path
            d="M24 12C24 18.624 18.624 24 12 24C5.376 24 0 18.624 0 12C0 5.376 5.376 0 12 0C18.624 0 24 5.376 24 12Z"
            fill={color ?? '#2081E2'}
          />
          <path
            d="M5.92801 12.408L5.97601 12.336L9.09601 7.464C9.14401 7.392 9.24001 7.392 9.28801 7.488C9.81601 8.664 10.248 10.104 10.056 11.016C9.96001 11.4 9.72001 11.904 9.43201 12.36C9.40801 12.432 9.36001 12.504 9.31201 12.552C9.28801 12.576 9.26401 12.6 9.21601 12.6H6.00001C5.92801 12.576 5.88001 12.48 5.92801 12.408Z"
            fill={color ?? 'white'}
          />
          <path
            d="M19.824 13.32V14.088C19.824 14.136 19.8 14.16 19.752 14.184C19.512 14.28 18.672 14.664 18.336 15.144C17.448 16.368 16.776 18.12 15.288 18.12H9.04801C6.84001 18.12 5.04001 16.32 5.04001 14.088V14.016C5.04001 13.968 5.08801 13.92 5.13601 13.92H8.64001C8.71201 13.92 8.76001 13.992 8.76001 14.04C8.73601 14.256 8.78401 14.496 8.88001 14.712C9.09601 15.144 9.52801 15.384 9.98401 15.384H11.712V14.04H10.008C9.91201 14.04 9.86401 13.944 9.91201 13.872C9.93601 13.848 9.96001 13.824 9.98401 13.776C10.152 13.536 10.368 13.2 10.608 12.792C10.776 12.528 10.92 12.216 11.04 11.928C11.064 11.88 11.088 11.832 11.112 11.76C11.136 11.664 11.184 11.568 11.208 11.496C11.232 11.424 11.256 11.352 11.28 11.28C11.328 11.04 11.352 10.776 11.352 10.488C11.352 10.392 11.352 10.272 11.328 10.152C11.328 10.032 11.304 9.912 11.304 9.792C11.304 9.696 11.28 9.576 11.256 9.48C11.232 9.312 11.208 9.168 11.16 9L11.136 8.952C11.112 8.856 11.088 8.736 11.04 8.64C10.944 8.304 10.824 7.968 10.704 7.68C10.656 7.56 10.608 7.44 10.56 7.32C10.488 7.152 10.416 6.984 10.344 6.84C10.32 6.768 10.272 6.72 10.248 6.648C10.224 6.576 10.176 6.504 10.152 6.432C10.128 6.384 10.104 6.336 10.08 6.288L9.86401 5.904C9.84001 5.856 9.88801 5.784 9.93601 5.808L11.256 6.168L11.424 6.216L11.616 6.264L11.688 6.288V5.496C11.688 5.112 12 4.8 12.36 4.8C12.552 4.8 12.72 4.872 12.84 4.992C12.96 5.112 13.032 5.28 13.032 5.472V6.624L13.176 6.672C13.176 6.672 13.2 6.672 13.2 6.696C13.224 6.72 13.272 6.768 13.344 6.816C13.392 6.864 13.44 6.912 13.512 6.96C13.632 7.056 13.8 7.2 13.968 7.344C14.016 7.392 14.064 7.416 14.088 7.464C14.304 7.656 14.544 7.896 14.784 8.16C14.856 8.232 14.904 8.304 14.976 8.376C15.048 8.448 15.096 8.544 15.168 8.616C15.24 8.712 15.336 8.832 15.408 8.928C15.432 8.976 15.48 9.024 15.504 9.096C15.6 9.24 15.672 9.384 15.768 9.528C15.792 9.6 15.84 9.672 15.864 9.744C15.96 9.936 16.032 10.128 16.056 10.344C16.08 10.392 16.08 10.44 16.08 10.464C16.104 10.512 16.104 10.584 16.104 10.656C16.128 10.872 16.104 11.064 16.08 11.28C16.056 11.376 16.032 11.448 16.008 11.544C15.984 11.616 15.96 11.712 15.912 11.808C15.84 11.976 15.744 12.144 15.648 12.312C15.624 12.36 15.576 12.432 15.528 12.504C15.48 12.576 15.432 12.624 15.408 12.696C15.36 12.768 15.288 12.84 15.24 12.912C15.192 12.984 15.144 13.056 15.072 13.128C15 13.224 14.904 13.32 14.832 13.416C14.784 13.464 14.736 13.536 14.664 13.584C14.616 13.632 14.568 13.704 14.496 13.752C14.424 13.824 14.352 13.896 14.28 13.944L14.136 14.064C14.112 14.088 14.088 14.088 14.064 14.088H13.008V15.432H14.328C14.616 15.432 14.904 15.336 15.12 15.144C15.192 15.072 15.528 14.784 15.936 14.352C15.96 14.328 15.96 14.328 15.984 14.328L19.704 13.2C19.776 13.176 19.824 13.248 19.824 13.32Z"
            fill={color ?? 'white'}
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_159_235">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
        <clipPath id="clip1_159_235">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
