import type { ActionFunctionArgs } from '@remix-run/node'

import { commitSession, getSession } from '~/sessions.server'

export async function action({ request }: ActionFunctionArgs) {
  const url = new URL(request.url)
  const walletAddress = url.searchParams.get('walletAddress')?.toLowerCase()

  const session = await getSession(request.headers.get('<PERSON>ie'))
  session.set('walletAddress', walletAddress)

  return new Response('', {
    status: 200,
    headers: {
      'Set-Cookie': await commitSession(session),
    },
  })
}
