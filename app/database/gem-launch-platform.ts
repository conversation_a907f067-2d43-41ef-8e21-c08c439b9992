import type { AssignedWallets, ReferralLock } from '@prisma/client'
import { json } from '@remix-run/node'

import { database } from '.'

import { getErrorMessage } from '~/utils'

export async function insertAssignWallet({
  subscription_address,
  assigned_address,
  message,
  sign,
  date,
  launch_id,
}: AssignedWallets) {
  try {
    await database!.assignedWallets.upsert({
      where: {
        walletLaunch: {
          subscription_address,
          launch_id,
        },
      },
      update: {
        assigned_address,
        message,
        sign,
        date,
        launch_id,
      },
      create: {
        subscription_address,
        assigned_address,
        message,
        sign,
        date,
        launch_id,
      },
    })
  } catch (error) {
    throw json({ message: getErrorMessage(error) }, { status: 409 })
  }

  return json({ message: 'Wallet assigned' }, { status: 201 })
}

export async function getAssignedWallet(
  subscription_address: string,
  launch_id: string
) {
  const result = await database!.assignedWallets.findUnique({
    where: {
      walletLaunch: {
        subscription_address,
        launch_id,
      },
    },
    select: {
      assigned_address: true,
    },
  })

  return result?.assigned_address || null
}

export async function getSubscriptionAddresses(
  assigned_address: string,
  launch_id: string
) {
  const result = await database!.assignedWallets.findMany({
    where: {
      assigned_address: {
        equals: assigned_address,
        mode: 'insensitive',
      },
      launch_id: {
        equals: launch_id,
      },
    },
    select: {
      subscription_address: true,
    },
  })

  return result ? result.map(x => x.subscription_address) : []
}

export async function deleteAssignWallet(
  subscription_address: string,
  launch_id: string
) {
  try {
    await database!.assignedWallets.delete({
      where: {
        walletLaunch: {
          subscription_address,
          launch_id,
        },
      },
    })
  } catch (error) {
    throw json({ message: getErrorMessage(error) }, { status: 406 })
  }

  return json({ message: 'Wallet deleted' }, { status: 201 })
}

export async function getReferralLocks(
  subscription_address: string
): Promise<number[]> {
  const result = await database!.referralLock.findMany({
    where: {
      subscription_address,
    },
    select: {
      lock_id: true,
    },
  })
  return result ? result.map(x => x.lock_id) : []
}

export async function addReferralLock({
  subscription_address,
  lock_id,
  message,
  sign,
  date,
}: Omit<ReferralLock, 'id'>) {
  try {
    await database!.referralLock.create({
      data: {
        subscription_address,
        lock_id,
        message,
        sign,
        date,
      },
    })
  } catch (error) {
    throw json({ message: getErrorMessage(error) }, { status: 409 })
  }

  return json({ message: 'Referrer added' }, { status: 201 })
}
