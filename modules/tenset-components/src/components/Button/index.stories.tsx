import { StoryObj, Meta } from '@storybook/react'
import { <PERSON> } from '@remix-run/react'

import { InfoIcon, LogoIcon } from '../../assets/icons'

import { Button, ButtonVariant } from './index'

type ButtonType = typeof Button

export default {
  title: 'Components/Button',
  component: Button,
  parameters: {
    docs: {
      description: {
        component:
          'Variants: Primary/Secondary/Ghost/Link. States: Rest/Hover/Pressed/Focused/Disabled. Content can be text (string) or/and icon (component). Can be rendered as any HTML element, e.g button, anchor or Link (react routing)',
      },
    },
  },
  args: {
    as: 'button',
    children: 'Label',
    variant: ButtonVariant.Primary,
    reverseChildren: false,
  },
  argTypes: {
    as: {
      description: 'The HTML element to render. E.g. button, a, Link',
    },
    children: {
      description:
        'The content of the button, can be a string or/and a component',
    },
    reverseChildren: {
      description: 'True when you want e.g. the logo to render before the text',
    },
  },
} as Meta<ButtonType>

export const Preview: StoryObj<ButtonType> = {
  render: arguments_ => <Button {...arguments_}>{arguments_.children}</Button>,
}

export const PrimaryVariant: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Primary}>
      {arguments_.children}
    </Button>
  ),
}

export const PrimaryVariantDisabled: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Primary} disabled>
      {arguments_.children}
    </Button>
  ),
}

export const SecondaryVariant: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Secondary}>
      {arguments_.children}
    </Button>
  ),
}

export const SecondaryVariantDisabled: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Secondary} disabled>
      {arguments_.children}
    </Button>
  ),
}

export const SecondaryDarkerVariant: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.SecondaryDarker}>
      {arguments_.children}
    </Button>
  ),
}

export const GhostVariant: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Ghost}>
      {arguments_.children}
    </Button>
  ),
}

export const GhostVariantDisabled: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Ghost} disabled>
      {arguments_.children}
    </Button>
  ),
}

export const LinkVariant: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} variant={ButtonVariant.Link}>
      {arguments_.children}
    </Button>
  ),
}

export const Anchor: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} as="a" to="https://tenset.io/">
      {arguments_.children}
    </Button>
  ),
}

export const LinkRouter: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} as={Link} to="/">
      {arguments_.children}
    </Button>
  ),
}

export const LabelIcon: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_}>
      {arguments_.children}

      <InfoIcon />
    </Button>
  ),
}

export const LogoLabel: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} reverseChildren>
      {arguments_.children}

      <LogoIcon />
    </Button>
  ),
}

export const Logo: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} isIcon>
      <LogoIcon />
    </Button>
  ),
}

export const Stretched: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} className="w-full">
      {arguments_.children}
    </Button>
  ),
}

export const ButtonIsLoading: StoryObj<ButtonType> = {
  render: arguments_ => (
    <Button {...arguments_} className="w-full" loading></Button>
  ),
}
