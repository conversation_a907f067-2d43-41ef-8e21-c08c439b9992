import { AssetProps } from './index'

export default function IPhone({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? 130}
      height={size ?? 130}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M37.2003 34.5502C34.8508 34.5502 32.9464 36.4553 32.9464 38.8057C32.9464 41.1562 34.8508 43.0613 37.2003 43.0613C39.5499 43.0613 41.4542 41.1562 41.4542 38.8057C41.4542 36.4553 39.5499 34.5502 37.2003 34.5502ZM37.2003 41.8242C35.5309 41.8242 34.183 40.4634 34.183 38.8057C34.183 37.1481 35.5433 35.7873 37.2003 35.7873C38.8574 35.7873 40.2176 37.1357 40.2176 38.8057C40.2176 40.4758 38.8697 41.8242 37.2003 41.8242ZM96.8541 19H66.0626C65.7782 19 65.5062 19.0495 65.2341 19.0866C65.1476 19.0371 65.0486 19 64.9373 19H34.1459C30.7576 19 28 21.7587 28 25.1483V103.852C28 107.241 30.7576 110 34.1459 110H64.9373C65.0486 110 65.1476 109.963 65.2341 109.913C65.5062 109.951 65.7782 110 66.0626 110H96.8541C100.242 110 103 107.241 103 103.852V25.1483C103 21.7587 100.242 19 96.8541 19ZM34.1583 108.751C31.4501 108.751 29.249 106.549 29.249 103.839V25.136C29.249 22.4268 31.4501 20.2247 34.1583 20.2247H62.4023C60.906 21.3505 59.9167 23.1195 59.9167 25.136V103.839C59.9167 105.856 60.906 107.625 62.4023 108.751H34.1583ZM101.763 39.0037L67.1632 73.6174C66.9159 73.8648 66.9159 74.2482 67.1632 74.4956C67.2869 74.6193 67.4477 74.6812 67.596 74.6812C67.7444 74.6812 67.9176 74.6193 68.0289 74.4956L101.751 40.7604V103.852C101.751 106.561 99.5499 108.763 96.8417 108.763H66.0503C63.3421 108.763 61.141 106.561 61.141 103.852V88.784L95.6917 54.2198C95.939 53.9724 95.939 53.5889 95.6917 53.3415C95.4444 53.0941 95.061 53.0941 94.8137 53.3415L61.141 87.0273V25.1483C61.141 22.4391 63.3421 20.2371 66.0503 20.2371H96.8417C99.5499 20.2371 101.751 22.4391 101.751 25.1483V39.0037H101.763ZM85.7123 21.2144H77.2044C76.2523 21.2144 75.4856 21.9814 75.4856 22.9339C75.4856 23.8865 76.2523 24.6535 77.2044 24.6535H85.7123C86.6521 24.6535 87.4312 23.8865 87.4312 22.9339C87.4312 21.9814 86.6645 21.2144 85.7123 21.2144ZM85.7123 23.4164H77.2044C76.9448 23.4164 76.7222 23.2061 76.7222 22.9339C76.7222 22.6618 76.9324 22.4514 77.2044 22.4514H85.7123C85.972 22.4514 86.1946 22.6618 86.1946 22.9339C86.1946 23.2061 85.9843 23.4164 85.7123 23.4164ZM37.2003 23.4164C34.8508 23.4164 32.9464 25.3215 32.9464 27.672C32.9464 30.0224 34.8508 31.9276 37.2003 31.9276C39.5499 31.9276 41.4542 30.0224 41.4542 27.672C41.4542 25.3215 39.5499 23.4164 37.2003 23.4164ZM37.2003 30.6905C35.5309 30.6905 34.183 29.342 34.183 27.672C34.183 26.0019 35.5433 24.6535 37.2003 24.6535C38.8574 24.6535 40.2176 26.0143 40.2176 27.672C40.2176 29.3297 38.8697 30.6905 37.2003 30.6905ZM46.5861 28.9833C44.2366 28.9833 42.3322 30.8884 42.3322 33.2389C42.3322 35.5893 44.2366 37.4944 46.5861 37.4944C48.9357 37.4944 50.8401 35.5893 50.8401 33.2389C50.8401 30.8884 48.9357 28.9833 46.5861 28.9833ZM46.5861 36.2574C44.9167 36.2574 43.5688 34.9089 43.5688 33.2389C43.5688 31.5688 44.9167 30.2204 46.5861 30.2204C48.2556 30.2204 49.6035 31.5812 49.6035 33.2389C49.6035 34.8966 48.2432 36.2574 46.5861 36.2574ZM48.812 21.2144H34.9868C32.3405 21.2144 30.1888 23.3669 30.1888 26.0143V40.4511C30.1888 43.0985 32.3405 45.2509 34.9868 45.2509H48.812C51.4584 45.2509 53.6101 43.0985 53.6101 40.4511V26.0143C53.6101 23.3669 51.4584 21.2144 48.812 21.2144ZM52.3735 40.4511C52.3735 42.4181 50.7782 44.0138 48.812 44.0138H34.9868C33.0206 44.0138 31.4254 42.4181 31.4254 40.4511V26.0143C31.4254 24.0473 33.0206 22.4514 34.9868 22.4514H48.812C50.7782 22.4514 52.3735 24.0473 52.3735 26.0143V40.4511Z"
        fill="url(#paint0_linear_2185_65)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_65"
          x1="37.5713"
          y1="112.907"
          x2="93.4861"
          y2="16.0993"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
