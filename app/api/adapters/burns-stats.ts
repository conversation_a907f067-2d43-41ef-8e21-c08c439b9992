import type { BurnsStatsApiData, BurnsStatsApiResponse } from '~/api/types'

export function adaptBurnsStats(
  data: BurnsStatsApiResponse
): BurnsStatsApiData {
  const currentTimestamp = Math.floor(Date.now() / 1000)

  const dailyAvg = Number.parseFloat(data.dailyAvg)
  const cachedDailyAvg = (currentTimestamp - data.cacheTimestamp) * dailyAvg

  return {
    cachedDailyAvg,
    dailyAvg,
    burnedToday: Number.parseFloat(data.burnedToday),
    burnedWeek: Number.parseFloat(data.burnedWeek),
    burnedMonth: Number.parseFloat(data.burnedMonth),
  }
}
