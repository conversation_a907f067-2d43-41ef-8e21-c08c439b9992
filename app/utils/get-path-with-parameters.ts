import type { Params } from '@remix-run/react'

/**
 * Returns a pathname with changed parameters value with their keys.
 * e.g. "/en/news/round-2-alvara-ido-details" => "/:locale/news/:slug"
 */
export function getPathWithParameters(
  pathname: string,
  parameters: Params
): string {
  const pathParts = pathname.split('/')

  const parameterKeys = Object.keys(parameters)

  for (const key of parameterKeys) {
    const value = parameters[key]
    if (!value) continue

    const index = pathParts.indexOf(value)

    if (index !== -1) {
      pathParts[index] = `:${key}`
    }
  }

  let finalPath = pathParts.join('/')

  if (finalPath !== '/' && finalPath.endsWith('/')) {
    finalPath = finalPath.slice(0, -1)
  }

  return finalPath
}
