import type { QueryBinding } from './query.binding'
import { QueryWhereBinding } from './query.binding'

export class Query {
  get limit() {
    return this._limit
  }

  get bindings() {
    return this._bindings
  }

  protected _limit: number | null = null

  protected _bindings: QueryBinding[] = []

  take(value: number) {
    this._limit = value

    return this
  }

  where<T>(
    key: keyof T,
    operatorOrValue: unknown,
    value: unknown | null = null
  ) {
    this._bindings.push(
      new QueryWhereBinding(key as string, operatorOrValue, value)
    )

    return this
  }
}
