import { Voting } from '~/components/governance'
import type { VotingDTO } from '~/state/governance'

export const votingsRaw = [
  {
    environment: {
      development: true,
      preview: true,
      production: true,
    },
    options: {
      yes: 'Yes',
      no: 'No',
    },
    tiers: {
      '1': 100,
    },
    visible: true,
    description: [
      'The community must vote to accept or reject this project to feature on the Tenset Gem Launch Platform. Please read the full information about <PERSON><PERSON><PERSON> and the voting process here before casting your vote. ',
      '<a href="https://www.tenset.io/en/news/time-to-vote-for-a-tglp-launch">https://www.tenset.io/en/news/time-to-vote-for-a-tglp-launch</a>',
    ],
    image: 'https://infinity-static.b-cdn.net/voting/alvara.jpeg',
    requirements: ['balance', 'tglp'],
    title: 'Would you like Alvara Protocol to feature on TGLP?',
    endsAt: {
      seconds: 1_692_007_200,
      nanoseconds: 766_000_000,
    },
    id: 'Rx0hgoVnqPznNhqlBJwK',
    votes: {
      items: {
        no: 257,
        yes: 1664,
      },
      participants: 311,
      total: 1921,
    },
  },
  {
    requirements: ['balance', 'tglp'],
    options: {
      yes: 'Yes',
      no: 'No',
    },
    environment: {
      development: true,
      preview: true,
      production: true,
    },
    image: 'https://infinity-static.b-cdn.net/voting/ivendpay.jpeg',
    tiers: {
      '1': 100,
    },
    visible: true,
    title: 'Would you like ivendPay to feature on TPL?',
    description: [
      'The community must vote to accept or reject a project to feature on the Tenset Protectron Launchpad. Please read the full information about ivendPay and the voting process here before casting your vote. ',
      '<a href="https://www.tenset.io/en/news/first-community-vote-for-tpl">https://www.tenset.io/en/news/first-community-vote-for-tpl</a>',
    ],
    endsAt: {
      seconds: 1_690_192_800,
      nanoseconds: 932_000_000,
    },
    id: '1BIy0ZrJnevAKLCeD3PR',
    votes: {
      items: {
        yes: 1539,
        no: 33,
      },
      participants: 299,
      total: 1572,
    },
  },
  {
    image: 'https://infinity-static.b-cdn.net/voting/new_chain.jpeg',
    options: {
      polygon: 'Polygon',
      cardano: 'Cardano',
      ethereum: 'Ethereum',
      arbitrum: 'Arbitrum',
      avalanche: 'Avalanche',
    },
    description:
      '10SET token is currently only on BNB chain. We would like to hear what additional chain the community would like 10SET token to bridge to. ',
    requirements: ['balance', 'tglp', 'infinity'],
    environment: {
      preview: true,
      production: true,
      development: true,
    },
    visible: true,
    title: 'What blockchain should 10SET token bridge to next?',
    tiers: {
      '1': 100,
    },
    endsAt: {
      seconds: 1_688_374_800,
      nanoseconds: 986_000_000,
    },
    id: '2mMfuedT5uFwKjH3mTce',
    votes: {
      items: {
        ethereum: 94,
        polygon: 67,
        arbitrum: 20,
        avalanche: 2,
        cardano: 5,
      },
      participants: 188,
      total: 188,
    },
  },
  {
    tiers: {
      '1': 100,
    },
    title: 'What is your preferred option for 10set token transaction tax?',
    image: 'https://infinity-static.b-cdn.net/io/abstract-4.jpeg',
    requirements: ['infinity', 'tglp', 'balance'],
    startsAt: {
      seconds: 1_673_431_185,
      nanoseconds: 387_000_000,
    },
    environment: {
      preview: true,
      development: true,
      production: true,
    },
    options: {
      a: '4% tax - 2% burn, 2% Infinity',
      c: '4% tax - 2% burn, 1% Infinity, 1% Genesis NFT holders',
      d: 'Keep as 2% tax - 1% burn, 1% Infinity',
      b: '4% tax - 3% burn, 1% Infinity',
    },
    endsAt: {
      seconds: 1_673_604_000,
      nanoseconds: 802_000_000,
    },
    visible: true,
    description: [
      'The Tenset community had a great discussion on our recent vote regarding 10set token tax and proposed many interesting ideas. We always consider feedback from our passionate supporters so want to share 4 options in a final vote. ',
    ],
    id: 'PKTnrlEuHrKdLC6ZOeTR',
    votes: {
      items: {
        a: 153,
        c: 369,
        d: 40,
        b: 26,
      },
      participants: 588,
      total: 588,
    },
  },
  {
    wistiaVideoUrl: 'https://tenset.wistia.com/medias/vds7ynlzmt',
    title: 'Should the 10set transaction tax be increased from 2% to 4%?',
    visible: true,
    environment: {
      preview: true,
      production: true,
      development: true,
    },
    tiers: {
      '1': 100,
    },
    description: [
      'Currently there is a 2% tax on all transactions - 1% burn, 1% to Infinity staking.',
      'We are proposing to increase this tax to 4% - 2% burn, 2% to Infinity staking.',
      'You can watch the below video from Tenset’s CEO for more context on why we are proposing this change and the potential benefits. We value the feedback and opinions of our passionate community.',
    ],
    options: {
      no: 'No',
      yes: 'Yes',
    },
    endsAt: {
      seconds: 1_673_175_600,
      nanoseconds: 438_000_000,
    },
    startsAt: {
      seconds: 1_672_829_700,
      nanoseconds: 499_000_000,
    },
    requirements: ['balance', 'tglp', 'infinity'],
    image: 'https://infinity-static.b-cdn.net/io/abstract-1.jpeg',
    id: 'QvWtsQH7wd05bmiPvXlr',
    votes: {
      items: {
        yes: 453,
        no: 211,
      },
      participants: 664,
      total: 664,
    },
  },
  {
    requirements: ['tglp'],
    title:
      'Would you like to exchange your TGLP subscription into a dedicated unique NFT, with the utility of TGLP access?',
    image: 'https://infinity-static.b-cdn.net/io/abstract-4.jpeg',
    visible: true,
    options: {
      yes: 'Yes, I would like to convert my subscription into a TGLP NFT',
      no: 'No, I do not want an NFT and would like to keep the TGLP subscription the same',
    },
    environment: {
      development: true,
      production: true,
      preview: true,
    },
    description: [
      'What does a TGLP NFT mean?',
      'Key features:',
      '&bull; No lock of 10set tokens is required, you simply need to acquire one of the dedicated NFTs.',
      '&bull; To acquire the NFT, you will need to exchange your TGLP subscription for the NFT. The 10set that was locked for the TGLP subscription will be burned, further boosting the deflation. ',
      '&bull; The NFT will be tradable on the future Tenset marketplace (or Opensea, depending on your preference), meaning owners can sell their NFT/TGLP subscription in the future.',
      '&bull; The NFT owner is eligible to participate in ALL gems',
      '&bull; Additional collectible value',
      '&bull; Limited edition (only 10,000 NFTs can exist ever) which will be hotly demanded after the 10,000 subscription cap is soon reached.',
      '&bull; Unique visual design for the NFTs will depend on when the subscription was made and the amount of 10set allocated',
      '<strong>The TGLP NFT concept has not been finalised yet and is subject to change. This Governance vote is to gauge the opinion of the community and explore whether they are interested in converting their TGLP subscription into an NFT. We appreciate your participation in this vote and invite you to share feedback on our social channels.</strong>',
    ],
    endsAt: {
      seconds: 1_648_717_200,
      nanoseconds: 0,
    },
    id: 'HDjlylOdxwHYkdMHIcv4',
    votes: {
      items: {
        yes: 851,
        no: 1070,
      },
      participants: 1921,
      total: 1921,
    },
  },
  {
    visible: true,
    endsAt: {
      seconds: 1_648_677_600,
      nanoseconds: 0,
    },
    options: {
      no: 'No, maybe next time',
      yes: 'Yes, I want to participate',
    },
    environment: {
      development: true,
      preview: true,
      production: true,
    },
    title: 'Do you want to participate in the Metahero Public Presale?',
    description: [
      'If you want to join Metahero Global Presale, simply answer, “Yes, I want to participate”.',
      'The registration is open for 72 hours, from 10 am 28th of June until 10 am 1st of July.',
      'Each participant needs to have 1000 sets / 3 votes for the registration to be valid.',
      'Each participant can only connect one wallet.',
      'All submitted addresses will be added to the whitelist; the system is automatic.',
    ],
    image: 'https://infinity-static.b-cdn.net/io/hero-cover.png',
    votes: {
      items: {
        yes: 12_931,
        no: 54,
      },
      total: 12_985,
      participants: 4109,
    },
    id: 'MPSh315kgtOEo4xQ5IAc',
  },
  // {
  //   environment: {
  //     production: true,
  //     development: true,
  //     preview: true,
  //   },
  //   visible: true,
  //   endsAt: {
  //     seconds: 1_628_978_400,
  //     nanoseconds: 0,
  //   },
  //   options: {
  //     '2500': 2500,
  //     '3000': 3000,
  //     '3500': 3500,
  //   },
  //   image: 'https://infinity-static.b-cdn.net/io/abstract-2.jpeg',
  //   title:
  //     'How many $10sets should be required for the next round of the TGLP subscription?',
  //   votes: {
  //     total: 2495,
  //     participants: 867,
  //     items: {
  //       '2500': 216,
  //       '3000': 1140,
  //       '3500': 1139,
  //     },
  //   },
  //   id: 'uQlhepoaikUhkfNgsxBw',
  // },
]

export const endedVotingsData = votingsRaw.map(dto =>
  Voting.make(dto as unknown as VotingDTO)
)
