import type { VerifiedMedia } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function fetchVerifiedMedias(
  phraseToVerify: string
): Promise<VerifiedMedia[] | false> {
  const VERIFICATION_URL = `${getServerEnvironment().STRAPI_URL}/api/verified-medias?`

  const parameters = decodeURIComponent(
    new URLSearchParams({
      populate: 'media_source,media_scopes',
      'filters[name][$eq]': phraseToVerify.toLowerCase() || '',
    }).toString()
  )

  return await fetch(VERIFICATION_URL + parameters)
    .then<{ data: VerifiedMedia[] }>(response => response.json())
    .then(({ data }) => data)
    .catch(() => false)
}
