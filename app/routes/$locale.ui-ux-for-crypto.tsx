import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'

import { uxIllustration } from '~/assets/images'
import { ActionStepper } from '~/components/action-stepper'
import { BusinessDescription } from '~/components/business-description'
import { CardsWithTitle } from '~/components/cards-with-title'
import { bigCards } from '~/data/ui-ux-for-crypto/big-cards'
import { design } from '~/data/ui-ux-for-crypto/design'
import { stepperData } from '~/data/ui-ux-for-crypto/stepper'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { BigCard, Button, H2, Hero, HeroImage, Text } from '~/tenset-components'
import { withCache } from '~/utils'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.BUSINESS)

  const title = t('ui-ux.meta.title')
  const description = t('ui-ux.meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.BUSINESS, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}
export default function SmartContractDevelopment() {
  const { t } = useTranslation([Namespace.BUSINESS, Namespace.COMMON])
  const localizePathname = useLocalizePathname()

  return (
    <div className="container gap-32">
      <div className="flex flex-col gap-16">
        <Hero
          title={t('ui-ux.hero.title')}
          description={t('ui-ux.hero.description')}
          rightContent={
            <HeroImage src={uxIllustration} alt={t('ui-ux.hero.title')} />
          }
          leftContent={
            <>
              <Button to={localizePathname('/contact')}>
                {t('ui-ux.hero.action')}
              </Button>
            </>
          }
        />

        <BusinessDescription
          title={t('ui-ux.description.title')}
          description={t('ui-ux.description.description')}
        />
      </div>

      <CardsWithTitle title={t('ui-ux.design.title')} cards={design(t)} />

      <div className="mt-8 flex flex-col gap-8 md:mt-0 md:gap-12">
        <H2 isBold>{t('ui-ux.conversion.title')}</H2>

        {/*<div className="grid gap-2 sm:grid-cols-2 md:grid-cols-3 md:gap-4 lg:grid-cols-4">*/}
        {/*  {conversionData.map(({ label, data }) => (*/}
        {/*    <DataPoint label={t(label)} key={label}>*/}
        {/*      {data}*/}
        {/*    </DataPoint>*/}
        {/*  ))}*/}
        {/*</div>*/}

        <div className="flex flex-col gap-8 lg:gap-6">
          {bigCards.map(card => (
            <BigCard
              key={card.title}
              cover={
                <img src={card.image} alt={t(card.title)} className="w-full" />
              }
              title={t(card.title)}
              description={t(card.description)}
            />
          ))}
        </div>
      </div>

      <div className="flex flex-col gap-12">
        <ActionStepper
          title={t('ui-ux.stepper.title')}
          steps={stepperData.map(step => {
            return {
              title: t(step.title),
              timeline: t(step.timeline),
              content: <Text>{t(step.content)}</Text>,
            }
          })}
          action={{
            label: t('ui-ux.stepper.action'),
            to: localizePathname('/contact'),
          }}
        />
      </div>
    </div>
  )
}
