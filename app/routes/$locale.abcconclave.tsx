import type { ActionFunction, LoaderFunctionArgs } from '@remix-run/node'
import { json, useFetcher, useLoaderData } from '@remix-run/react'
import { verifyMessage } from 'ethers/lib/utils'
import type { ChangeEvent, ReactElement } from 'react'
import { useEffect, useState } from 'react'

import {
  fetchSignedMessage,
  postSignedMessage,
  fetchGlpSubscriptionInfo,
  areTicketsLeft,
  hasPremiumPackage,
} from '~/api/fetchers'
import { ConnectWallet } from '~/components/connect-wallet'
import { detectLocale, i18next, Namespace } from '~/i18n'
import { getSession } from '~/sessions.server'
import {
  Button,
  ButtonVariant,
  Card,
  H2,
  Input,
  Text,
  TextS,
} from '~/tenset-components'
import { useWallet } from '~/tenset-web3'
import {
  formDataToObject,
  isFormOk,
  schema,
  translateValidations,
  validate,
  validateExistanceAndNoEmptiness,
  validateIfEthAddress,
} from '~/utils'

interface TicketGiveAwayData {
  address: string
  signature: string
  message: string
}

const verificationTicketGiveAwayFormSchema = schema({
  address: [validateIfEthAddress],
  signature: [validateExistanceAndNoEmptiness],
  message: [validateExistanceAndNoEmptiness],
})

export const action: ActionFunction = async ({ request }) => {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.FORM_VALIDATORS)

  const data: TicketGiveAwayData = formDataToObject(
    await request.formData(),
    Object.keys(verificationTicketGiveAwayFormSchema)
  )

  const validations = validate(data, verificationTicketGiveAwayFormSchema)
  const formOk = isFormOk(validations)

  if (!formOk) {
    return json({
      validations: translateValidations(t, validations),
      ok: false,
    })
  }

  const { address, message, signature } = data

  const signingAddress = verifyMessage(message, signature)

  const subscriptionInfo = await fetchGlpSubscriptionInfo(signingAddress)

  const hasDesiredPackage = await hasPremiumPackage(signingAddress)

  const isEligable = subscriptionInfo?.nfts?.length > 0 || hasDesiredPackage

  if (!isEligable) {
    throw new Response('Not eligible', {
      status: 400,
    })
  }

  if (signingAddress.toLowerCase() !== address.toLowerCase()) {
    throw new Response('Signing address mismatch', {
      status: 400,
    })
  }

  const hasTickets = await areTicketsLeft()

  if (!hasTickets) {
    throw new Response('No more tickets available', {
      status: 400,
    })
  }

  const result = await postSignedMessage({
    address,
    message,
    signature,
  })

  return json({ ok: result })
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get('Cookie'))
  const walletAddress = session.get('walletAddress')

  if (!walletAddress) {
    return json({
      isSigned: false,
      isEligable: false,
      hasTickets: false,
    })
  }

  const isSigned = await fetchSignedMessage(walletAddress as string)

  const hasTickets = await areTicketsLeft()

  const subscriptionInfo = await fetchGlpSubscriptionInfo(
    walletAddress as string
  )

  const hasDesiredPackage = await hasPremiumPackage(walletAddress as string)

  console.log(subscriptionInfo.nfts, hasDesiredPackage)

  const isEligable = subscriptionInfo?.nfts?.length > 0 || hasDesiredPackage

  return json({
    isSigned,
    isEligable,
    hasTickets,
  })
}

export default function TicketsGiveAwayPage() {
  const { isSigned, isEligable, hasTickets } = useLoaderData<typeof loader>()
  const { wallet } = useWallet()
  const fetcher = useFetcher()

  const [fullname, setFullname] = useState('')
  const [email, setEmail] = useState('')

  const onFullnameInput = (event: ChangeEvent<HTMLInputElement>) => {
    setFullname(event.target.value)
  }

  const onEmailInput = (event: ChangeEvent<HTMLInputElement>) => {
    setEmail(event.target.value)
  }

  const onSubmit = async () => {
    if (!wallet?.isConnected) return

    const message = `${fullname}\n${email}`

    const signature = await wallet?.signMessage(message)

    if (!signature) return

    fetcher.submit(
      {
        address: wallet?.account,
        message,
        signature,
      },
      { method: 'post' }
    )
  }

  useEffect(() => {
    if (fetcher.data && fetcher.state === 'idle') {
      setFullname('')
      setEmail('')
    }
  }, [fetcher.data, fetcher.state])

  if (!wallet?.isConnected) {
    return (
      <Container>
        <div>
          <ConnectWallet buttonConnectLabel={'Connect Wallet'} />
        </div>
      </Container>
    )
  }

  if (!isEligable) {
    return (
      <Container>
        <Text>You are not eligible for a free ticket.</Text>
      </Container>
    )
  }

  if (!hasTickets) {
    return (
      <Container>
        <Text>No more tickets available</Text>
      </Container>
    )
  }

  if (isSigned) {
    return (
      <Container>
        <Text>You have already applied. Thank you!</Text>
      </Container>
    )
  }

  return (
    <Container>
      <>
        <Text>
          You are eligible for a free VIP ticket! The conference takes place
          November 11-12 in Bangkok, fill in the form below if you would like to
          attend! You can find out more information about the event here:{' '}
          <Button
            to="https://www.abcconclave.com/bangkok"
            variant={ButtonVariant.Link}
          >
            https://www.abcconclave.com/bangkok
          </Button>
        </Text>
        <div className="mt-16">
          <Card title={'Contact Details'}>
            <div className="flex flex-col gap-4 mt-2">
              <div className="flex flex-col gap-2">
                <TextS isBold className="text-start">
                  Full name:
                </TextS>
                <Input
                  name="fullname"
                  placeholder="Your name"
                  value={fullname}
                  onInput={onFullnameInput}
                />
              </div>
              <div className="flex flex-col gap-2">
                <TextS isBold className="text-start">
                  Email:
                </TextS>
                <Input
                  name="email"
                  placeholder="Your email"
                  value={email}
                  onInput={onEmailInput}
                />
              </div>
              <div>
                <Button onClick={onSubmit}>Submit</Button>
              </div>
            </div>
          </Card>
        </div>
      </>
    </Container>
  )
}

function Container({ children }: { children: ReactElement }) {
  return (
    <div className="container min-h-[50vh]">
      <div className="flex flex-col gap-4 max-w-[600px]">
        <H2>ABC Conclave Bangkok Giveaway</H2>
        {children}
      </div>
    </div>
  )
}
