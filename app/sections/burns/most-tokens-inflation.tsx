import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import {
  CurrencyFormatter,
  H2,
  Image,
  NumberFormatter,
  TextNumeric,
} from '~/tenset-components'
import { BuybacksDataSection } from '~/sections/homepage/buybacks/data'
import { mostTokensInflation } from '~/data'
import { mostTokensInflationBanner } from '~/assets/images'

export function BurnsMostTokensInflation() {
  const { t } = useTranslation([Namespace.BURNS])

  const mostTokensInflationMap = mostTokensInflation.map(
    ({ token, value, inflationPercentage }) => ({
      label: token,
      data: [
        {
          value,
          formatter: ({ value }: { value: number }) => (
            <CurrencyFormatter value={value} currency="USD" />
          ),
        },
        {
          value: inflationPercentage,
          formatter: ({ value }: { value: number }) => (
            <TextNumeric>
              <NumberFormatter value={value} suffix="% Inflation" />
            </TextNumeric>
          ),
        },
      ],
    })
  )

  return (
    <div className="flex flex-col gap-8 lg:gap-12">
      <H2 isBold>{t('most-tokens-inflation.title')}</H2>

      <div className="rounded-2xl overflow-hidden">
        <Image
          src={mostTokensInflationBanner}
          alt={t('most-tokens-inflation.title')}
          width="100%"
        />
      </div>

      <BuybacksDataSection burnsData={mostTokensInflationMap} wider />
    </div>
  )
}
