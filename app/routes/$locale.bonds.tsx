import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'

import { BusinessDescription } from '~/components/business-description'
import { CardsWithTitle } from '~/components/cards-with-title'
import { ecosystem } from '~/data/bonds/ecosystem'
import { stepperData } from '~/data/bonds/stepper'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import {
  Button,
  H2,
  Hero,
  HeroImage,
  Stepper,
  Text,
  TextL,
} from '~/tenset-components'
import { withCache } from '~/utils'
import { bondsIllustration } from '~/assets/images'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.BUSINESS)

  const title = t(
    'bonds.meta.title',
    'Tenset Bonds - The Future of Smart Investing'
  )
  const description = t(
    'bonds.meta.description',
    'A new way to invest in blockchain-based projects through secure and transparent digital bonds. Earn rewards while supporting innovation.'
  )

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.BUSINESS, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function Bonds() {
  const { t } = useTranslation([Namespace.BUSINESS, Namespace.COMMON])
  const localizePathname = useLocalizePathname()

  return (
    <div className="container gap-32">
      <div className="flex flex-col gap-16">
        <Hero
          title={t(
            'bonds.hero.title',
            'Tenset Bonds - The Future of Smart Investing'
          )}
          description={t(
            'bonds.hero.description',
            'A new way to invest in blockchain-based projects through secure and transparent digital bonds. Earn rewards while supporting innovation.'
          )}
          rightContent={
            <HeroImage
              src={bondsIllustration}
              alt={t('bonds.hero.title', 'Tenset Bonds')}
            />
          }
          leftContent={
            <>
              <Button to={localizePathname('/contact')}>
                {t('bonds.hero.action', 'Create a Bond')}
              </Button>
            </>
          }
        />
        <BusinessDescription
          title={t('bonds.description.title', 'What Are Tenset Bonds?')}
          description={t(
            'bonds.description.description',
            'Tenset Bonds are blockchain-powered digital bonds that allow investors to fund exciting projects while earning potential returns. Unlike traditional financial bonds, which rely on banks or intermediaries, Tenset Bonds operate on smart contracts, ensuring secure, automated, and transparent transactions.'
          )}
        />
      </div>

      <CardsWithTitle
        title={t('bonds.ecosystem.title', 'Why Use Tenset Bonds?')}
        cards={ecosystem(t)}
        cardIconSize="small"
      />

      <div className="flex flex-col gap-12">
        <H2 isBold>{t('bonds.stepper.title', 'How it works?')}</H2>

        <Stepper
          steps={stepperData.map(step => {
            return {
              title: t(step.title, step.defaultTitle),
              content: <Text>{t(step.content, step.defaultContent)}</Text>,
            }
          })}
        />
      </div>

      <div className="flex flex-col gap-6 md:items-center md:text-center">
        <div className="flex flex-col gap-4">
          <header>
            <H2 isBold>
              {t('bonds.start-investing.title', 'Start Investing Today!')}
            </H2>
          </header>

          <main className="max-w-[800px]">
            <TextL>
              {t(
                'bonds.start-investing.description',
                'Join the new era of investing with Tenset Bonds and grow your wealth securely with blockchain-backed opportunities.'
              )}
            </TextL>
          </main>
        </div>

        <Button to={localizePathname('/contact')}>
          {t('bonds.hero.action', 'Create a Bond')}
        </Button>
      </div>
    </div>
  )
}
