import type { PostDataType, PostsPaginationType } from '~/api/types'
import {
  renderMarkdown,
  sanitizeDescriptionToRawString,
  sanitizeHTML,
} from '~/utils'

export async function getPostsList(
  baseUrl: string,
  page: number,
  pageSize: number
): Promise<PostsPaginationType | undefined> {
  const POSTS_LIST_URL = `${baseUrl}/api/posts`

  const parameters = decodeURIComponent(
    new URLSearchParams({
      populate: 'cover',
      'pagination[page]': page.toString(),
      'pagination[pageSize]': pageSize.toString(),
      'sort[0]': 'date:desc',
      'sort[1]': 'publishedAt:desc',
    }).toString()
  )

  const posts = await fetch(`${POSTS_LIST_URL}?${parameters}`)
    .then<PostsPaginationType>(response => response.json())
    .catch(error => {
      console.warn(error)
    })

  if (!posts || !posts.data) return undefined

  const now = new Date()

  return {
    meta: posts.meta,
    data: posts.data
      .filter((post: PostDataType) => {
        if (post.attributes.PublicationDate === null) return true

        return new Date(post.attributes.PublicationDate) < now
      })
      .map((post: PostDataType) => {
        const body = renderMarkdown(post.attributes.body) as string

        post.attributes.body = sanitizeHTML(body)
        post.attributes.description = sanitizeDescriptionToRawString(body)

        return post
      }),
  }
}
