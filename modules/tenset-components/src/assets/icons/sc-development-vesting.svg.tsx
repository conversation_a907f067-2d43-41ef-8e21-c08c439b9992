import { AssetProps } from './index'

export default function ScDevelopmentVesting({
  size,
  color,
  className,
}: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M56.2875 36.476C52.0036 33.2714 49.2372 28.163 49.2372 22.411C49.2372 12.717 57.1067 4.85987 66.8158 4.85987C76.525 4.85987 84.3944 12.717 84.3944 22.411C84.3944 28.163 81.628 33.2714 77.3442 36.476"
        fill={color ?? '#15121A'}
      />
      <path
        d="M16.6316 38.514L62.0217 84.2756V100.888L71.61 95.6992V84.2756C79.4525 76.3648 117 38.514 117 38.514L116.96 38.4738C116.745 38.6481 116.49 38.7956 116.194 38.9162L71.5025 55.3008C68.481 56.4137 65.1506 56.4137 62.1291 55.3008L17.4374 38.9162C17.1554 38.809 16.9136 38.6749 16.6988 38.5006"
        fill={color ?? '#15121A'}
      />
      <path
        d="M72.9797 121.496C72.9797 124.888 70.2268 127.65 66.8158 127.65C63.4048 127.65 60.6519 124.902 60.6519 121.496C60.6519 115.074 66.8158 107.847 66.8158 107.847C66.8158 107.847 72.9797 114.672 72.9797 121.496Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M67.3798 117.549C67.447 117.87 67.729 118.085 68.0378 118.085C68.0781 118.085 68.1318 118.085 68.1721 118.085C68.5347 118.018 68.7764 117.656 68.6959 117.294C68.3601 115.604 67.6215 113.794 66.4935 111.931C66.3055 111.609 65.8892 111.515 65.5669 111.703C65.2446 111.89 65.1506 112.306 65.3386 112.628C66.3861 114.357 67.071 116.02 67.3798 117.562V117.549ZM64.7343 105.133C64.5463 105.133 64.3583 105.227 64.2374 105.374C63.9823 105.682 57.9124 112.856 57.9124 119.452C57.9124 123.22 60.9742 126.277 64.7477 126.277C68.5213 126.277 71.5831 123.22 71.5831 119.452C71.5831 112.453 65.4998 105.642 65.2446 105.361C65.1238 105.213 64.9357 105.133 64.7477 105.133H64.7343ZM64.7343 124.923C61.7128 124.923 59.2418 122.469 59.2418 119.439C59.2418 114.398 63.3108 108.713 64.7477 106.849C66.1981 108.632 70.2268 114.089 70.2268 119.439C70.2268 122.456 67.7693 124.923 64.7343 124.923ZM114.341 31.3752L82.9575 19.8711C82.6889 10.0565 74.6315 2.13234 64.7209 2.13234C54.8103 2.13234 46.7529 10.0431 46.4843 19.8711L15.1007 31.3752C13.7981 31.8579 12.9655 33.0512 12.9655 34.4322C12.9655 35.3172 13.3146 36.1351 13.9055 36.725C13.9324 36.8055 13.9861 36.8725 14.0398 36.9395L59.2418 82.5134V98.8443C59.2418 99.0857 59.3627 99.3002 59.5641 99.4209C59.6716 99.4879 59.7924 99.5147 59.9133 99.5147C60.0207 99.5147 60.1282 99.4879 60.2356 99.4343L69.8239 94.2454C70.0388 94.1247 70.1731 93.8968 70.1731 93.6554V82.5134L115.375 36.9395C115.375 36.9395 115.483 36.8055 115.509 36.725C116.1 36.1351 116.449 35.3172 116.449 34.4322C116.449 33.0512 115.617 31.8579 114.314 31.3752H114.341ZM64.7209 3.47314C74.054 3.47314 81.6414 11.0487 81.6414 20.3538C81.6414 25.65 79.2242 30.5171 75.0075 33.7484H54.4343C50.2176 30.5171 47.8003 25.6634 47.8003 20.3538C47.8003 11.0487 55.3877 3.47314 64.7074 3.47314H64.7209ZM69.045 81.7491C68.9241 81.8698 68.857 82.0441 68.857 82.2184V93.2398L60.6116 97.7047V82.2184C60.6116 82.0441 60.5444 81.8698 60.4236 81.7491L17.303 38.2669L59.8193 53.847C61.4039 54.4236 63.0691 54.7186 64.7477 54.7186C66.4264 54.7186 68.0781 54.4236 69.6628 53.847L112.179 38.2669L69.0584 81.7491H69.045ZM113.871 36.2155L69.1793 52.6001C66.3055 53.6593 63.1363 53.6593 60.2624 52.6001L15.5707 36.2155C14.6441 35.8803 14.3084 35.0624 14.3084 34.4188C14.3084 33.7752 14.6441 32.9574 15.5707 32.6222L46.5111 21.279C46.7529 26.0925 48.8209 30.5171 52.3528 33.7484H43.315C42.939 33.7484 42.6436 34.0434 42.6436 34.4188C42.6436 34.7943 42.939 35.0892 43.315 35.0892H86.1267C86.5027 35.0892 86.7982 34.7943 86.7982 34.4188C86.7982 34.0434 86.5027 33.7484 86.1267 33.7484H77.089C80.6208 30.5171 82.6889 26.0925 82.9306 21.279L113.871 32.6222C114.798 32.9574 115.133 33.7752 115.133 34.4188C115.133 35.0624 114.798 35.8803 113.871 36.2155ZM62.3171 28.3316V29.8869C62.3171 30.2624 62.6125 30.5573 62.9885 30.5573C63.3645 30.5573 63.66 30.2624 63.66 29.8869V28.5461C63.9017 28.5595 64.1569 28.5863 64.412 28.5863C64.882 28.5863 65.3386 28.5461 65.7818 28.4791V29.9003C65.7818 30.2758 66.0772 30.5707 66.4532 30.5707C66.8292 30.5707 67.1247 30.2758 67.1247 29.9003V28.1573C69.3942 27.4199 70.9788 25.7975 70.9788 23.907C70.9788 21.1449 67.6081 19.5359 64.4657 19.2276C62.4111 18.9996 60.2222 18.1147 60.2222 16.3046C60.2222 14.6956 62.0888 13.3817 64.3986 13.3817C66.0772 13.3817 67.5813 14.0923 68.2393 15.1783C68.4407 15.5001 68.8436 15.594 69.1659 15.4063C69.4882 15.2186 69.5822 14.8029 69.3942 14.4811C68.9107 13.6766 68.105 13.0331 67.1381 12.6174V10.7403C67.1381 10.3649 66.8427 10.0699 66.4666 10.0699C66.0906 10.0699 65.7952 10.3649 65.7952 10.7403V12.1883C65.352 12.0945 64.882 12.0409 64.3986 12.0409C64.1434 12.0409 63.9017 12.0677 63.66 12.0945V10.7269C63.66 10.3515 63.3645 10.0565 62.9885 10.0565C62.6125 10.0565 62.3171 10.3515 62.3171 10.7269V12.3627C60.3027 12.9928 58.8793 14.5213 58.8793 16.3046C58.8793 18.5572 60.9607 20.1929 64.3449 20.5549C66.9769 20.8231 69.6359 22.0566 69.6359 23.907C69.6359 25.7573 67.2455 27.2455 64.412 27.2455C62.4111 27.2455 60.5445 26.4813 59.6716 25.3014C59.4433 25.0064 59.027 24.9394 58.7315 25.1539C58.4361 25.3684 58.369 25.7975 58.5838 26.0925C59.3761 27.1785 60.7325 27.9562 62.3037 28.3316H62.3171Z"
        fill={color ?? 'url(#paint0_linear_940_1132)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_940_1132"
          x1="26.2333"
          y1="105.133"
          x2="81.6187"
          y2="9.05628"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
