import { useFetcher, useSearchParams } from '@remix-run/react'
import { useTranslation } from 'react-i18next'

import type { GlpAffiliateCodeWithParameter } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import { Button, ButtonVariant, Text } from '~/tenset-components'

interface AffiliateInfoProps {
  selectedAffiliateOffer: GlpAffiliateCodeWithParameter
}

export function AffiliateInfo({ selectedAffiliateOffer }: AffiliateInfoProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  const fetcher = useFetcher()

  const [, setSearchParameters] = useSearchParams()

  if (!selectedAffiliateOffer) return null

  const {
    commissionNumerator,
    commissionDenominator,
    bonusNumerator,
    bonusDenominator,
  } = selectedAffiliateOffer

  function resetAffiliateCode() {
    fetcher.submit(
      {},
      {
        method: 'post',
        action: `/api/gem-launch-platform/reset-affiliate-code`,
      }
    )

    setSearchParameters(previous => {
      previous.delete('affiliate_code')

      return previous
    })
  }

  return (
    <div className="md:fixed md:left-0 md:top-[64px] bg-neutral-500 w-full flex gap-4 items-center justify-center p-3 z-20 flex-wrap text-center">
      <Text>
        {t('affiliate.info', {
          reducedPercentage:
            (commissionNumerator / commissionDenominator) * 100,
          higherAllocationPercentage: (bonusNumerator / bonusDenominator) * 100,
        })}
      </Text>

      <Button onClick={resetAffiliateCode} variant={ButtonVariant.Secondary}>
        {t('affiliate.cancel')}
      </Button>
    </div>
  )
}
