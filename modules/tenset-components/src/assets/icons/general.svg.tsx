import { AssetProps } from './index'

export default function General({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M72.8581 44.2003V22.3719C72.8581 16.5798 68.1649 11.8839 62.3761 11.8839H16.3637C10.5749 11.8839 5.88171 16.5798 5.88171 22.3719V44.2003C5.88171 49.9924 10.5749 54.6883 16.3637 54.6883H48.6752L59.4547 66.6783V54.6883H62.3897C68.1784 54.6883 72.8716 49.9924 72.8716 44.2003H72.8581Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M114.083 74.9333H76.8209C72.1413 74.9333 68.3407 78.7361 68.3407 83.4184V101.092C68.3407 105.775 72.1413 109.577 76.8209 109.577H79.1878V119.28L87.9115 109.577H114.069C118.749 109.577 122.549 105.775 122.549 101.092V83.4184C122.549 78.7361 118.749 74.9333 114.069 74.9333H114.083Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M110.877 71.0495H73.6155C68.5571 71.0495 64.459 75.1634 64.459 80.2112V97.885C64.459 102.946 68.5706 107.047 73.6155 107.047H75.3061V116.073C75.3061 116.357 75.482 116.601 75.7389 116.709C75.8201 116.736 75.9012 116.75 75.9824 116.75C76.1717 116.75 76.3476 116.669 76.4828 116.52L85.0036 107.047H110.864C115.922 107.047 120.02 102.933 120.02 97.885V80.2112C120.02 75.1499 115.909 71.0495 110.864 71.0495H110.877ZM118.681 97.8986C118.681 102.202 115.178 105.707 110.877 105.707H84.7196C84.5303 105.707 84.3409 105.788 84.2192 105.937L76.6722 114.327V106.384C76.6722 106.005 76.3746 105.707 75.9959 105.707H73.629C69.328 105.707 65.825 102.202 65.825 97.8986V80.2247C65.825 75.9213 69.328 72.4163 73.629 72.4163H110.891C115.192 72.4163 118.695 75.9213 118.695 80.2247V97.8986H118.681ZM93.7679 93.2298H76.7128C76.334 93.2298 76.0365 93.5275 76.0365 93.9064C76.0365 94.2853 76.334 94.583 76.7128 94.583H93.7679C94.1466 94.583 94.4442 94.2853 94.4442 93.9064C94.4442 93.5275 94.1466 93.2298 93.7679 93.2298ZM106.387 84.9071H76.7128C76.334 84.9071 76.0365 85.2048 76.0365 85.5837C76.0365 85.9626 76.334 86.2604 76.7128 86.2604H106.387C106.766 86.2604 107.063 85.9626 107.063 85.5837C107.063 85.2048 106.766 84.9071 106.387 84.9071ZM70.3289 40.993V19.1646C70.3289 13.0071 65.3246 8 59.1707 8H13.1582C7.00429 8 2 13.0071 2 19.1646V40.993C2 47.1505 7.00429 52.1576 13.1582 52.1576H45.1722L55.7488 63.9176C55.8841 64.0665 56.0599 64.1477 56.2492 64.1477C56.3304 64.1477 56.4115 64.1342 56.4927 64.1071C56.7497 64.0124 56.9255 63.7553 56.9255 63.4711V52.1576H59.1842C65.3381 52.1576 70.3424 47.1505 70.3424 40.993H70.3289ZM59.1707 50.8043H56.2357C55.857 50.8043 55.5595 51.1021 55.5595 51.481V61.6983L45.9566 51.0209C45.8349 50.872 45.6456 50.7908 45.4562 50.7908H13.1582C7.74818 50.7908 3.35251 46.3926 3.35251 40.9795V19.1646C3.35251 13.7515 7.74818 9.35328 13.1582 9.35328H59.1842C64.5942 9.35328 68.9899 13.7515 68.9899 19.1646V40.993C68.9899 46.4062 64.5942 50.8043 59.1842 50.8043H59.1707ZM54.491 19.76H17.8379C17.4592 19.76 17.1617 20.0578 17.1617 20.4367C17.1617 20.8156 17.4592 21.1133 17.8379 21.1133H54.491C54.8697 21.1133 55.1672 20.8156 55.1672 20.4367C55.1672 20.0578 54.8697 19.76 54.491 19.76ZM54.491 29.2871H17.8379C17.4592 29.2871 17.1617 29.5849 17.1617 29.9638C17.1617 30.3427 17.4592 30.6404 17.8379 30.6404H54.491C54.8697 30.6404 55.1672 30.3427 55.1672 29.9638C55.1672 29.5849 54.8697 29.2871 54.491 29.2871ZM38.8965 38.8143H17.8379C17.4592 38.8143 17.1617 39.112 17.1617 39.4909C17.1617 39.8698 17.4592 40.1675 17.8379 40.1675H38.8965C39.2752 40.1675 39.5728 39.8698 39.5728 39.4909C39.5728 39.112 39.2752 38.8143 38.8965 38.8143Z"
        fill="url(#paint0_linear_819_784)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_819_784"
          x1="39.397"
          y1="95.7333"
          x2="83.0934"
          y2="20.0929"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
