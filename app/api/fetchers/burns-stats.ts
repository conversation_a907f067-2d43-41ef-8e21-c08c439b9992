import { adaptBurnsStats } from '~/api/adapters'
import type { BurnsStatsApiData, BurnsStatsApiResponse } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function fetchBurnsStats(): Promise<BurnsStatsApiData> {
  const BURNS_STATS_URL = `${getServerEnvironment().DATA2_TENSET_URL}burns-stats`

  return await fetch(BURNS_STATS_URL)
    .then<BurnsStatsApiResponse>(response => response.json())
    .then(adaptBurnsStats)
}
