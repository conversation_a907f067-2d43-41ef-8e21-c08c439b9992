import { AssetProps } from './index'

export default function MarketingDS({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M114.249 27.6046C115.959 25.1795 115.397 21.8294 112.977 20.1168L104.168 13.854C101.747 12.1414 98.4035 12.7039 96.6942 15.129L92.5642 20.9543L79.6006 39.2552H29.1058V16.7291C22.8922 16.7291 17.864 21.7669 17.864 27.9922V108.521C17.864 114.746 22.8922 119.784 29.1058 119.784H112.44V39.2552H106.002L110.132 33.4299L114.262 27.6046H114.249Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M72.0869 70.9761C72.1989 71.0509 72.3233 71.0883 72.4477 71.0883C72.6467 71.0883 72.8333 71.0011 72.9577 70.8265L97.6759 35.9206C97.8749 35.6464 97.8127 35.2474 97.5266 35.048C97.2405 34.8485 96.8549 34.9108 96.6558 35.1976L71.9376 70.1035C71.7386 70.3778 71.8008 70.7767 72.0869 70.9761ZM112.231 25.6857C114.134 22.993 113.499 19.253 110.812 17.3457L102.03 11.1C99.3428 9.19266 95.6109 9.82844 93.7075 12.5212L89.5899 18.3305L76.8514 36.3195H27.4524V14.4784C27.4524 14.1294 27.1787 13.8551 26.8304 13.8551C20.3119 13.8551 15 19.1782 15 25.7106V106.019C15 112.552 20.3119 117.875 26.8304 117.875H109.917C110.265 117.875 110.539 117.6 110.539 117.251V36.9429C110.539 36.5938 110.265 36.3195 109.917 36.3195H104.704L108.125 31.495L112.243 25.6857H112.231ZM16.244 25.7106C16.244 20.0758 20.6602 15.4633 26.2084 15.1391V94.201C21.842 94.4254 18.0975 97.0434 16.244 100.758V25.7106ZM109.295 37.5662V116.628H26.8304C20.9961 116.628 16.244 111.866 16.244 106.019C16.244 100.172 20.9961 95.4103 26.8304 95.4103C27.1787 95.4103 27.4524 95.136 27.4524 94.787V37.5662H75.9806L53.0538 69.9414C53.0538 69.9414 53.0538 69.9539 53.0538 69.9664C53.0289 70.0038 53.0165 70.0536 52.9916 70.1035C52.9792 70.1409 52.9667 70.1783 52.9667 70.2157C52.9667 70.2282 52.9667 70.2406 52.9667 70.2531L51.1505 91.3961C51.1256 91.6205 51.2251 91.8324 51.4117 91.9571C51.5237 92.0319 51.6481 92.0693 51.7725 92.0693C51.8596 92.0693 51.9466 92.0568 52.0213 92.0194L71.3405 83.3303C71.3405 83.3303 71.3654 83.3179 71.3778 83.3054C71.4152 83.2805 71.4525 83.2555 71.4774 83.2306C71.5147 83.2057 71.5396 83.1807 71.5644 83.1433C71.5644 83.1433 71.5893 83.1309 71.5893 83.1184L103.846 37.5787H109.32L109.295 37.5662ZM53.0911 82.9813L54.0863 71.4374L69.8104 82.6198L59.2738 87.357L53.0911 82.9688V82.9813ZM57.9551 87.9678L52.4567 90.4487L52.9667 84.4274L57.9551 87.9678ZM70.9176 81.8842L54.4346 70.1534L90.2617 19.5522L106.745 31.2831L70.9176 81.8842ZM90.9832 18.5425L94.7401 13.2442C96.2453 11.1125 99.206 10.6138 101.333 12.1223L110.116 18.3679C112.243 19.8764 112.741 22.8434 111.235 24.9751L107.479 30.2733L90.9956 18.5425H90.9832ZM101.52 103.389C101.172 103.389 100.898 103.663 100.898 104.012V107.914H95.0884V105.383C95.0884 105.034 94.8147 104.76 94.4664 104.76C94.1181 104.76 93.8444 105.034 93.8444 105.383V107.914H88.5947V105.383C88.5947 105.034 88.321 104.76 87.9727 104.76C87.6244 104.76 87.3507 105.034 87.3507 105.383V107.914H82.1011V105.383C82.1011 105.034 81.8274 104.76 81.4791 104.76C81.1308 104.76 80.8571 105.034 80.8571 105.383V107.914H75.6074V105.383C75.6074 105.034 75.3337 104.76 74.9854 104.76C74.6371 104.76 74.3634 105.034 74.3634 105.383V107.914H69.1138V105.383C69.1138 105.034 68.8401 104.76 68.4918 104.76C68.1435 104.76 67.8698 105.034 67.8698 105.383V107.914H62.6201V105.383C62.6201 105.034 62.3464 104.76 61.9981 104.76C61.6498 104.76 61.3761 105.034 61.3761 105.383V107.914H56.1265V105.383C56.1265 105.034 55.8528 104.76 55.5045 104.76C55.1562 104.76 54.8825 105.034 54.8825 105.383V107.914H49.073V104.012C49.073 103.663 48.7993 103.389 48.451 103.389C48.1027 103.389 47.829 103.663 47.829 104.012V108.537C47.829 108.886 48.1027 109.161 48.451 109.161H101.52C101.868 109.161 102.142 108.886 102.142 108.537V104.012C102.142 103.663 101.868 103.389 101.52 103.389Z"
        fill={color ?? 'url(#paint0_linear_941_1251)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_941_1251"
          x1="32.304"
          y1="122.874"
          x2="98.2588"
          y2="8.87767"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
