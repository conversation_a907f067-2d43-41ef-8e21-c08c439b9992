import { Meta } from '@storybook/react'

import { TagColor, Tag } from './index'

type TagType = typeof Tag

const { NEUTRAL, GREEN, YELLOW, BLUE, VIOLET } = TagColor

export default {
  title: 'Components/Tag',
  component: Tag,
} as Meta<TagType>

export const Green = {
  args: {
    children: 'Label',
    color: GREEN,
  },
}

export const Yellow = {
  args: {
    children: 'Label',
    color: YELLOW,
  },
}

export const Neutral = {
  args: {
    children: 'Label',
    color: NEUTRAL,
  },
}

export const Violet = {
  args: {
    children: 'Exclusive gem',
    color: VIOLET,
  },
}

export const Blue = {
  args: {
    children: 'Label',
    color: BLUE,
  },
}
