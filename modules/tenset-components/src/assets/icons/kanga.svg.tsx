import { AssetProps } from './index'

export default function KangaLogo({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_64_316)">
        <path
          d="M11.984 19.12V19.68C9.64001 19.88 5.86401 18.16 4.86401 14.128C3.87201 10.128 6.44801 6.03199 10.456 5.20799C15.096 4.24799 19.2 7.74399 19.336 12.04C19.96 12.408 20.168 12.808 20.016 13.352C19.856 13.904 19.28 14.216 18.728 14.056C18.376 13.952 18.104 13.68 18.016 13.328C17.88 12.816 18.088 12.408 18.68 12.056C18.656 9.38399 16.52 6.15199 12.752 5.71199C9.16001 5.29599 5.88001 7.80799 5.35201 11.384C5.03201 13.32 5.60001 15.296 6.91201 16.76C8.23201 18.304 9.94401 19.056 11.984 19.12ZM19.016 13.464C19.24 13.48 19.424 13.312 19.44 13.088C19.44 13.08 19.44 13.064 19.44 13.056C19.448 12.824 19.264 12.64 19.032 12.632C19.032 12.632 19.032 12.632 19.024 12.632C18.8 12.64 18.624 12.816 18.608 13.032C18.6 13.264 18.784 13.464 19.016 13.464Z"
          fill={color ?? 'url(#paint0_linear_64_316)'}
        />
        <path
          d="M3.52799 19.424C1.33599 16.784 0.535991 13.728 1.16799 10.368C2.27199 4.44001 7.55199 1.32801 11.728 1.40001C12.112 2.00001 12.504 2.20001 13.032 2.04801C13.584 1.87201 13.888 1.28001 13.712 0.728006C13.608 0.400006 13.352 0.152006 13.032 0.0480063C12.512 -0.103994 12.128 0.0880063 11.712 0.728006C4.81599 0.840006 -0.832009 7.22401 0.559991 14.568C1.79199 21.064 7.71999 24.232 11.984 23.984V23.432C8.56799 23.336 5.71199 22.048 3.52799 19.424ZM12.32 1.02401C12.336 0.808006 12.52 0.632006 12.736 0.632006C12.968 0.640006 13.144 0.832006 13.152 1.06401C13.136 1.29601 12.944 1.47201 12.712 1.46401C12.488 1.45601 12.312 1.26401 12.32 1.04001C12.32 1.03201 12.32 1.02401 12.32 1.02401ZM19.256 5.15201C18.944 4.76001 18.48 4.67201 17.872 4.92001C14.12 2.00001 8.79199 2.28801 5.37599 5.60001C2.31199 8.60801 1.62399 13.352 3.73599 17.04C4.75999 18.872 6.36799 20.312 8.31199 21.12C9.46399 21.624 10.712 21.872 11.968 21.864V21.288C9.67199 21.232 7.66399 20.472 5.98399 18.92C4.32799 17.432 3.30399 15.36 3.14399 13.136C2.82399 9.35201 4.94399 5.77601 8.42399 4.25601C11.704 2.79201 15.328 3.61601 17.456 5.44001C17.392 6.17601 17.576 6.56801 18.072 6.76001C18.504 6.92801 19 6.79201 19.28 6.41601C19.552 6.03201 19.544 5.52001 19.256 5.15201ZM18.448 6.21601C18.224 6.22401 18.032 6.04001 18.024 5.81601C18.024 5.80001 18.024 5.79201 18.024 5.77601C18.032 5.54401 18.224 5.36801 18.448 5.36001C18.688 5.36801 18.872 5.56801 18.872 5.80801C18.864 6.04001 18.68 6.21601 18.448 6.21601ZM10.608 7.40001C9.26399 7.75201 8.13599 8.64801 7.47199 9.86401C7.03199 10.624 6.80799 11.496 6.82399 12.384H7.39999C7.43999 9.83201 9.54399 7.79201 12.104 7.83201C12.336 7.83201 12.576 7.85601 12.8 7.89601C15.2 8.30401 16.856 10.616 16.504 13.096C16.208 15.168 14.032 17.128 12.264 16.904C11.888 16.312 11.48 16.112 10.952 16.264C10.4 16.44 10.096 17.024 10.272 17.576C10.376 17.912 10.656 18.176 11 18.272C11.504 18.408 11.912 18.192 12.288 17.576C13.888 17.44 15.2 16.768 16.16 15.48C17.112 14.256 17.432 12.656 17.032 11.152C16.36 8.38401 13.576 6.68001 10.808 7.35201C10.736 7.36001 10.672 7.37601 10.608 7.40001ZM11.28 17.68C11.048 17.672 10.864 17.496 10.848 17.264C10.848 17.032 11.04 16.848 11.264 16.848C11.488 16.84 11.672 17.016 11.672 17.24C11.672 17.248 11.672 17.256 11.672 17.264C11.672 17.496 11.496 17.672 11.28 17.68ZM11.712 10.016C12.168 10.656 12.544 10.832 13.072 10.648C13.616 10.456 13.896 9.85601 13.704 9.31201C13.592 9.00001 13.336 8.75201 13.016 8.66401C12.488 8.52001 12.12 8.71201 11.712 9.35201C9.69599 9.62401 8.70399 11.416 9.03999 13.024C9.39199 14.648 10.984 15.68 12.608 15.36C14.016 15.072 15.224 13.584 14.984 12.408H14.44C14.304 14.032 13.008 14.92 11.72 14.776C10.504 14.616 9.59999 13.576 9.59199 12.352C9.61599 11.144 10.568 10.096 11.712 10.016ZM12.72 9.26401C12.952 9.25601 13.144 9.44001 13.144 9.67201C13.136 9.90401 12.952 10.088 12.72 10.096C12.488 10.088 12.312 9.89601 12.312 9.66401C12.32 9.44001 12.504 9.25601 12.72 9.26401Z"
          fill={color ?? '#FAFAFA'}
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_64_316"
          x1="4.65587"
          y1="12.3726"
          x2="20.0719"
          y2="12.3726"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F86008" />
          <stop offset="1" stopColor="#F59C00" />
        </linearGradient>
        <clipPath id="clip0_64_316">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
