import type { VoteDTO } from './vote.dto'

export interface VotingOption {
  key: string
  value: string
}

export interface VotingDTO {
  id: string
  image: string
  title: string
  wistiaVideoUrl?: string
  visible: boolean
  startsAt: { seconds: number }
  endsAt: { seconds: number }
  description: string | string[]
  tiers: { [key: number]: number }
  options: { [key: string]: string }
  requirements: string[]
  votes:
    | VoteDTO[]
    | {
        items: { [key: string]: number }
        participants: number
        total: number
      }
}
