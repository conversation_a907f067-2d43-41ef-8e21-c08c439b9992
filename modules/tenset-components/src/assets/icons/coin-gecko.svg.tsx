import { AssetProps } from './index'

export default function CoinGeckoLogo({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '24'}
      height={size ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_631_723)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9685 0.00013593C18.6108 0.00013593 23.9684 5.35778 23.9684 12.0313C23.9684 18.6109 18.5481 24.0312 11.8745 23.9999C5.42031 23.9372 0 18.6109 0 12C0 5.35778 5.32631 -0.0311953 11.9685 0.00013593ZM2.85114 18.2662C2.85114 18.3916 2.9138 18.4542 2.97647 18.5482C3.44644 19.2062 3.97907 19.8328 4.60569 20.3654C4.88767 20.6161 5.16965 20.8667 5.48297 21.0861C6.36024 21.65 7.26885 22.1827 8.27145 22.5273C9.08606 22.8093 9.932 23.0286 10.7779 23.1226C11.4359 23.1853 12.1252 23.2166 12.7831 23.1226C13.1591 23.0599 13.5664 23.0599 13.9424 22.9033C14.0051 22.9346 14.0364 22.8719 14.0677 22.8719C14.851 22.7466 15.603 22.496 16.3549 22.1827C18.7987 21.1174 20.6473 19.3942 21.8379 17.013C22.9031 14.9138 23.2478 12.6893 22.9031 10.3708C22.5898 8.14626 21.6499 6.20372 20.146 4.5745C18.1408 2.28732 15.5716 1.09673 12.5325 0.908742C11.1853 0.814749 9.83801 1.00274 8.55343 1.44137C6.45424 2.13066 4.73102 3.32125 3.32111 5.04447C1.72322 7.01833 0.939937 9.27418 0.845943 11.7807C0.814612 12.4073 0.877275 13.0026 0.971269 13.6292C1.15926 15.0391 1.62922 16.3864 2.34984 17.6083C2.53783 17.7963 2.66316 18.0469 2.85114 18.2662Z"
          fill={color ?? '#8DC63F'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.9424 22.8719C13.5664 23.0599 13.1591 23.0599 12.7831 23.0913C12.1252 23.1853 11.4359 23.1539 10.7779 23.0913C9.932 22.9973 9.08605 22.7779 8.27144 22.496C7.26884 22.1513 6.36023 21.6187 5.48296 21.0547C5.16965 20.8354 4.88767 20.5848 4.60568 20.3341C3.97906 19.8015 3.44643 19.1749 2.97646 18.5169C2.9138 18.4229 2.85114 18.3289 2.85114 18.2349C3.60309 16.543 3.88507 14.7572 4.04172 12.9399C4.10438 12.094 4.13572 11.2481 4.29237 10.4021C4.44903 9.43084 4.825 8.55357 5.51429 7.80162C6.14092 7.14366 6.9242 6.70503 7.77014 6.39171C8.45943 6.10973 9.18005 5.85908 9.96333 5.82775C10.6839 5.79642 11.3732 5.89041 12.0625 6.04707C13.0965 6.29772 14.1304 6.61103 15.1017 7.081C15.8849 7.39431 16.6995 7.70763 17.5142 7.95828C18.3601 8.20892 19.206 8.49091 19.9267 9.02354C20.8353 9.71283 21.0859 10.5901 20.6786 11.6554C20.428 12.3133 19.958 12.8146 19.488 13.3159C18.6421 14.1932 17.7335 15.0078 16.9189 15.9164C15.9789 17.013 15.133 18.1409 14.5377 19.4568C14.0364 20.5534 13.7544 21.6814 13.9424 22.8719ZM19.9893 12.564C19.958 12.564 19.9267 12.564 19.8953 12.5953C19.864 12.5953 19.8327 12.6266 19.8327 12.658C19.8327 12.658 19.8327 12.658 19.8327 12.6893C19.864 12.6893 19.8953 12.658 19.8953 12.6266L19.9893 12.564C20.0207 12.564 20.052 12.564 19.9893 12.564C20.0207 12.5326 19.9893 12.564 19.9893 12.564ZM10.402 7.11233C9.21138 7.11233 8.24011 8.0836 8.24011 9.24286C8.24011 10.4334 9.21138 11.3734 10.3706 11.4047C11.5612 11.4047 12.5012 10.4334 12.5325 9.24286C12.5325 8.05227 11.5926 7.11233 10.402 7.11233ZM12.1565 13.6919C12.3445 13.9739 12.6891 14.2245 13.0965 14.3498C13.9111 14.6318 14.7257 14.6318 15.5403 14.4752C16.9815 14.1932 18.3288 13.6919 19.5507 12.8773C19.6133 12.8146 19.7387 12.8146 19.77 12.6893C19.7073 12.658 19.676 12.6893 19.6447 12.7206C19.2374 12.9713 18.7987 13.1593 18.3601 13.3472C17.2322 13.8172 16.0416 14.1305 14.7883 14.1932C13.8797 14.2559 12.9711 14.1932 12.1565 13.6919ZM18.0781 11.8433C18.3288 11.8433 18.5481 11.624 18.5481 11.342C18.5481 11.0914 18.3288 10.8721 18.0468 10.9034C17.7961 10.9034 17.6082 11.1227 17.6082 11.3734C17.6082 11.6554 17.7961 11.8433 18.0781 11.8433Z"
          fill={color ?? '#8BC53F'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.0312 6.04706C11.3419 5.92173 10.6526 5.7964 9.93201 5.82774C9.14873 5.85907 8.45944 6.10972 7.73882 6.3917C6.89288 6.70501 6.1096 7.14365 5.48297 7.8016C4.79368 8.52222 4.41771 9.3995 4.26105 10.4021C4.13573 11.248 4.1044 12.094 4.0104 12.9399C3.85375 14.7571 3.57177 16.5744 2.81982 18.2349C2.6005 18.0469 2.47517 17.7649 2.34985 17.5456C1.62923 16.3237 1.15926 14.9765 0.971275 13.5666C0.877281 12.9399 0.814618 12.3446 0.845949 11.718C0.939943 9.21151 1.72322 6.95566 3.32112 4.98179C4.69969 3.25857 6.45424 2.06799 8.55343 1.3787C9.83802 0.971394 11.1853 0.783408 12.5325 0.84607C15.5403 1.03406 18.1095 2.25598 20.146 4.48049C21.6499 6.14105 22.5585 8.08359 22.9031 10.2768C23.2478 12.5953 22.9031 14.8198 21.8379 16.919C20.6473 19.3002 18.7987 21.0234 16.3549 22.0887C15.6343 22.402 14.8823 22.6526 14.0677 22.7779C14.0051 22.7779 13.9737 22.8719 13.9424 22.8093C13.7544 21.6187 14.0364 20.4908 14.5064 19.3942C15.1017 18.0783 15.9476 16.9503 16.8875 15.8537C17.6708 14.9451 18.5794 14.0992 19.4567 13.2532C19.958 12.7519 20.3966 12.2506 20.6473 11.5927C21.0859 10.5274 20.8353 9.65015 19.8953 8.96086C19.1747 8.42823 18.3288 8.14625 17.4828 7.8956C16.6682 7.64495 15.8536 7.33164 15.0703 7.01832C14.9137 6.48569 14.5377 6.14105 14.0364 5.95306C13.3784 5.7964 12.7205 5.85907 12.0312 6.04706ZM22.2765 12.7206H22.3392C22.4645 12.282 22.4645 11.812 22.4645 11.342C22.4645 10.8094 22.4018 10.2768 22.3078 9.74414C21.9632 8.02092 21.1799 6.51702 19.958 5.23244C19.2061 4.44916 18.3601 3.79121 17.5142 3.10192C16.7622 2.47529 15.9476 2.00532 15.0077 1.75467C14.0991 1.53536 13.2218 1.41003 12.2819 1.56669C12.2192 1.56669 12.1565 1.56669 12.1565 1.62935C12.1565 1.69201 12.2192 1.69201 12.2819 1.69201C13.6604 1.94266 14.9763 2.3813 16.2609 2.8826C17.5142 3.3839 18.5794 4.10452 19.3627 5.23244C19.8953 6.01573 20.3966 6.83034 20.8039 7.70761C21.3366 8.8982 21.7439 10.1201 21.9945 11.3734C22.1199 11.8747 22.2139 12.282 22.2765 12.7206Z"
          fill={color ?? '#F9E988'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.402 7.11234C11.5926 7.11234 12.5325 8.0836 12.5325 9.24286C12.5325 10.4334 11.5612 11.3734 10.3706 11.4047C9.18005 11.4047 8.24011 10.4334 8.24011 9.24286C8.24011 8.05227 9.21138 7.11234 10.402 7.11234ZM11.9059 9.27419C11.9059 8.42825 11.2479 7.77029 10.402 7.77029C9.55602 7.77029 8.89807 8.42825 8.89807 9.27419C8.89807 10.0888 9.55602 10.7781 10.3706 10.7781H10.402C11.2166 10.7781 11.8745 10.1201 11.9059 9.27419Z"
          fill={color ?? '#FCFDFB'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.1565 13.6919C12.9711 14.1932 13.8797 14.2558 14.7883 14.2245C16.0416 14.1619 17.2322 13.8799 18.3601 13.3786C18.7987 13.1906 19.2374 13.0026 19.6447 12.752C19.676 12.7206 19.7073 12.6893 19.77 12.7206C19.7387 12.8146 19.6447 12.8459 19.5507 12.9086C18.3288 13.7232 16.9815 14.2245 15.5403 14.5065C14.7257 14.6632 13.8797 14.6632 13.0965 14.3812C12.6891 14.2245 12.3132 13.9739 12.1565 13.6919ZM12.0312 6.04706C12.6891 5.85908 13.3784 5.79641 14.0364 6.04706C14.5377 6.23505 14.9137 6.5797 15.0703 7.11233C14.1304 6.61103 13.0965 6.29771 12.0312 6.04706Z"
          fill={color ?? '#585A5B'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.0781 11.8433C17.8275 11.8433 17.6082 11.624 17.6082 11.3734C17.6082 11.1227 17.7961 10.9034 18.0468 10.9034C18.2974 10.9034 18.5168 11.0914 18.5481 11.342C18.5168 11.624 18.3288 11.8433 18.0781 11.8433Z"
          fill={color ?? '#FCFDFB'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19.8953 12.6266C19.8953 12.658 19.864 12.6893 19.8327 12.6893C19.8327 12.6266 19.864 12.5953 19.8953 12.6266ZM19.9893 12.564L19.8953 12.6266L19.864 12.5953C19.9267 12.564 19.958 12.564 19.9893 12.564Z"
          fill={color ?? '#585A5B'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.2765 12.7206C22.1825 12.282 22.0885 11.8747 22.0259 11.436C21.7752 10.1514 21.3366 8.92953 20.8353 7.77028C20.4593 6.893 19.958 6.07839 19.3941 5.29511C18.6108 4.16718 17.5768 3.44656 16.2923 2.94526C14.9763 2.44396 13.6918 1.97399 12.3132 1.75468C12.2505 1.75468 12.1879 1.75468 12.1879 1.69201C12.1879 1.62935 12.2505 1.62935 12.3132 1.62935C13.2218 1.4727 14.1304 1.56669 15.039 1.81734C15.9789 2.06799 16.7936 2.56929 17.5455 3.16458C18.3915 3.85387 19.2374 4.51183 19.9893 5.29511C21.2113 6.57969 21.9945 8.08359 22.3392 9.80681C22.4332 10.3394 22.4958 10.8721 22.4958 11.4047C22.4958 11.8433 22.4958 12.3133 22.3705 12.7833L22.2765 12.7206Z"
          fill={color ?? '#FCFDFB'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9059 9.27418C11.9059 10.0888 11.2479 10.7781 10.4333 10.7781H10.402C9.58736 10.7781 8.89807 10.1201 8.89807 9.27418V9.24285C8.89807 8.39691 9.58736 7.73895 10.402 7.73895C11.2166 7.73895 11.9059 8.42824 11.9059 9.27418Z"
          fill={color ?? '#585A5B'}
        />
      </g>
      <defs>
        <clipPath id="clip0_631_723">
          <rect width="24" height="24" fill={color ?? 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}
