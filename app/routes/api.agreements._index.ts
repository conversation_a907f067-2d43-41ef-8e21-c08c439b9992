import type { AgreementSignature } from '@prisma/client'
import type { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import { verifyMessage } from 'ethers/lib/utils'
import { cors } from 'remix-utils/cors'

import { agreementConfig } from '~/config'
import { database } from '~/database'
import { getAgreement } from '~/routes/api.agreements.helpers'
import { getErrorMessage } from '~/utils'

async function postAgreement({
  wallet,
  message,
  signature,
}: AgreementSignature) {
  const agreementId = agreementConfig.id

  try {
    await database!.agreementSignature.upsert({
      where: {
        walletAgreement: {
          wallet,
          agreementId,
        },
      },
      update: {
        message,
        signature,
      },
      create: {
        agreementId,
        wallet,
        message,
        signature,
        createdAt: new Date(),
      },
    })
  } catch (error) {
    throw json({ error: getErrorMessage(error) }, { status: 409 })
  }

  return json({ message: 'Agreement created' }, { status: 201 })
}

export async function action({ request }: ActionFunctionArgs) {
  const data: AgreementSignature = await request.json()

  if (request.method !== 'POST')
    throw await cors(
      request,
      json({ error: 'Method not allowed' }, { status: 405 })
    )

  await getAgreement(request)

  if (!data.wallet || !data.message || !data.signature) {
    throw await cors(
      request,
      json(
        { error: 'Wallet, message and signature are required' },
        { status: 400 }
      )
    )
  }

  const walletLower = data.wallet.toLowerCase()

  let signingAddress

  try {
    signingAddress = verifyMessage(data.message, data.signature).toLowerCase()
  } catch {
    throw await cors(
      request,
      json({ error: 'Invalid signature' }, { status: 400 })
    )
  }

  if (signingAddress !== walletLower)
    throw await cors(
      request,
      json({ error: 'Message and signature mismatch' }, { status: 400 })
    )

  return await cors(
    request,
    await postAgreement({ ...data, wallet: walletLower })
  )
}

export async function loader({ request }: LoaderFunctionArgs) {
  const agreement = await getAgreement(request)

  const { message } = agreement

  return await cors(request, json({ message }))
}
