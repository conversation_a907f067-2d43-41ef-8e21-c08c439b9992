{"cta": {"always-1-to-1": "Siempre 1:1", "description": "Recibe la misma cantidad de 10set tokens en la misma dirección de billetera. El único costo es cubrir el gas.", "header": "Puente 10set tokens uno a uno", "only-gas-fee": "Solo tarifa de gas", "same-wallet-address": "Misma dirección de billetera"}, "form": {"approve": "Aprobar el puente", "approve-in-progress": "Aprobar en curso", "approved-gt-balance": "Ya aprobado más del saldo.", "bridge": "Puente", "bridge-in-progress": "Puente en progreso", "connect-wallet": "Conecte la billetera al puente", "currently-approved": "Actualmente aprobado:", "dropdown": {"bsc": "Cadena BNB", "eth": "Red principal de Ethereum", "from": "De", "to": "A"}, "enter-an-amount": "Introduce una cantidad", "errors": {"not-enought-assets": "No hay suficientes activos. Reduzca el número o compre más tokens a cambio."}, "input": {"amount": "Cantidad", "balance": "Balance", "max": "máx.", "the-same-amount-of": "la misma cantidad de", "tokens": "fi<PERSON>s", "you-will-get": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "none-of-assets-will-be-bridged": "Ninguno de sus activos será puenteado en esta etapa.", "please-accept-approve": "La transacción confirmada suele tardar unos segundos.", "please-accept-bridge": "La transacción confirmada suele tardar unos minutos.", "please-connect": "Conecte su billetera para comenzar.", "set-unlimited-allowance": "Establecer asignación ilimitada", "switch-network": "Cambiar de red", "toasts": {"bridge-pending": "Los tokens deberían aparecer en su billetera en unos minutos.", "error": "Algo salió mal", "pending": "Por favor acepte la transacción en su billetera.", "received": "Recibido {{amount}} {{symbol}}", "sent": "Enviado {{amount}} {{symbol}}", "success": "Transacción enviada exitosamente.", "waiting": "Transacción en curso"}, "unlimited-allowance": "Asignación ilimitada", "your-assets-will-get": "Sus activos se obtendrán de una red y se enviarán a la otra.", "your-wallet-must-be-connected": "Tu billetera debe estar conectada a la red desde la cual deseas enviar tokens"}}