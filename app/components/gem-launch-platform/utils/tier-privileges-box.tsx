import { parseEther } from 'ethers/lib/utils'
import { useTranslation } from 'react-i18next'

import { PurchaseNftModal } from '~/components/gem-launch-platform'
import type { TierPrivileges } from '~/data/gem-launch-platform'
import { glpSubscribeOffers } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import {
  Button,
  ButtonVariant,
  H3,
  Icon,
  IconName,
  NumberFormatter,
  TextL,
  TextS,
} from '~/tenset-components'

interface TierPrivilegesProps {
  tier: number
  tierPrivileges: TierPrivileges
}

export function TierPrivilegesBox({
  tier,
  tierPrivileges,
}: TierPrivilegesProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])

  const {
    entry,
    accessTime,
    poolWeight,
    claimBack,
    tradableVesting,
    stakingRewards,
  } = tierPrivileges

  return (
    <div className="bg-[#141119] rounded-lg p-4 flex flex-col justify-between gap-4 md:gap-6 min-h-[300px]">
      <div className="flex flex-col gap-4 md:gap-6">
        <H3
          isBold
          className="bg-gradient-to-r from-[#5C2985] to-[#B05CA0] text-[transparent] bg-clip-text"
        >
          {t('tiers-privileges.tier', { tier })}
        </H3>

        <TextL isBold className="flex items-end mx-2">
          {typeof entry === 'number' ? (
            <>
              <NumberFormatter value={entry} />{' '}
              <Icon
                name={IconName.Tenset}
                color="#fafafa"
                size={14}
                className="sm:scale-[1.2] mb-1 sm:mb-2 mx-0.5"
              />
              <TextS tag="span" className="text-neutral-100 ml-1">
                {t('tiers-privileges.entry.tensets-locked')}
              </TextS>
            </>
          ) : (
            entry
          )}
        </TextL>

        <div className="text-neutral-100 divide-y mx-4">
          <TextS className="py-2 flex gap-2 justify-between items-center">
            {t('tiers-privileges.pool-weight')}{' '}
            <TextL tag="span" className="text-white" isBold>
              {poolWeight}x
            </TextL>
          </TextS>

          <TextS className="py-2">{accessTime}</TextS>
          {claimBack && (
            <TextS className="py-2">{t('tiers-privileges.claim-back')}</TextS>
          )}

          {tradableVesting && (
            <TextS className="py-2">
              {t('tiers-privileges.tradable-vesting')}
            </TextS>
          )}

          {stakingRewards && (
            <TextS className="py-2">
              {t('tiers-privileges.staking-rewards')}
            </TextS>
          )}
        </div>
      </div>

      {typeof entry === 'number' ? (
        <TierPrivilegesLockButton entry={entry} />
      ) : (
        <PurchaseNftModal showTier={tier} />
      )}
    </div>
  )
}

type TierPrivilegesLockButtonProps = Pick<TierPrivileges, 'entry'>

function TierPrivilegesLockButton({ entry }: TierPrivilegesLockButtonProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  const localizePathname = useLocalizePathname()

  if (typeof entry !== 'number') return null

  const _offer = glpSubscribeOffers.find(offer =>
    offer.price.eq(parseEther(entry.toString()))
  )!

  if (!_offer) return null

  const { id: offerId } = _offer

  const LOCK_URL = localizePathname('/gem-launch-platform/panel/subscribe')

  return (
    <Button variant={ButtonVariant.Secondary} to={`${LOCK_URL}?id=${offerId}`}>
      {t('lock')}
    </Button>
  )
}
