import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTranslation } from 'react-i18next'

import { scDevelopmentIllustration } from '~/assets/images'
import { ActionStepper } from '~/components/action-stepper'
import { BusinessDescription } from '~/components/business-description'
import { CardsWithTitle } from '~/components/cards-with-title'
import { bigCards } from '~/data/smart-contract-development/big-cards'
import { ecosystem } from '~/data/smart-contract-development/ecosystem'
import { stepperData } from '~/data/smart-contract-development/stepper'
import { Namespace, i18next } from '~/i18n'
import { detectLocale } from '~/i18n/detect-locale'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { BigCard, Button, H2, Hero, HeroImage, Text } from '~/tenset-components'
import { withCache } from '~/utils'

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.BUSINESS)

  const title = t('smart-contract-development.meta.title')
  const description = t('smart-contract-development.meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.BUSINESS, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function SmartContractDevelopment() {
  const { t } = useTranslation([Namespace.BUSINESS, Namespace.COMMON])
  const localizePathname = useLocalizePathname()

  return (
    <div className="container gap-32">
      <div className="flex flex-col gap-16">
        <Hero
          title={t('smart-contract-development.hero.title')}
          description={t('smart-contract-development.hero.description')}
          rightContent={
            <HeroImage
              src={scDevelopmentIllustration}
              alt={t('smart-contract-development.hero.title')}
            />
          }
          leftContent={
            <>
              <Button to={localizePathname('/contact')}>
                {t('smart-contract-development.hero.action')}
              </Button>
            </>
          }
        />
        <BusinessDescription
          title={t('smart-contract-development.description.title')}
          description={t('smart-contract-development.description.description')}
        />
      </div>

      <CardsWithTitle
        title={t('smart-contract-development.ecosystem.title')}
        cards={ecosystem(t)}
      />

      <div className="mt-8 flex flex-col gap-8 md:mt-0 md:gap-12">
        <div className="max-w-[800px]">
          <H2 isBold>
            {t('smart-contract-development.technological-foundations.title')}
          </H2>
        </div>

        {/*<div className="grid gap-2 sm:grid-cols-2 md:grid-cols-3 md:gap-4 lg:grid-cols-4">*/}
        {/*  {technologicalFoundationsData.map(({ label, data }) => (*/}
        {/*    <DataPoint label={t(label)} key={label}>*/}
        {/*      {data}*/}
        {/*    </DataPoint>*/}
        {/*  ))}*/}
        {/*</div>*/}

        <div className="flex flex-col gap-8 lg:gap-6">
          {bigCards.map(card => (
            <BigCard
              key={card.title}
              cover={
                <img src={card.image} alt={t(card.title)} className="w-full" />
              }
              title={t(card.title)}
              description={t(card.description)}
            />
          ))}
        </div>
      </div>

      <div className="flex flex-col gap-12">
        <ActionStepper
          title={t('smart-contract-development.stepper.title')}
          steps={stepperData.map(step => {
            return {
              title: t(step.title),
              timeline: t(step.timeline),
              content: <Text>{t(step.content)}</Text>,
            }
          })}
          action={{
            label: t('smart-contract-development.stepper.action'),
            to: localizePathname('/contact'),
          }}
        />
      </div>
    </div>
  )
}
