{"include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx", "tailwind.config.cjs"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2019", "ES2021.String"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "skipLibCheck": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "module": "ESNext", "resolveJsonModule": true, "target": "ES2019", "strict": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"], "~/tenset-components": ["./modules/tenset-components/src/index.ts"], "~/tenset-components/utils": ["./modules/tenset-components/src/utils/index.ts"], "~/tenset-web3": ["./modules/tenset-web3/lib/index.ts"]}, "noEmit": true}}