import { AssetProps } from './index'

export default function Business({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M104.913 32.3876H68.3543V27.9083C68.3543 25.337 66.2714 23.253 63.7016 23.253H63.4717C63.7828 23.253 64.0398 22.9958 64.0398 22.6846V20.4652C64.0398 17.8804 61.9569 15.7964 59.3736 15.7964H25.0604C22.4771 15.7964 20.3942 17.8804 20.3942 20.4652V22.6846C20.3942 22.9958 20.6512 23.253 20.9623 23.253H20.7324C18.1626 23.253 16.0797 25.337 16.0797 27.9083V112.691H96.7841V95.5318L68.3813 90.5923V79.6307L74.0754 73.2974H104.94C110.472 73.2974 114.948 68.818 114.948 63.2831V42.429C114.948 36.8941 110.472 32.4147 104.94 32.4147L104.913 32.3876Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M54.7209 75.7333H22.7746C22.3959 75.7333 22.0984 76.031 22.0984 76.4099C22.0984 76.7889 22.3959 77.0866 22.7746 77.0866H54.7209C55.0996 77.0866 55.3972 76.7889 55.3972 76.4099C55.3972 76.031 55.0996 75.7333 54.7209 75.7333ZM54.7209 86.6949H22.7746C22.3959 86.6949 22.0984 86.9926 22.0984 87.3715C22.0984 87.7505 22.3959 88.0482 22.7746 88.0482H54.7209C55.0996 88.0482 55.3972 87.7505 55.3972 87.3715C55.3972 86.9926 55.0996 86.6949 54.7209 86.6949ZM63.891 43.9582C61.1454 43.9582 58.9002 46.1911 58.9002 48.9518C58.9002 51.7125 61.1318 53.9454 63.891 53.9454C66.6501 53.9454 68.8817 51.7125 68.8817 48.9518C68.8817 46.1911 66.6501 43.9582 63.891 43.9582ZM63.891 52.5786C61.8893 52.5786 60.2527 50.9547 60.2527 48.9383C60.2527 46.9219 61.8893 45.298 63.891 45.298C65.8927 45.298 67.5292 46.9219 67.5292 48.9383C67.5292 50.9547 65.9062 52.5786 63.891 52.5786ZM101.707 28.5037H65.8251V24.701C65.8251 22.1027 63.9586 19.9374 61.497 19.4638V17.2579C61.497 14.3078 59.1031 11.9125 56.1546 11.9125H21.8549C18.9064 11.9125 16.5125 14.3078 16.5125 17.2579V19.4638C14.0509 19.9374 12.1844 22.1027 12.1844 24.701V109.484C12.1844 109.863 12.482 110.161 12.8607 110.161H79.0797C79.1608 110.201 79.2555 110.215 79.3502 110.215C79.4449 110.215 79.5395 110.188 79.6207 110.161H93.5516C93.9303 110.161 94.2278 109.863 94.2278 109.484V92.3246C94.2278 91.9998 93.9979 91.7156 93.6733 91.6614L65.8251 86.8167V76.6806L71.1404 70.7667H101.707C107.604 70.7667 112.392 65.9761 112.392 60.0758V39.2217C112.392 33.3214 107.604 28.5308 101.707 28.5308V28.5037ZM17.8515 17.2444C17.8515 15.0386 19.6368 13.2522 21.8414 13.2522H56.1546C58.3592 13.2522 60.1445 15.0386 60.1445 17.2444V19.3555H17.865V17.2444H17.8515ZM64.4726 87.358V108.78H13.537V24.6739C13.537 22.4816 15.3223 20.6953 17.5133 20.6953H60.4962C62.6872 20.6953 64.4726 22.4816 64.4726 24.6739V28.4767H57.737C54.6804 28.4767 51.9348 29.7758 49.9872 31.8328H22.7746C22.3959 31.8328 22.0984 32.1305 22.0984 32.5095C22.0984 32.8884 22.3959 33.1861 22.7746 33.1861H48.9051C47.742 34.8912 47.0657 36.9618 47.0657 39.1676V42.7944H22.7746C22.3959 42.7944 22.0984 43.0921 22.0984 43.471C22.0984 43.85 22.3959 44.1477 22.7746 44.1477H47.0657V53.756H22.7746C22.3959 53.756 22.0984 54.0537 22.0984 54.4326C22.0984 54.8116 22.3959 55.1093 22.7746 55.1093H47.0657V60.0217C47.0657 61.7133 47.4715 63.3102 48.1748 64.7311H22.7746C22.3959 64.7311 22.0984 65.0288 22.0984 65.4078C22.0984 65.7867 22.3959 66.0844 22.7746 66.0844H48.9592C50.8933 68.8857 54.1123 70.7126 57.7506 70.7126H59.874V81.4847C59.874 81.7689 60.0498 82.0125 60.3068 82.1208C60.388 82.1479 60.4691 82.1614 60.5503 82.1614C60.7396 82.1614 60.9154 82.0802 61.0507 81.9313L64.4726 78.1151V87.3174V87.358ZM92.8753 92.8659V108.78H80.0264V100.715C80.0264 100.336 79.7289 100.038 79.3502 100.038C78.9715 100.038 78.6739 100.336 78.6739 100.715V108.78H65.8251V88.1564L92.8753 92.8659ZM111.04 60.0352C111.04 65.1912 106.847 69.3729 101.707 69.3729H70.8429C70.6535 69.3729 70.4642 69.4541 70.3425 69.6029L61.2265 79.739V70.0495C61.2265 69.6706 60.929 69.3729 60.5503 69.3729H57.7506C52.5975 69.3729 48.4182 65.1777 48.4182 60.0352V39.1811C48.4182 34.0251 52.611 29.8435 57.7506 29.8435H101.707C106.86 29.8435 111.04 34.0387 111.04 39.1811V60.0352ZM79.7289 43.9447C76.9833 43.9447 74.7381 46.1776 74.7381 48.9383C74.7381 51.699 76.9698 53.9319 79.7289 53.9319C82.488 53.9319 84.7197 51.699 84.7197 48.9383C84.7197 46.1776 82.488 43.9447 79.7289 43.9447ZM79.7289 52.5651C77.7272 52.5651 76.0906 50.9412 76.0906 48.9248C76.0906 46.9084 77.7136 45.2844 79.7289 45.2844C81.7441 45.2844 83.3671 46.9084 83.3671 48.9248C83.3671 50.9412 81.7441 52.5651 79.7289 52.5651ZM95.5668 43.9447C92.8212 43.9447 90.576 46.1776 90.576 48.9383C90.576 51.699 92.8077 53.9319 95.5668 53.9319C98.3259 53.9319 100.558 51.699 100.558 48.9383C100.558 46.1776 98.3259 43.9447 95.5668 43.9447ZM95.5668 52.5651C93.5651 52.5651 91.9285 50.9412 91.9285 48.9248C91.9285 46.9084 93.5651 45.2844 95.5668 45.2844C97.5685 45.2844 99.2051 46.9084 99.2051 48.9248C99.2051 50.9412 97.582 52.5651 95.5668 52.5651Z"
        fill="url(#paint0_linear_820_771)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_820_771"
          x1="24.1677"
          y1="116.792"
          x2="82.5244"
          y2="15.7711"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
