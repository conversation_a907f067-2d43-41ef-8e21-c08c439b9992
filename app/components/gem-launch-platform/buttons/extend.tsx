import { useTranslation } from 'react-i18next'

import type { GlpLock } from '~/api/types'
import { Namespace } from '~/i18n'
import { Button, ButtonVariant } from '~/tenset-components'
import { useWallet } from '~/tenset-web3'

interface ExtendLockButtonProps {
  lock?: GlpLock
  setLock: (lock?: GlpLock) => void
}

export function ExtendLockButton({ lock, setLock }: ExtendLockButtonProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM_SUBSCRIPTION])

  const { wallet } = useWallet()

  return (
    <Button
      variant={ButtonVariant.Secondary}
      disabled={wallet?.isProcessing}
      onClick={() => setLock(lock)}
    >
      {t('extend.button')}
    </Button>
  )
}
