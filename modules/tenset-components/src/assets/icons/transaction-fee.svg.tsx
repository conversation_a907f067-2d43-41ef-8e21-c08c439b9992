import { AssetProps } from './index'

export default function TransactionFee({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M120.744 63.96H94.744V34.112L87.776 41.704L80.808 34.112L73.84 41.704L66.872 34.112L59.904 41.704L52.936 34.112L45.968 41.704L39 34.112V112.424C39 117.208 42.848 121.16 47.736 121.16H67.6H88.192H103.584C108.368 121.16 112.32 117.312 112.32 112.424V108.472V99.32H129.688V72.696C129.48 67.912 125.632 63.96 120.744 63.96Z"
        fill={color ?? '#15121A'}
      />
      <path d="M24.024 68.64L27.664 62.4H20.384L24.024 68.64Z" fill="#15121A" />
      <path
        d="M10.92 44.512C10.608 44.512 10.296 44.824 10.296 45.136V46.28C10.296 46.592 10.608 46.904 10.92 46.904C11.232 46.904 11.544 46.592 11.544 46.28V45.136C11.544 44.824 11.232 44.512 10.92 44.512ZM65.936 72.384C66.04 72.488 66.248 72.592 66.352 72.592C66.56 72.592 66.664 72.488 66.768 72.384L82.16 56.992C82.368 56.784 82.368 56.368 82.16 56.056C81.952 55.848 81.536 55.848 81.224 56.056L65.936 71.448C65.728 71.656 65.728 72.072 65.936 72.384ZM107.016 22.048C107.328 22.048 107.64 21.736 107.64 21.424V19.448C107.64 19.136 107.328 18.824 107.016 18.824C106.704 18.824 106.392 19.136 106.392 19.448V21.424C106.392 21.84 106.704 22.048 107.016 22.048ZM24.752 58.968H17.576C17.368 58.968 17.16 59.072 17.056 59.28C16.952 59.488 16.952 59.696 17.056 59.904L20.696 66.144C20.8 66.352 21.008 66.456 21.216 66.456C21.424 66.456 21.632 66.352 21.736 66.144L25.376 59.904C25.48 59.696 25.48 59.488 25.376 59.28C25.272 59.072 25.064 58.968 24.752 58.968ZM21.216 64.584L18.72 60.216H23.712L21.216 64.584ZM109.512 17.576H111.488C111.8 17.576 112.112 17.264 112.112 16.952C112.112 16.64 111.8 16.328 111.488 16.328H109.512C109.2 16.328 108.888 16.64 108.888 16.952C108.888 17.368 109.2 17.576 109.512 17.576ZM107.016 15.08C107.328 15.08 107.64 14.768 107.64 14.456V12.48C107.64 12.168 107.328 11.856 107.016 11.856C106.704 11.856 106.392 12.168 106.392 12.48V14.456C106.392 14.768 106.704 15.08 107.016 15.08ZM10.92 42.952C11.024 42.952 11.128 42.952 11.232 42.952C17.056 42.952 21.84 39.208 21.84 34.528C21.84 29.64 16.536 26.832 11.544 26.208V16.64C11.544 16.328 11.232 16.016 10.92 16.016C10.608 16.016 10.296 16.328 10.296 16.64V26C5.72 25.272 2.912 22.88 2.912 19.864C2.912 16.432 6.656 13.52 11.232 13.52C14.56 13.52 17.472 14.976 18.824 17.264C19.032 17.576 19.344 17.68 19.656 17.472C19.968 17.264 20.072 16.952 19.864 16.64C18.408 14.04 15.08 12.376 11.544 12.272V8.94401C11.544 8.63201 11.232 8.32001 10.92 8.32001C10.608 8.32001 10.296 8.52801 10.296 8.94401V12.272C5.408 12.688 1.664 15.912 1.664 19.864C1.664 23.712 4.992 26.52 10.296 27.352V41.6C7.072 41.392 4.264 39.936 2.808 37.648C2.6 37.336 2.184 37.232 1.976 37.44C1.664 37.648 1.56 38.064 1.768 38.272C3.432 41.08 6.968 42.848 10.92 42.952ZM11.544 27.456C16.12 28.08 20.592 30.68 20.592 34.528C20.592 38.376 16.536 41.496 11.544 41.6V27.456ZM68.432 62.192C69.368 62.192 70.304 61.88 70.928 61.152C72.28 59.8 72.28 57.512 70.928 56.056C69.576 54.704 67.288 54.704 65.832 56.056C64.48 57.408 64.48 59.696 65.832 61.152C66.664 61.776 67.6 62.192 68.432 62.192ZM66.872 56.992C67.288 56.576 67.912 56.368 68.536 56.368C69.16 56.368 69.68 56.576 70.2 56.992C70.616 57.408 70.824 58.032 70.824 58.656C70.824 59.28 70.616 59.8 70.2 60.32C69.368 61.152 67.808 61.152 66.976 60.32C66.56 59.904 66.352 59.28 66.352 58.656C66.352 58.032 66.456 57.408 66.872 56.992ZM77.168 67.288C75.816 68.64 75.816 70.928 77.168 72.384C77.896 73.112 78.832 73.424 79.664 73.424C80.6 73.424 81.536 73.112 82.16 72.384C83.512 71.032 83.512 68.744 82.16 67.288C80.912 65.936 78.624 65.936 77.168 67.288ZM81.328 71.448C80.496 72.28 78.936 72.28 78.104 71.448C77.688 71.032 77.48 70.408 77.48 69.784C77.48 69.16 77.688 68.64 78.104 68.12C78.52 67.6 79.144 67.496 79.768 67.496C80.392 67.496 80.912 67.704 81.432 68.12C81.848 68.536 82.056 69.16 82.056 69.784C82.056 70.408 81.848 71.032 81.328 71.448ZM56.16 83.408C56.16 83.72 56.472 84.032 56.784 84.032C57.096 84.032 57.408 83.72 57.408 83.408V74.256H60.424C60.736 74.256 61.048 73.944 61.048 73.632C61.048 73.32 60.736 73.008 60.424 73.008H57.408V68.64C57.408 68.328 57.096 68.016 56.784 68.016C56.472 68.016 56.16 68.224 56.16 68.64V73.008H46.072L50.648 56.784C50.752 56.472 50.544 56.056 50.232 55.952C49.92 55.848 49.504 56.056 49.4 56.368L44.616 73.32C44.512 73.528 44.616 73.736 44.72 73.84C44.824 74.048 45.032 74.048 45.24 74.048H56.16V83.408ZM102.544 17.576H104.52C104.832 17.576 105.144 17.264 105.144 16.952C105.144 16.64 104.832 16.328 104.52 16.328H102.544C102.232 16.328 101.92 16.64 101.92 16.952C101.92 17.368 102.232 17.576 102.544 17.576ZM117.936 60.632H92.56V31.304C92.56 30.992 92.352 30.784 92.144 30.68C91.936 30.576 91.624 30.68 91.416 30.888L84.864 38.064L78.312 30.888C78.104 30.576 77.584 30.576 77.376 30.888L70.824 38.064L64.272 30.992C64.064 30.68 63.544 30.68 63.336 30.992L56.784 38.064L50.232 30.992C50.024 30.68 49.504 30.68 49.296 30.992L42.744 38.064L36.192 30.992C35.984 30.784 35.776 30.68 35.464 30.784C35.256 30.888 35.048 31.096 35.048 31.408V109.72C35.048 114.816 39.208 119.08 44.408 119.08H100.256C105.352 119.08 109.616 114.92 109.616 109.72V97.24H126.36C126.672 97.24 126.984 96.928 126.984 96.616V69.888C127.296 64.792 123.136 60.632 117.936 60.632ZM44.72 117.728C40.248 117.728 36.712 114.088 36.712 109.72V32.968L42.536 39.416C42.744 39.728 43.264 39.728 43.472 39.416L50.024 32.344L56.576 39.416C56.784 39.728 57.304 39.728 57.512 39.416L64.064 32.344L70.616 39.416C70.72 39.52 70.928 39.624 71.136 39.624C71.344 39.624 71.448 39.52 71.656 39.416L78.208 32.344L84.76 39.52C84.968 39.832 85.488 39.832 85.696 39.52L91.624 33.072V109.72C91.624 113.152 93.496 116.168 96.304 117.728H44.72ZM108.68 69.888V109.616C108.68 114.088 105.04 117.624 100.672 117.624C96.2 117.624 92.664 113.984 92.664 109.616V61.88H113.464C110.552 63.544 108.68 66.456 108.68 69.888ZM126.048 95.888H109.928V69.888C109.928 65.416 113.568 61.88 117.936 61.88C122.408 61.88 125.944 65.52 125.944 69.888V95.888H126.048ZM79.04 95.264H47.008C46.696 95.264 46.384 95.576 46.384 95.888C46.384 96.2 46.696 96.512 47.008 96.512H79.04C79.352 96.512 79.664 96.2 79.664 95.888C79.664 95.576 79.456 95.264 79.04 95.264ZM70.512 100.984H47.008C46.696 100.984 46.384 101.296 46.384 101.608C46.384 101.92 46.696 102.232 47.008 102.232H70.408C70.72 102.232 71.032 101.92 71.032 101.608C71.032 101.296 70.824 100.984 70.512 100.984Z"
        fill={color ?? 'url(#paint0_linear_1_2)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1_2"
          x1="32.2377"
          y1="113.224"
          x2="94.8561"
          y2="4.7656"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.5309" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.7733" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
