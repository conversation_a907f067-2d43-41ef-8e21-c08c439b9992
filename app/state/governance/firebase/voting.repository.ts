import { Timestamp, orderBy, where } from 'firebase/firestore'

import type { Query } from '../query'
import type { VoteDTO } from '../vote.dto'
import type { VotingDTO } from '../voting.dto'

import { FirstoreRepository } from './firestore.repository'

import { votingsRaw } from '~/data'
import { getEnvironment } from '~/environment'

export class VotingRepository extends FirstoreRepository<VotingDTO> {
  async get(query: Query) {
    const endsAtTimestamp = Timestamp.fromDate(
      new Date(
        Math.max.apply(
          null,
          votingsRaw.map(
            voting =>
              voting.endsAt.seconds * 1000 +
              voting.endsAt.nanoseconds / 1_000_000
          )
        )
      )
    )

    const entities = await this.client
      .firestore()
      .collection<VotingDTO>('votings')
      .query(
        ...this.queryToConstraints(query),
        where('visible', '==', true),
        where(`environment.${getEnvironment().NODE_ENV}`, '==', true),
        where('endsAt', '>', endsAtTimestamp),
        orderBy('endsAt', 'desc')
      )

    const subCollectionResolvers = entities
      .filter(entity => {
        // Firebase has problems with filtering and sorting by two dates.
        const { startsAt } = entity

        if (!startsAt) return true

        return Date.now() >= startsAt.seconds * 1000
      })
      .map(entity =>
        this.client
          .firestore()
          .collection<VoteDTO>(`votings/${entity.id}/votes`)
          .query()
          .then(votes => ({
            ...entity,
            votes: votes.length > 0 ? votes : entity.votes,
          }))
      )

    return Promise.all(subCollectionResolvers)
  }
}
