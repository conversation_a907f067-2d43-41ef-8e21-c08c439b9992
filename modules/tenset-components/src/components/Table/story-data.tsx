import { Graph, TableItem } from '..'
import { Icon, IconName } from '../Icon'
import { TextS } from '../Typo/Text'
import { graphTokenPriceArguments } from '../Graph'
import { graphTokenPriceData } from '../Graph/story-data'

const headers = {
  name: 'Name',
  currentValue: 'Current value',
  totalInvestment: 'Total investment',
  profit: 'Profit',
  priceGraph: 'Price graph (24h)',
}

type StoryTableItem = TableItem<keyof typeof headers>

function generateItem(): StoryTableItem {
  return {
    name: (
      <div className="flex flex-wrap items-center gap-3">
        <Icon name={IconName.PancakeSwap} />
        <TextS isBold>PancakeSwap</TextS>
        <TextS className="text-neutral-100">&#183; CAKE</TextS>
      </div>
    ),
    currentValue: '3,661,470 USDT',
    totalInvestment: '3,847,500 USDT',
    profit: '-186,030 USDT',
    priceGraph: () => (
      <div className="h-[100px] w-[300px]">
        <Graph
          data={graphTokenPriceData}
          {...graphTokenPriceArguments(graphTokenPriceData)}
        />
      </div>
    ),
  }
}

const items: StoryTableItem[] = Array.from({ length: 5 }, generateItem)

export { headers, items }
