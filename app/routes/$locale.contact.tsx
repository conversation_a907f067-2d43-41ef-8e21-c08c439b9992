import type {
  ActionFunction,
  LoaderFunctionArgs,
  MetaFunction,
} from '@remix-run/node'
import { json } from '@remix-run/node'

import { detectLocale } from '~/i18n/detect-locale'
import { i18next, Namespace } from '~/i18n'
import { CallToActionContactSection } from '~/sections/contact'
import {
  formDataToObject,
  isFormOk,
  schema,
  translateValidations,
  validate,
  validateExistanceAndNoEmptiness,
  validateIfEmailAlike,
  withCache,
} from '~/utils'
import { sendMail } from '~/tenset-components/utils'
import { fetchRecaptcha } from '~/api/fetchers'

const contactFormSchema = schema({
  name: [validateExistanceAndNoEmptiness],
  lastName: [validateExistanceAndNoEmptiness],
  workEmail: [validateExistanceAndNoEmptiness, validateIfEmailAlike],
  message: [validateExistanceAndNoEmptiness],
  recaptcha: [validateExistanceAndNoEmptiness],
})

export const action: ActionFunction = async ({ request }) => {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.FORM_VALIDATORS)

  const data = formDataToObject(
    await request.formData(),
    Object.keys(contactFormSchema)
  )

  const validations = validate(data, contactFormSchema)
  const formOk = isFormOk(validations)

  const resultCaptchaCheck = await fetchRecaptcha(data.recaptcha)

  if (!formOk || !resultCaptchaCheck) {
    return json({
      validations: translateValidations(t, validations),
      ok: false,
    })
  }

  const result = await sendMail({
    data: {
      topic: `Contact Page -- ${data.name} ${data.lastName}`,
      email: data.workEmail,
      message: data.message,
    },
  })

  return json({
    ok: result,
  })
}

export type ContactAction = typeof action

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = detectLocale(request)
  const t = await i18next.getFixedT(locale, Namespace.CONTACT_PAGE)

  const title = t('meta.title')
  const description = t('meta.description')

  return withCache(
    json({
      title,
      description,
    })
  )
}

export const handle = {
  i18n: [Namespace.CONTACT_PAGE, Namespace.COMMON],
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data!.title} | Tenset` },
    {
      name: 'description',
      content: data!.description,
    },
  ]
}

export default function ContactPage() {
  return (
    <div className="container gap-16 min-h-[50vh]">
      <CallToActionContactSection />
    </div>
  )
}
