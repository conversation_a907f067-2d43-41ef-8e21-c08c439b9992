import { Meta } from '@storybook/react'

import { ScrollArea, ScrollBar } from './index'

type ScrollAreaType = typeof ScrollArea

export default {
  title: 'Components/ScrollArea',
  component: ScrollArea,
  args: {
    className: 'h-[200px] w-[350px] rounded-md border p-4',
    children:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ullamcorper, risus eu sagittis rutrum, mi purus tristique nulla, ut laoreet sem velit eu nisi. Mauris sodales semper ligula at venenatis. Duis vel urna vel erat tempor tempus. Morbi lacinia ligula in erat pretium elementum. Phasellus maximus vitae lorem a ultricies.',
  },
} as Meta<ScrollAreaType>

export const Preview = {}

export const Horizontal = {
  args: {
    className: 'w-96 rounded-md border p-4',
    children: (
      <>
        <div className="w-[150%]">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur
          ullamcorper, risus eu sagittis rutrum, mi purus tristique nulla, ut
          laoreet sem velit eu nisi. Mauris sodales semper ligula at venenatis.
          Duis vel urna vel erat tempor tempus. Morbi lacinia ligula in erat
          pretium elementum. Phasellus maximus vitae lorem a ultricies.
        </div>

        <ScrollBar orientation="horizontal" />
      </>
    ),
  },
}
