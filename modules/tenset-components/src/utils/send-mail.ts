type MailData = {
  email: string
  message: string
  topic: string
}

export async function sendMail({
  data,
  url = null,
}: {
  url?: string | null
  data: MailData
}) {
  const mailProviderUrl =
    url ||
    'https://asia-northeast1-clear-tooling-375108.cloudfunctions.net/gmail-sender'

  const r = await fetch(mailProviderUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const text = await r.text()

  return text === 'success'
}
