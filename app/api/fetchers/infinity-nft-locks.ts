import { infinityNftLocksAdapter } from '~/api/adapters'
import type { InfinityNftLockResponse, InfinityNftLocksData } from '~/api/types'
import { getServerEnvironment } from '~/environment/environment.server'

export async function fetchInfinityNftLocks(
  address: string
): Promise<InfinityNftLocksData> {
  const NFT_LOCKS_URL = `${getServerEnvironment().DATA2_TENSET_URL}nft-locks/${address}`

  return await fetch(NFT_LOCKS_URL)
    .then<InfinityNftLockResponse[]>(response => response.json())
    .then(infinityNftLocksAdapter)
    .catch(() => ({
      lockedNftLocks: [],
      withdrawnNftLocks: [],
    }))
}
