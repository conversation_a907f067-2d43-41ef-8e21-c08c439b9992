import type { TypedResponse } from '@remix-run/node'

import { getServerEnvironment } from '~/environment/environment.server'

export function withCache<T extends TypedResponse<unknown>>(
  x: T,
  timeInSeconds = 86_400
) {
  if (getServerEnvironment().NODE_ENV === 'development') {
    return x
  }

  x.headers.set('Cache-Control', `s-maxage=${timeInSeconds}, max-age=30`)

  return x
}

export function withoutCache<T extends TypedResponse<unknown>>(x: T) {
  x.headers.set('Cache-Control', 'no-cache')

  return x
}

export function potentiallyCacheSSRResponse(
  _request: Request,
  headers: Headers
) {
  if (getServerEnvironment().NODE_ENV === 'development') {
    return
  }

  headers.set('Cache-Control', 's-maxage=60, stale-while-revalidate')
}
