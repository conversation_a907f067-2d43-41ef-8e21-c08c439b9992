interface CalcTimeLeftProps {
  value: number
  unit: string
}

// I think we need to get current date from our server
export default function calcTimeLeft(
  date: Date,
  currentDate = new Date()
): CalcTimeLeftProps {
  const msBetweenDates = Math.abs(date.getTime() - currentDate.getTime())
  const hoursBetweenDates = msBetweenDates / (60 * 60 * 1000)

  const endsTime = {
    value: Math.floor(hoursBetweenDates / 24),
    unit: 'days',
  }

  if (hoursBetweenDates < 48) {
    endsTime.unit = 'day'
  }

  if (hoursBetweenDates < 24) {
    endsTime.value = Math.floor(hoursBetweenDates)
    endsTime.unit = 'hours'
  }

  if (hoursBetweenDates < 2) {
    endsTime.value = Math.floor(hoursBetweenDates)
    endsTime.unit = 'hour'
  }

  if (hoursBetweenDates < 1) {
    endsTime.value = Math.floor(hoursBetweenDates * 60)
    endsTime.unit = 'minutes'
  }

  return endsTime
}
