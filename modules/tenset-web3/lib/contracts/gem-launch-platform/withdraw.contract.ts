import { BigNumber, ContractInterface } from 'ethers'

import { glpWithdrawAbi } from '../../assets'
import { Utils } from '../../types'
import { Contract } from '../index'

export class GlpWithdrawContract extends Contract {
  constructor(
    address: string,
    chain: Utils.Network,
    abi: ContractInterface = glpWithdrawAbi,
    rpcUrl?: string
  ) {
    super(address, chain, abi, rpcUrl)
  }

  async withdraw(tokenId: BigNumber) {
    this.wallet!.asBusy()

    try {
      const tx = await this.adapter!.withdraw(tokenId)

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
