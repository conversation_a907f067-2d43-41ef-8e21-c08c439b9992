import { useTranslation } from 'react-i18next'

import { CardsWithTitle } from '~/components/cards-with-title'
import { accessLaunchpad } from '~/data/gem-launch-platform'
import { Namespace } from '~/i18n'
import useLocalizePathname from '~/i18n/use-localize-pathname'
import { useWallet } from '~/tenset-web3'

export function GlpAccessTheLaunchpad() {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])
  const { wallet } = useWallet()

  const localizePathname = useLocalizePathname()

  return (
    <CardsWithTitle
      title={t('access-launchpad.title')}
      cards={accessLaunchpad(Boolean(wallet?.isConnected), t, localizePathname)}
    />
  )
}
