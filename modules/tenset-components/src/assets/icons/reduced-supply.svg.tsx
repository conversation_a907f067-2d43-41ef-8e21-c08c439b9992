import { AssetProps } from './index'

export default function ReducedSupply({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '109'}
      height={size ?? '111'}
      viewBox="0 0 109 111"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M108.359 84.698C108.261 84.4778 108.04 84.3432 107.808 84.3432L95.8902 84.49V61.8533C95.8902 61.5107 95.6208 61.2415 95.2778 61.2415H77.9223C77.5793 61.2415 77.3099 61.5107 77.3099 61.8533V84.7102L65.3802 84.8571C65.1475 84.8571 64.9271 85.0039 64.8291 85.2119C64.7311 85.4322 64.7678 85.6769 64.9148 85.8604L85.908 110.455C86.0183 110.589 86.1897 110.675 86.3735 110.675C86.5572 110.675 86.7287 110.602 86.8389 110.455L108.273 85.3343C108.432 85.1507 108.469 84.8938 108.359 84.6735V84.698ZM86.3612 109.133L66.6785 86.0807L77.91 85.9461C78.2407 85.9461 78.5102 85.6646 78.5102 85.3343V62.4896H94.6409V85.1263C94.6409 85.2853 94.7021 85.4444 94.8246 85.5668C94.9471 85.6769 95.0696 85.7748 95.2656 85.7381L106.46 85.6035L86.3612 109.146V109.133ZM27.7664 30.4678C27.7664 30.4678 27.8644 30.4678 27.9134 30.4555C28.9177 30.2108 29.9221 29.9049 30.8774 29.5501C31.1959 29.44 31.3551 29.0851 31.2449 28.767C31.1224 28.4488 30.7794 28.2898 30.461 28.3999C29.5424 28.7425 28.587 29.0239 27.6317 29.2564C27.301 29.3421 27.105 29.6602 27.1785 29.9906C27.252 30.272 27.4969 30.4555 27.7786 30.4555L27.7664 30.4678ZM57.8109 14.2672C57.8109 14.2672 57.9089 14.2672 57.9579 14.255C58.9623 14.0103 59.9666 13.7166 60.922 13.3495C61.2404 13.2272 61.3996 12.8846 61.2894 12.5664C61.1669 12.2483 60.824 12.0892 60.5055 12.2116C59.5869 12.5542 58.6316 12.8356 57.6762 13.0681C57.3455 13.1415 57.1495 13.4719 57.223 13.8023C57.2965 14.0837 57.5415 14.2672 57.8232 14.2672H57.8109ZM66.9235 65.7443V54.1323V30.9083V19.2963V7.64754C66.9235 3.35268 58.8275 0 48.4901 0C38.1528 0 30.0568 3.36492 30.0568 7.64754V17.8646C26.909 16.8123 22.8917 16.1883 18.4456 16.1883C8.09598 16.2005 0 19.5532 0 23.8481V81.9449C0 86.2397 8.09598 89.5924 18.4334 89.5924C28.7707 89.5924 36.8667 86.2275 36.8667 81.9449V71.6911C40.4309 72.8168 44.1176 73.4041 47.8532 73.4041C48.0615 73.4041 48.2697 73.4041 48.4779 73.4041C58.8153 73.4041 66.9113 70.0392 66.9113 65.7566L66.9235 65.7443ZM1.22481 26.6257C3.8459 29.5011 10.4721 31.5079 18.4334 31.5079C19.0335 31.5079 19.6337 31.4956 20.2216 31.4711C20.5645 31.4589 20.8217 31.1775 20.8095 30.8349C20.7972 30.4923 20.5155 30.2353 20.1726 30.2475C19.5969 30.272 19.0213 30.2842 18.4334 30.2842C8.13273 30.2842 1.22481 26.956 1.22481 23.8603C1.22481 20.7646 8.14497 17.4364 18.4334 17.4364C28.7217 17.4364 35.6419 20.7646 35.6419 23.8603V24.3742C35.6419 24.3742 35.6419 24.4476 35.6419 24.4844V35.4723C35.6419 38.5803 28.7217 41.8963 18.4334 41.8963C8.14497 41.8963 1.22481 38.5681 1.22481 35.4723V26.6257ZM1.22481 38.2377C3.8459 41.1132 10.4721 43.1199 18.4334 43.1199C26.3946 43.1199 33.0208 41.1254 35.6419 38.2377V47.0844C35.6419 50.1923 28.7217 53.5083 18.4334 53.5083C8.14497 53.5083 1.22481 50.1801 1.22481 47.0844V38.2377ZM1.22481 49.8619C3.8459 52.7374 10.4721 54.7441 18.4334 54.7441C26.3946 54.7441 33.0208 52.7496 35.6419 49.8619V58.7086C35.6419 61.8166 28.7217 65.1325 18.4334 65.1325C8.14497 65.1325 1.22481 61.8043 1.22481 58.7086V49.8619ZM1.22481 61.474C3.8459 64.3494 10.4721 66.3561 18.4334 66.3561C26.3946 66.3561 33.0208 64.3617 35.6419 61.474V70.3206C35.6419 73.4286 28.7217 76.7446 18.4334 76.7446C8.14497 76.7446 1.22481 73.4164 1.22481 70.3206V61.474ZM35.6419 81.9449C35.6419 85.0528 28.7217 88.3688 18.4334 88.3688C8.14497 88.3688 1.22481 85.0406 1.22481 81.9449V73.0982C3.8459 75.9737 10.4721 77.9804 18.4334 77.9804C26.3946 77.9804 33.0208 75.9859 35.6419 73.0982V81.9449ZM31.2693 18.3174V10.4251C33.8904 13.3006 40.5167 15.3073 48.4779 15.3073C49.0781 15.3073 49.6782 15.3073 50.2661 15.2706C50.6091 15.2584 50.8663 14.9769 50.854 14.6343C50.8418 14.2917 50.5601 14.047 50.2171 14.047C49.6415 14.0715 49.0658 14.0837 48.4779 14.0837C38.1773 14.0837 31.2693 10.7555 31.2693 7.65977C31.2693 4.56405 38.1895 1.23584 48.4779 1.23584C58.7663 1.23584 65.6865 4.56405 65.6865 7.65977V19.284C65.6865 22.392 58.7663 25.708 48.4657 25.708C44.4973 25.7814 40.6146 25.1696 36.8667 23.946V23.8603C36.8667 21.6578 34.7356 19.7123 31.2693 18.3296V18.3174ZM48.4779 48.9442C44.534 49.0177 40.6269 48.4058 36.879 47.1822V36.8428C40.4432 37.9685 44.1421 38.5558 47.8777 38.5558C48.086 38.5558 48.2942 38.5558 48.4901 38.5558C56.4514 38.5558 63.0776 36.5613 65.6987 33.6736V42.5203C65.6987 45.6283 58.7785 48.9442 48.4779 48.9442ZM65.6987 45.2857V54.1323C65.6987 57.2403 58.7785 60.5563 48.4779 60.5563C44.534 60.6297 40.6269 60.0179 36.879 58.7943V48.4548C40.4432 49.5805 44.1298 50.1678 47.8655 50.1678C48.0737 50.1678 48.2819 50.1678 48.4901 50.1678C56.4514 50.1678 63.0776 48.1734 65.6987 45.2857ZM48.4779 37.32C44.5218 37.3812 40.6269 36.7816 36.879 35.558V25.2185C40.4432 26.3442 44.1421 26.9316 47.8777 26.9316C48.086 26.9316 48.2942 26.9316 48.4901 26.9316C56.4514 26.9316 63.0776 24.9371 65.6987 22.0494V30.896C65.6987 34.004 58.7785 37.32 48.4779 37.32ZM48.4779 72.1683C44.534 72.2295 40.6269 71.6299 36.879 70.4063V60.0668C40.4432 61.1925 44.1298 61.7799 47.8655 61.7799C48.0737 61.7799 48.2819 61.7799 48.4901 61.7799C56.4514 61.7799 63.0776 59.7854 65.6987 56.8977V65.7443C65.6987 68.8523 58.7785 72.1683 48.4779 72.1683Z"
        fill="url(#paint0_linear_2146_34)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2146_34"
          x1="27.4602"
          y1="100.299"
          x2="78.4962"
          y2="11.8133"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
