import { Utils } from '../../types'

import { useInfinityNftWithdrawalFeeContract } from './use-nft-withdrawal-fee-contract'

import {
  useInfinityNftVaultContract,
  useInfinityVaultBridgeContract,
  useInfinityVaultContract,
  useInfinityWithdrawalFeeContract,
} from '.'

export const useInfinityContracts = (withdrawalFeeNetwork?: Utils.Network) => {
  const { infinityVaultContract } = useInfinityVaultContract()
  const { infinityVaultBridgeContract } = useInfinityVaultBridgeContract()

  const { infinityNftVaultContract } = useInfinityNftVaultContract()

  const { infinityWithdrawalFeeContract } = useInfinityWithdrawalFeeContract(
    withdrawalFeeNetwork ?? Utils.Network.SMART_CHAIN
  )

  const { infinityNftWithdrawalFeeContract } =
    useInfinityNftWithdrawalFeeContract(withdrawalFeeNetwork!)

  return {
    infinityVaultContract,
    infinityNftVaultContract,
    infinityVaultBridgeContract,
    infinityWithdrawalFeeContract,
    infinityNftWithdrawalFeeContract,
  }
}
