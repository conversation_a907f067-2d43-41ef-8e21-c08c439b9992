/* eslint unicorn/prefer-module: 0 */
/* eslint @typescript-eslint/no-var-requires: 0 */
const { resolve } = require('node:path')

/** @type {import('tailwindcss').Config} */
const config = {
  content: ['./src/components/**/*.{js,jsx,ts,tsx,mdx}'],
  theme: {
    extend: {
      fontFamily: {
        sora: ['Sora', 'sans-serif'],
        'roboto-mono': ['Roboto Mono', 'sans-serif'],
      },
      fontSize: {
        sm: ['14px', '24px'],
        base: ['16px', '24px'],
        lg: ['20px', '32px'],
        xl: ['24px', '32px'],
        '2xl': ['36px', '44px'],
        '3xl': ['48px', '56px'],
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
    colors: {
      white: '#FAFAFA',
      green: {
        900: '#004214',
        700: '#00A66A',
        600: '#188F3C',
        500: '#25A84C',
        100: '#67DB8A',
      },
      neutral: {
        900: '#07060A',
        800: '#15121A',
        700: '#26232B',
        600: '#3A3740',
        500: '#4E4A54',
        400: '#636069',
        300: '#7A787D',
        200: '#908E91',
        100: '#A3A3A3',
      },
      blue: {
        900: '#003DB2',
        800: '#0848C4',
        700: '#0D52D9',
        600: '#135CEB',
        500: '#1A68FF',
        400: '#2B73FF',
        300: '#4081FF',
        200: '#5992FF',
        100: '#6E9FFF',
        0: '#95B9FF',
      },
      pink: {
        500: '#E545BC',
      },
      red: {
        100: '#FF8585',
        500: '#D42A2A',
      },
      violet: {
        900: '#370099',
        500: '#682AD5',
        0: '#C29FFF',
      },
    },
    container: {
      center: true,
      padding: {
        DEFAULT: '16px',
        md: '72px',
      },
      screens: {
        DEFAULT: '1440px',
      },
    },
  },
  plugins: [],
}

module.exports = {
  resolveConfigRelative(path) {
    config.content = config.content.map(item => resolve(path, item))

    return config
  },
}
