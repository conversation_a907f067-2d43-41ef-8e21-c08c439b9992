import { AssetProps } from './index'

export default function Tglp({ size, color, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M99.0902 70.4561C111.08 41.4366 108.214 26.5145 96.132 7.54773C84.0896 16.1606 76.4191 24.6427 72.6494 38.3736V38.282C72.4268 38.2689 72.2043 38.2558 71.9687 38.2558C70.9346 38.2558 69.9398 38.4522 69.0235 38.7794C69.0235 38.6092 69.0497 38.4259 69.0497 38.2558C69.0497 31.8943 63.8924 26.7501 57.544 26.7501C57.544 26.7501 57.5309 26.7501 57.5178 26.7501C55.2272 22.784 50.9469 20.1006 46.0383 20.1006C39.271 20.1006 33.708 25.1662 32.8833 31.711C32.6084 31.6979 32.3467 31.6717 32.0718 31.6717C25.5008 31.6717 20.1734 36.9992 20.1734 43.5702C20.1734 50.1411 25.5008 55.4686 32.0718 55.4686H70.0707V55.4293C69.9136 58.3614 69.8482 61.4766 69.8744 64.8014C63.2249 68.6759 54.2847 78.925 52.6616 85.2734C50.6197 92.3156 62.9631 82.1319 72.5577 84.619C73.2907 85.6268 74.1808 86.5039 75.1625 87.1583C71.9556 89.9071 69.3115 96.3079 68.1596 99.4101C67.8455 100.248 68.4476 101.138 69.3377 101.151C69.9136 101.151 70.6205 101.177 71.4058 101.23L71.3535 101.413C68.5654 103.847 67.0732 109.515 74.351 119.843C84.9535 112.971 85.6865 107.159 84.0111 103.861L85.9745 104.384C86.8122 104.711 87.7023 104.096 87.7285 103.206C87.8332 99.8945 87.7678 92.9701 85.8174 89.2265C86.9693 88.9778 88.1212 88.5196 89.1814 87.8521C99.0117 89.1218 106.656 103.18 107.389 95.889C108.266 89.3966 103.789 76.5558 99.0771 70.4561H99.0902Z"
        fill={color ?? '#15121A'}
      />
      <path
        d="M87.2442 37.2479C87.7808 37.3527 88.3175 37.405 88.8542 37.405C90.5035 37.405 92.1135 36.9207 93.501 35.9783C95.3466 34.7348 96.5901 32.8368 97.022 30.6508C97.899 26.1349 94.9408 21.7499 90.4249 20.8729C85.909 19.9959 81.524 22.9542 80.647 27.4701C79.77 31.9859 82.7283 36.3709 87.2442 37.2479ZM81.9298 27.7188C82.5843 24.3678 85.5294 22.0248 88.828 22.0248C89.273 22.0248 89.7181 22.0641 90.1631 22.1557C93.9722 22.8887 96.4592 26.5931 95.7262 30.389C95.3728 32.2347 94.3125 33.8185 92.7549 34.8787C91.1972 35.939 89.3254 36.3186 87.4798 35.9521C83.6707 35.2191 81.1837 31.5147 81.9167 27.7188H81.9298ZM97.2053 67.7989C99.0247 63.3616 100.491 59.2646 101.63 55.4293C101.63 55.4032 102.454 52.5235 102.454 52.5235L102.664 51.6988C102.664 51.6988 104.732 41.3973 104.784 36.8291C104.876 29.3811 103.384 22.6008 99.8625 15.0481C98.331 11.7626 96.4068 8.33314 94.0507 4.64189C93.9591 4.48482 93.802 4.3801 93.6188 4.35392C93.4355 4.32774 93.2653 4.35394 93.1083 4.45866C81.2753 12.9276 73.4216 21.1609 69.5078 35.0751C68.6963 35.062 67.8716 35.1275 67.0601 35.3369C66.8638 28.923 61.6934 23.7395 55.2533 23.5432C52.727 19.4331 48.2112 16.8937 43.3942 16.8937C36.6007 16.8937 30.8151 21.8285 29.6894 28.4649C29.6109 28.4649 29.5193 28.4649 29.4407 28.4649C22.5164 28.4649 16.8879 34.0934 16.8879 41.0177C16.8879 47.9421 22.5164 53.5706 29.4407 53.5706H66.759C66.6281 56.1885 66.5627 58.9373 66.5889 61.8694C60.1488 65.7962 51.0778 75.9275 49.4023 82.5378C49.1536 83.4148 48.9049 84.6714 49.6641 85.4044C50.0306 85.7447 50.5149 85.9018 51.117 85.9018C52.3082 85.9018 53.9836 85.3389 56.1303 84.619C59.9263 83.3362 65.0836 81.6215 69.5209 82.6687C70.0969 83.4279 70.7513 84.1085 71.4713 84.6844C68.4868 87.6819 66.0915 93.3759 64.9003 96.6352C64.6778 97.2373 64.7694 97.8918 65.1228 98.4154C65.4763 98.939 66.0522 99.24 66.6936 99.2531C66.9292 99.2531 67.1779 99.2531 67.4397 99.2662C65.5417 101.662 63.7615 107.094 71.1964 117.657C71.3273 117.84 71.5236 117.932 71.733 117.932C71.8509 117.932 71.9818 117.893 72.0865 117.827C82.9246 110.811 83.3042 105.104 82.4403 102.172C82.6759 102.264 82.9115 102.342 83.134 102.434C83.3697 102.525 83.6053 102.565 83.8409 102.565C84.2074 102.565 84.587 102.46 84.9011 102.238C85.4247 101.884 85.752 101.295 85.7781 100.667C85.8829 97.185 85.7781 91.0198 84.1158 87.1191C85.0059 86.8573 85.8698 86.4777 86.6944 85.9803C91.2103 86.6479 95.3597 90.1951 98.4095 92.8C100.648 94.711 102.232 96.0723 103.501 96.0723C103.711 96.0723 103.92 96.0331 104.117 95.9545C105.111 95.5619 105.347 94.3053 105.439 93.4152C106.355 86.6348 101.721 73.8463 97.2184 67.7989H97.2053ZM100.543 54.4738L84.5085 51.3716L68.4738 48.2694C68.5392 47.5625 68.6177 46.8688 68.6963 46.1881L84.9011 49.3296L101.106 52.4711C100.923 53.1387 100.739 53.8062 100.543 54.4869V54.4738ZM93.3177 5.91158C94.3649 7.57395 95.3335 9.21016 96.2498 10.8333C95.111 9.93008 93.9329 9.35415 92.7025 9.11853C91.4721 8.88292 90.1631 8.97454 88.7756 9.3934C90.2155 8.22843 91.7339 7.07656 93.3308 5.92468L93.3177 5.91158ZM84.6001 12.9799C87.663 10.8071 90.2286 9.95627 92.4407 10.3882C94.6659 10.8202 96.721 12.5611 98.7499 15.7287C98.7499 15.7287 98.763 15.7419 98.776 15.7549C103.658 26.2397 105.085 36.6851 101.433 51.1752L85.1498 48.0206L68.8665 44.8661C70.8953 30.0487 76.1311 20.886 84.587 12.9799H84.6001ZM29.4538 52.2485C23.2494 52.2485 18.2099 47.2091 18.2099 41.0046C18.2099 34.8002 23.2494 29.7607 29.4538 29.7607C29.6633 29.7607 29.8727 29.7738 30.0691 29.7869H30.213C30.5534 29.8262 30.8675 29.5644 30.9068 29.2241C31.6922 22.928 37.0719 18.1896 43.4073 18.1896C47.8839 18.1896 52.0726 20.6111 54.324 24.5118C54.4418 24.7081 54.6774 24.839 54.913 24.839C60.8949 24.839 65.7642 29.7084 65.7642 35.6903C65.7642 35.7819 65.7642 35.8866 65.7511 35.9783V36.1877C65.725 36.4102 65.8297 36.6197 65.9998 36.7375C66.17 36.8684 66.4056 36.8945 66.6151 36.8291C67.4397 36.528 68.3036 36.3709 69.1675 36.3448C68.4869 38.9758 67.5444 44.9053 67.5444 44.9053L67.4397 45.73C67.4397 45.73 66.9815 49.971 66.8507 52.2617H29.4669L29.4538 52.2485ZM55.7376 83.3624C53.5517 84.0954 51.0778 84.9331 50.5934 84.4488C50.4495 84.3179 50.3971 83.8336 50.6851 82.865C52.1249 77.2103 59.9525 67.7335 66.6281 63.3878C66.6936 67.4586 66.8768 71.8174 67.2172 76.5427C67.335 78.1658 67.8324 79.7366 68.5916 81.1503C64.128 80.5482 59.3504 82.1451 55.7507 83.3624H55.7376ZM71.9163 116.374C64.4029 105.431 67.479 100.824 69.1675 99.3447C69.8613 99.384 70.6073 99.4495 71.4058 99.528C70.555 100.444 69.6911 101.714 69.678 103.311C69.678 105.143 70.7382 107.054 72.9635 109.149C73.0813 109.267 73.2515 109.332 73.4085 109.332C73.4871 109.332 73.5656 109.319 73.6441 109.293C76.4846 108.18 78.1993 106.819 78.8669 105.104C79.4559 103.612 79.1156 102.107 78.6836 100.942C79.4428 101.151 80.1627 101.387 80.8303 101.596C81.8513 103.612 82.977 109.018 71.9163 116.374ZM73.042 99.7374C73.7096 99.829 74.3902 99.9468 75.0709 100.078C75.7515 100.209 76.4322 100.366 77.0736 100.523C77.6626 101.806 78.1862 103.258 77.6495 104.62C77.1521 105.876 75.7777 106.989 73.5525 107.905C71.8378 106.217 70.9739 104.685 70.9739 103.324C70.9739 101.858 72.0079 100.706 73.0289 99.7374H73.042ZM84.4692 100.628C84.4692 100.837 84.3645 101.033 84.1812 101.151C84.011 101.269 83.8016 101.282 83.6053 101.217C81.668 100.47 78.6574 99.4494 75.3196 98.795C71.9818 98.1536 68.801 97.9703 66.7329 97.9441C66.5234 97.9441 66.3402 97.8394 66.2224 97.6693C66.1046 97.4991 66.0784 97.2766 66.1569 97.0802C67.1124 94.4754 69.6126 88.2971 72.6101 85.4305C73.2515 85.8101 73.9321 86.1112 74.6389 86.3076L73.3692 92.8654C73.2514 93.4675 73.4085 94.0958 73.7881 94.5801L74.5604 95.5488C74.9793 96.0723 75.5814 96.3472 76.2097 96.3472C76.5631 96.3472 76.9296 96.2556 77.2568 96.0723L78.3302 95.4571C78.8669 95.1561 79.2464 94.6325 79.3643 94.0173L80.6339 87.4725C80.9219 87.4987 81.2099 87.5249 81.4979 87.5249C81.9429 87.5249 82.3879 87.4856 82.833 87.4202C84.5346 91.2031 84.5477 97.8656 84.4692 100.641V100.628ZM74.6389 93.101L77.2699 79.5534C78.8145 71.5687 80.8565 65.9402 81.6942 65.6784C82.4665 66.2544 82.244 72.2363 80.6994 80.2209L78.0684 93.7686C78.0291 94.0042 77.8851 94.2005 77.6757 94.3184L76.5893 94.9336C76.2489 95.1299 75.817 95.0514 75.5683 94.7372L74.796 93.7686C74.652 93.5853 74.5866 93.3497 74.6389 93.1141V93.101ZM80.8826 86.1505L81.9822 80.4827C82.6759 76.8831 83.1864 73.2573 83.3566 70.5346C83.6184 66.49 83.2126 64.6574 81.9822 64.4087C80.7517 64.1862 79.6915 65.7177 78.4218 69.566C77.571 72.1578 76.6809 75.705 75.9871 79.3046L74.8876 85.0117C71.5891 84.0038 68.7879 80.3256 68.513 76.4511C67.7669 65.8486 67.7146 57.0393 68.356 49.5521L84.2728 52.6413L100.19 55.7304C97.9907 62.9165 94.6528 71.0713 89.993 80.6136C88.2913 84.0954 84.3252 86.4515 80.8957 86.1374L80.8826 86.1505ZM104.13 93.2581C104.025 94.2398 103.802 94.6718 103.619 94.7372C102.991 94.9859 101.001 93.2973 99.2473 91.8051C96.3676 89.3312 92.5192 86.0719 88.1604 84.9593C89.3908 83.9384 90.438 82.6687 91.1579 81.2026C93.2261 76.9485 95.0324 72.9693 96.6163 69.2126C101.185 75.7443 104.902 87.4463 104.13 93.2581ZM87.6761 35.0358C88.0688 35.1143 88.4484 35.1405 88.8411 35.1405C91.6946 35.1405 94.2602 33.1116 94.823 30.2058C95.4644 26.9072 93.3046 23.7003 90.0061 23.072C88.4091 22.7578 86.786 23.0982 85.4378 24.0013C84.0896 24.9176 83.1733 26.292 82.8723 27.8889C82.5581 29.4859 82.8984 31.1089 83.8016 32.4572C84.7179 33.8054 86.0923 34.7216 87.6892 35.0227L87.6761 35.0358ZM84.1419 28.1507C84.3906 26.8941 85.0975 25.8077 86.1577 25.1009C86.9562 24.5642 87.8856 24.2762 88.828 24.2762C89.1291 24.2762 89.4432 24.3024 89.7443 24.3678C92.336 24.8652 94.0246 27.3784 93.5271 29.9702C93.0297 32.5619 90.5166 34.2636 87.9248 33.7531C86.6682 33.5044 85.5818 32.7975 84.875 31.7373C84.1681 30.677 83.9063 29.4073 84.1419 28.1507Z"
        fill="url(#paint0_linear_819_762)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_819_762"
          x1="41.2737"
          y1="100.457"
          x2="96.0011"
          y2="5.66289"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color ?? '#9031DE'} />
          <stop offset="0.53" stopColor={color ?? '#C12CCF'} />
          <stop offset="0.77" stopColor={color ?? '#D140CF'} />
          <stop offset="1" stopColor={color ?? '#DD4FCF'} />
        </linearGradient>
      </defs>
    </svg>
  )
}
