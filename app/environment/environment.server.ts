import { z } from 'zod'

const environmentSchema = z.object({
  VERSION: z.string().default('1'),
  PORT: z.coerce.number().optional(),
  NODE_ENV: z.enum(['development', 'production', 'test']),

  AGREEMENTS_DATABASE_URL: z.string().url(),
  DATA_TENSET_URL: z.string().url(),
  DATA2_TENSET_URL: z.string().url(),
  DATABASE_URL: z.string().url(),
  TELEGRAM_BOT_DATABASE_URL: z.string().url(),
  TELEGRAM_BOT_TOKEN: z.string(),
  TELEGRAM_BOT_CHAT_ID: z.string(),
  STRAPI_URL: z.string().url(),
  STRAPI_TOKEN: z.string(),

  RECAPTCHA_SECRET: z.string(),
  RECAPTCHA_PUBLIC: z.string(),
  RECAPTCHA_URL: z.string().url(),

  SENTRY_ENABLED: z.enum(['true', 'false']).transform(v => v === 'true'),
  SENTRY_DSN: z.string().default(''),
  SENTRY_AUTH_TOKEN: z.string().default(''),
  SENTRY_ORG: z.string().default(''),
  SENTRY_PROJECT: z.string().default(''),
  SENTRY_ENVIRONMENT: z.string(),

  FIREBASE_API_HOST: z.string(),
  FIREBASE_MEASUREMENT_ID: z.string(),
  FIREBASE_APP_ID: z.string(),
  FIREBASE_MESSAGING_SENDER_ID: z.string(),
  FIREBASE_STORAGE_BUCKET: z.string(),
  FIREBASE_PROJECT_ID: z.string(),
  FIREBASE_DATABASE_URL: z.string(),
  FIREBASE_AUTH_DOMAIN: z.string(),
  FIREBASE_API_KEY: z.string(),

  LOCIZE_PROJECTID: z.string(),
  LOCIZE_API_KEY: z.string(),

  CRISP_WEBSITE_ID: z.string(),
})

let environment: ReturnType<typeof initializeEnvironment>

/**
 * Remix modules can not have side effects, so it is initialized in `entry.server.tsx`
 * @see https://remix.run/docs/en/main/guides/constraints#no-module-side-effects
 */
export const initializeEnvironment = () => {
  const environmentParsed = environmentSchema.safeParse(process.env)

  if (!environmentParsed.success) {
    console.error(
      'Invalid environment variables:',
      environmentParsed.error.flatten().fieldErrors
    )

    throw new Error('Invalid environment variables')
  }

  environment = environmentParsed.data

  return environmentParsed.data
}

/**
 * To import this, file name has to end with `.server.ts(x)`
 * @see https://remix.run/docs/en/main/discussion/server-vs-client#splitting-up-client-and-server-code
 *
 * @returns server sided environment variables
 */
export const getServerEnvironment = () => environment

/**
 * This is used in `root.tsx` to expose public env variables to browser code through `window.__remixContext.state.loaderData!.root.env`
 * @see https://remix.run/docs/en/main/guides/envvars#browser-environment-variables
 *
 * NOTE: Do *not* add any environment variables in here that
 * you do not wish to be included in the client.
 *
 * @returns all public/client environment variables
 */
export const getClientEnvironment = () => {
  const serverEnvironment = environment

  return {
    VERSION: serverEnvironment.VERSION,
    NODE_ENV: serverEnvironment.NODE_ENV,

    SENTRY_ENABLED: serverEnvironment.SENTRY_ENABLED,
    SENTRY_DSN: serverEnvironment.SENTRY_DSN,
    SENTRY_ENVIRONMENT: serverEnvironment.SENTRY_ENVIRONMENT,

    FIREBASE_API_HOST: serverEnvironment.FIREBASE_API_HOST,
    FIREBASE_MEASUREMENT_ID: serverEnvironment.FIREBASE_MEASUREMENT_ID,
    FIREBASE_APP_ID: serverEnvironment.FIREBASE_APP_ID,
    FIREBASE_MESSAGING_SENDER_ID:
      serverEnvironment.FIREBASE_MESSAGING_SENDER_ID,
    FIREBASE_STORAGE_BUCKET: serverEnvironment.FIREBASE_STORAGE_BUCKET,
    FIREBASE_PROJECT_ID: serverEnvironment.FIREBASE_PROJECT_ID,
    FIREBASE_DATABASE_URL: serverEnvironment.FIREBASE_DATABASE_URL,
    FIREBASE_AUTH_DOMAIN: serverEnvironment.FIREBASE_AUTH_DOMAIN,
    FIREBASE_API_KEY: serverEnvironment.FIREBASE_API_KEY,
  }
}

export type CLIENT_ENVIRONMENT = ReturnType<typeof getClientEnvironment>
type APP_ENVIRONMENT = z.infer<typeof environmentSchema>

declare global {
  interface Window {
    env: CLIENT_ENVIRONMENT
  }

  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace NodeJS {
    type ProcessEnvironment = APP_ENVIRONMENT
  }
}
