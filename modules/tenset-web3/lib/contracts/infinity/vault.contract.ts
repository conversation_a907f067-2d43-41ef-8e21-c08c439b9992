import { BigNumber, ContractTransaction } from 'ethers'

import { infinityVaultAbi } from '../../assets'
import { config } from '../../config'
import { InfinityVault, Utils } from '../../types'
import { Contract } from '../base'

export class InfinityVaultContract extends Contract {
  constructor(chain: Utils.Network) {
    super(config.infinity.vault[chain]!, chain, infinityVaultAbi)
  }

  public async getDepositDetails(
    id: number
  ): Promise<InfinityVault.DepositDetails> {
    return this.adapter!.getDepositDetails(id)
  }

  public async deposit(packageKey: string, amount: BigNumber) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.deposit(
        packageKey,
        amount
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }

  public async extendDeposit(id: number) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.extendDeposit(id)

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }

  public async withdrawDeposit(id: number) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.withdrawDeposit(id)

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }

  public async burnDeposit(
    id: number,
    overrides: Utils.ContractMethodOverrides = {}
  ) {
    this.wallet!.asBusy()

    try {
      const tx: ContractTransaction = await this.adapter!.burnDeposit(
        id,
        overrides
      )

      await tx.wait()
    } catch (error) {
      this.wallet!.asIdle()

      throw error
    }

    this.wallet!.asIdle()
  }
}
