import { AssetProps } from './index'

export default function ManualSwapback({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '130'}
      height={size ?? '130'}
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M61.5955 11.3157C61.7853 13.8788 63.9519 15.8846 66.5093 15.8846C68.8657 15.8846 70.8536 14.1908 71.3227 11.9286C82.6804 13.8899 91.8268 22.8162 94.0939 34.0493L90.0623 31.3858L89.4481 32.3108L95.0543 36.0106L95.3447 35.576L98.7955 30.4498L97.8686 29.8257L95.1772 33.8152C92.8096 22.1253 83.2722 12.8312 71.4455 10.8142C71.3785 8.15078 69.2008 6 66.5093 6C63.8179 6 61.562 8.21763 61.562 10.9367C61.562 11.0259 61.562 11.1151 61.5843 11.2042V11.3045L61.5955 11.3157ZM66.5093 7.1367C68.62 7.1367 70.3399 8.85286 70.3399 10.9591C70.3399 11.0036 70.3399 11.0482 70.3287 11.0816V11.2042H70.3176V11.2377C70.1724 13.2213 68.4972 14.7703 66.5205 14.7703C64.5438 14.7703 62.8686 13.2213 62.7234 11.2377C62.7234 11.1819 62.7123 11.1262 62.7011 11.0705C62.7011 11.0259 62.6899 10.9925 62.6899 10.9479C62.6899 8.8417 64.4098 7.12555 66.5205 7.12555L66.5093 7.1367ZM37.8415 45.3715C40.2091 57.0614 49.7464 66.3555 61.5731 68.3725C61.6401 71.0359 63.8179 73.1867 66.5093 73.1867C69.2008 73.1867 71.4567 70.9691 71.4567 68.2499C71.4567 68.1608 71.4567 68.0605 71.4232 67.8822C71.2333 65.3191 69.0668 63.3132 66.5093 63.3132C64.1529 63.3132 62.165 65.0071 61.696 67.2693C50.3383 65.3079 41.1918 56.3817 38.9248 45.1486L42.9564 47.812L43.5706 46.887L37.9643 43.1872L34.2119 48.7481L35.1389 49.3721L37.8303 45.3826L37.8415 45.3715ZM62.6899 68.1273C62.6899 68.1273 62.7011 68.0159 62.7123 67.9602C62.8574 65.9766 64.5326 64.4275 66.5093 64.4275C68.486 64.4275 70.1612 65.9766 70.3064 67.9602L70.3287 68.1162C70.3287 68.1162 70.3399 68.2053 70.3399 68.2388C70.3399 70.345 68.62 72.0611 66.5093 72.0611C64.3986 72.0611 62.6788 70.345 62.6788 68.2388C62.6788 68.1942 62.6788 68.1608 62.6899 68.1162V68.1273ZM23.6918 117.918C24.1162 117.829 24.3954 117.417 24.3172 116.993C24.2279 116.57 23.8147 116.28 23.3903 116.369C22.9659 116.459 22.6867 116.871 22.7649 117.294C22.8431 117.673 23.1669 117.93 23.5467 117.93C23.5913 117.93 23.6472 117.929 23.6918 117.918ZM23.2116 117.205L23.7588 117.094L23.2116 117.194C23.1781 117.016 23.2898 116.849 23.4685 116.815C23.636 116.771 23.8147 116.893 23.8482 117.071C23.8817 117.25 23.77 117.417 23.5913 117.45C23.4126 117.484 23.2451 117.372 23.2116 117.194V117.205ZM65.0687 50.0853H67.9611C71.8252 50.0853 75.1979 47.9903 77.0183 44.8811C77.0518 44.8477 77.0741 44.8031 77.0964 44.7585C77.9675 43.2318 78.4701 41.4711 78.4701 39.5989C78.4701 37.7267 77.9675 35.9549 77.0964 34.4281C77.0853 34.3947 77.0629 34.3724 77.0406 34.3501C75.2202 31.2298 71.8364 29.1125 67.9611 29.1125H65.0687C59.2726 29.1125 54.5597 33.8152 54.5597 39.5989C54.5597 45.3826 59.2726 50.0853 65.0687 50.0853ZM70.3399 48.6589C71.8252 47.7897 73.076 46.575 73.9806 45.1263H75.5218C74.271 46.8425 72.4506 48.1017 70.3399 48.6589ZM76.2477 44.0119H74.5948C75.1421 42.8307 75.4771 41.5268 75.5553 40.1561H77.331C77.2528 41.5379 76.8731 42.8418 76.2477 44.0119ZM77.3198 39.0417H75.5441C75.4659 37.671 75.1421 36.3783 74.5837 35.1859H76.2365C76.8619 36.3449 77.2304 37.6487 77.3198 39.0417ZM75.5329 34.0827H73.9918C73.0872 32.634 71.8364 31.4193 70.3511 30.55C72.4618 31.1072 74.2821 32.3665 75.5329 34.0827ZM65.0687 30.2269C70.2505 30.2269 74.4608 34.4281 74.4608 39.5989C74.4608 44.7697 70.2505 48.9709 65.0687 48.9709C59.8868 48.9709 55.6765 44.7697 55.6765 39.5989C55.6765 34.4281 59.8868 30.2269 65.0687 30.2269ZM111.895 93.0117C111.359 91.0615 109.126 89.5348 105.362 90.4709C103.553 90.9278 100.739 91.6522 97.6229 92.4545C98.7174 91.1618 99.1752 89.1894 99.6555 87.1277L101.386 79.7839C101.744 78.2906 100.672 76.8084 98.9519 76.4073C97.2879 76.0061 95.0766 76.7527 94.2837 80.1182C92.9771 85.6679 92.1172 87.7964 89.6044 89.4791C89.1242 89.8023 88.4206 90.248 87.5942 90.7606C87.8399 89.5014 87.8064 88.0415 87.7729 86.5148L87.6277 78.9815C87.5942 77.4548 86.1647 76.2513 84.4337 76.3181C82.725 76.3516 80.7595 77.6108 80.8265 81.0654C80.9382 86.7711 80.6366 89.0445 78.5929 91.329C78.3919 91.5519 78.3137 91.641 78.0457 91.9196C77.8447 91.8193 77.6325 91.7302 77.4203 91.6856C74.6172 91.0281 66.8779 90.014 58.77 89.4568C53.6886 89.1114 50.6957 90.5378 48.2946 91.6856C46.5636 92.5102 45.2011 93.1566 43.5482 92.9671C39.2486 92.4768 30.2809 91.5296 30.2809 91.5296L23.1558 90.8386C22.8766 90.8275 22.575 91.0281 22.5415 91.3401C22.508 91.641 22.7314 91.9196 23.0441 91.953L28.6615 92.4991C28.0585 93.3014 27.5447 94.5941 27.1315 96.3994C26.4615 99.2857 26.1376 103.108 26.1934 107.164C26.2158 108.747 26.3051 110.329 26.4503 111.845C26.8523 116.113 27.7234 119.857 29.0077 121.607L21.5364 121.886C21.2237 121.897 20.9892 122.153 21.0004 122.465C21.0116 122.766 21.2572 123 21.5588 123H21.5811L30.6829 122.666C30.6829 122.666 30.7164 122.666 30.7276 122.655C33.4414 122.51 34.2901 115.344 34.5246 112.391C41.3817 112.413 51.0084 112.536 54.8501 112.97C55.3191 113.026 55.844 113.048 56.4248 113.048C60.099 113.048 65.8057 112.034 70.3846 110.519C72.1491 109.939 76.538 108.446 81.7758 106.663C92.0725 103.164 106.177 98.3831 109.059 97.525C111.505 96.8006 112.331 94.6499 111.884 93.0006L111.895 93.0117ZM90.2298 90.4041C93.2116 88.4204 94.1274 85.7013 95.3782 80.3745C95.9812 77.8226 97.4889 77.2208 98.7062 77.4994C99.6555 77.7223 100.538 78.5692 100.303 79.5387L98.561 86.8826C97.9803 89.4122 97.4777 91.6076 95.9254 92.4434C95.568 92.6328 95.0655 92.9225 94.4959 93.268C88.6105 94.7947 82.4905 96.3883 80.3128 96.9566C80.3351 96.8118 80.3686 96.6669 80.3798 96.522C83.3504 94.7167 88.3424 91.6633 90.2298 90.4152V90.4041ZM79.464 92.0422C81.8539 89.39 82.0661 86.5148 81.9544 81.0543C81.8986 78.4243 83.2164 77.466 84.4672 77.4437C85.4165 77.4102 86.4997 78.0232 86.5221 79.015L86.6673 86.5594C86.7119 88.5096 86.7678 90.3595 86.187 91.6187C86.187 91.6299 86.187 91.6521 86.187 91.6633C84.1768 92.9114 81.8874 94.3155 80.3351 95.2516C80.1453 94.2152 79.6427 93.2569 78.9726 92.5882C79.1848 92.3654 79.2742 92.2651 79.4752 92.0422H79.464ZM30.6606 121.562C29.6555 121.562 28.2595 118.119 27.6452 112.402C28.6392 112.402 30.7611 112.402 33.419 112.402C32.9165 118.687 31.5317 121.551 30.6717 121.562H30.6606ZM108.757 96.4663C105.854 97.3355 91.7375 102.127 81.4407 105.627C76.203 107.398 71.814 108.892 70.0607 109.482C64.9682 111.165 58.2116 112.246 54.9953 111.878C50.9637 111.421 40.9573 111.31 34.0668 111.288C34.0668 111.288 34.0668 111.288 34.0556 111.288C31.0068 111.288 28.5722 111.288 27.5336 111.288C27.4219 110.006 27.3437 108.624 27.3214 107.142C27.2655 103.175 27.5894 99.4417 28.2371 96.6446C28.9519 93.5243 29.823 92.6662 30.2362 92.644C30.8169 92.7108 39.3045 93.6023 43.4477 94.0704C45.4244 94.2933 46.9879 93.5466 48.8083 92.6885C51.2206 91.5407 53.9455 90.2369 58.7253 90.5712C67.5926 91.1841 74.8629 92.2316 77.1969 92.7777C78.2914 93.034 79.2407 94.4716 79.3077 95.976C79.3412 96.8452 79.1066 98.3942 77.0629 99.0517C75.3989 99.5978 73.8913 99.5866 71.9927 99.5644C68.3408 99.5309 63.36 99.4975 53.6216 103.465C53.3313 103.576 53.1973 103.91 53.3201 104.189C53.4094 104.401 53.6216 104.535 53.8338 104.535C53.9008 104.535 53.979 104.523 54.046 104.49C63.5722 100.601 68.4302 100.634 71.9816 100.668C73.9136 100.679 75.5664 100.701 77.4091 100.099C78.5147 99.7426 79.3523 99.074 79.8549 98.2159C81.1727 97.8705 99.4209 93.112 105.653 91.5407C108.69 90.7718 110.444 91.875 110.834 93.2903C111.158 94.4604 110.555 95.9203 108.768 96.444L108.757 96.4663Z"
        fill="url(#paint0_linear_2185_57)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2185_57"
          x1="37.9755"
          y1="132.55"
          x2="100.147"
          y2="24.6286"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
      </defs>
    </svg>
  )
}
