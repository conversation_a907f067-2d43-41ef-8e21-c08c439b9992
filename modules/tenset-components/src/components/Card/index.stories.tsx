import { Meta } from '@storybook/react'

import { blankCover } from '../../assets/images/'
import { InfoIcon } from '../../assets/icons'
import { Button, ButtonVariant } from '../Button'
import { Text, TextL } from '../Typo/Text'

import { Card } from './index'

type CardType = typeof Card

export default {
  title: 'Components/Card',
  component: Card,
  args: {
    title: 'Title',
    children: <Text>Description</Text>,
    image: blankCover,
    actions: (
      <Button variant={ButtonVariant.Secondary}>
        Action
        <InfoIcon />
      </Button>
    ),
  },
} as Meta<CardType>

export const Small = {}

export const Large = {
  args: {
    isLarge: true,
    image: undefined,
    children: <TextL>Description</TextL>,
  },
}

export const Centered = {
  args: {
    isCentered: true,
  },
}
