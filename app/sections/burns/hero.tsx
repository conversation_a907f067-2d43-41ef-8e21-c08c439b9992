import { useTranslation } from 'react-i18next'

import { buybackIllustration } from '~/assets/images'
import TokensAvailableOnExchangesBlock from '~/components/blocks/available-on-exchanges'
import { Namespace } from '~/i18n'
import { H3, Hero, HeroImage } from '~/tenset-components'

export function BurnsHero() {
  const { t } = useTranslation([Namespace.BURNS])

  return (
    <Hero
      title={t('hero.title')}
      description={t('hero.description')}
      leftContent={<TokensAvailableOnExchangesBlock HeaderLevel={H3} />}
      rightContent={
        <HeroImage src={buybackIllustration} alt={t('hero.title')} />
      }
    />
  )
}
