{"name": "tenset-io", "repository": "**************:tenset_io/tenset-io.git", "author": "Tenset <gitlab.com/tenset_io>", "license": "UNLICENSED", "private": true, "sideEffects": false, "scripts": {"preinstall": "[ ! -z $CI_RUNNER ] && echo 'Skipping preinstall hook' || yarn git:submodules", "postinstall": "([ -f .env ] || cp .env.example .env) && ([ ! -z $CI_RUNNER ] && echo 'Skipping dev postinstall tasks' || (yarn git:submodules && yarn locize:download:dev))", "build": "yarn prisma generate && remix build --sourcemap", "start": "node server.js", "dev": "remix dev --manual", "lint": "run-s lint:*", "lint:ts": "eslint --ext \".js,.ts,.md,.jsx,.tsx,.mdx\" app --fix --ignore-path .gitignore --resolve-plugins-relative-to .", "lint:style": "stylelint \"**/*.{sass,scss,pcss,postcss,css}\" --fix --allow-empty-input --ignore-path .gitignore", "prepare": "husky", "sentry:upload-sourcemaps": "./node_modules/@sentry/remix/scripts/sentry-upload-sourcemaps.js", "locize:download:dev": "([ ! -z \"$CI_SYNC_TRANSLATIONS\" ] || dotenv -- locize download --path ./public/locales --format json)", "locize:sync": "([ ! -z \"$CI_SYNC_TRANSLATIONS\" ] && dotenv -- locize sync --path ./public/locales --format json --update-values true) || echo \"Skipping sync of translations (only production CI should sync)\"", "git:submodules": "git submodule update --init --recursive --remote", "ci:prebuilt": "yarn lint", "ci:postbuild": "yarn sentry:upload-sourcemaps --release $CI_COMMIT_SHA", "update-deps": "ncu '/^(?!@typescript-eslint|stylelint|ethers).*$/' -u && yarn"}, "lint-staged": {"*.{js,ts,md,jsx,tsx,mdx}": "eslint --cache --fix --ignore-path .gitignore --resolve-plugins-relative-to .", "*.{sass,scss,pcss,postcss,css}": "stylelint --fix --allow-empty-input --ignore-path .gitignore"}, "dependencies": {"@prisma/client": "5.11.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-tooltip": "1.0.7", "@react-spring/web": "9.7.3", "@remix-run/css-bundle": "2.8.1", "@remix-run/express": "2.8.1", "@remix-run/node": "2.8.1", "@remix-run/react": "2.8.1", "@sentry/remix": "8.17.0", "@typeform/embed-react": "3.16.0", "@walletconnect/ethereum-provider": "2.11.3", "@walletconnect/modal": "2.6.2", "accept-language": "3.0.18", "autoprefixer": "10.4.18", "clsx": "2.1.0", "dotenv": "16.4.5", "ethers": "5.7.2", "ethers-multicall-provider": "3.1.2", "express": "4.18.3", "firebase": "10.9.0", "grammy": "^1.35.0", "husky": "9.0.11", "i18next": "23.10.1", "i18next-browser-languagedetector": "7.2.0", "i18next-fs-backend": "2.3.1", "i18next-http-backend": "2.5.0", "i18next-resources-to-backend": "1.2.0", "isbot": "5.1.2", "lint-staged": "15.2.2", "lodash.sumby": "4.6.0", "marked": "12.0.1", "marked-base-url": "1.1.3", "node-html-parser": "6.1.12", "npm-run-all": "4.1.5", "pg": "^8.14.0", "postcss-loader": "8.1.1", "react": "18.2.0", "react-dom": "18.2.0", "react-ga4": "2.1.0", "react-google-recaptcha-v3": "1.10.1", "react-hotjar": "6.2.0", "react-i18next": "14.1.0", "react-infinite-scroll-component": "6.1.0", "react-player": "2.15.1", "react-sticky-box": "2.0.5", "react-toastify": "10.0.5", "recharts": "^2.12.7", "remix-i18next": "6.0.1", "remix-utils": "7.5.0"}, "devDependencies": {"@commitlint/cli": "19.2.1", "@commitlint/config-conventional": "19.1.0", "@commitlint/types": "19.0.3", "@remix-run/dev": "2.8.1", "@remix-run/eslint-config": "2.8.1", "@remix-run/serve": "2.8.1", "@restart/ui": "1.6.8", "@tailwindcss/typography": "0.5.10", "@types/i18next-fs-backend": "1.1.5", "@types/lodash.sumby": "4.6.9", "@types/marked": "6.0.0", "@types/react": "18.2.67", "@types/react-dom": "18.2.22", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "4.2.1", "@walletconnect/types": "2.11.3", "buffer": "6.0.3", "concurrently": "8.2.2", "dotenv-cli": "7.4.1", "eslint": "8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-alias": "1.1.2", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-import-helpers": "1.3.1", "eslint-plugin-jest-dom": "5.1.0", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-mdx": "3.1.5", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-sonarjs": "0.24.0", "eslint-plugin-testing-library": "6.2.0", "eslint-plugin-unicorn": "51.0.1", "locize-cli": "8.0.0", "npm-check-updates": "16.14.17", "number-money": "1.0.2", "postcss": "8.4.36", "prettier": "3.2.5", "prisma": "5.11.0", "stylelint": "15.11.0", "stylelint-config-prettier": "9.0.5", "stylelint-config-standard": "35.0.0", "tailwindcss": "3.4.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.4.2", "zod": "3.22.4"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0"}}