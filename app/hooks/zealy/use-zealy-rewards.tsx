import { useEffect, useState } from 'react'
import { BigNumber } from 'ethers'
import type { Id } from 'react-toastify'
import { toast } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import { formatEther } from 'ethers/lib/utils'

import { Button, ButtonVariant } from '~/tenset-components'
import { Namespace } from '~/i18n'
import type { Wallet, ZealyRewardsContract } from '~/tenset-web3'
import type { ZealyRewards } from '~/api/types/zealy-rewards'
import { fetchZealyRewards } from '~/api/fetchers'

const ZealyRewardsDataModel: ZealyRewards = {
  error: false,
  message: '',
  data: {
    address: '',
    rewardInWei: BigNumber.from(0),
    claimsEndsAt: 0,
    proofs: ['', ''],
    rewardInEther: 0,
    claimsEndsAtDateFormat: null,
  },
}

export function useZealyRewards({
  wallet,
  zealyRewardsContract,
}: {
  wallet: Wallet | null
  zealyRewardsContract: ZealyRewardsContract | undefined
}) {
  const { t } = useTranslation([Namespace.ZEALY, Namespace.COMMON])

  const [isDataLoading, setIsDataLoading] = useState(true)
  const [isCorrectNetwork, setIsCorrectNetwork] = useState(false)
  const [isRewardClaimed, setIsRewardClaimed] = useState(false)
  const [zealyRewardsData, setZealyRewardsData] = useState<ZealyRewards | null>(
    null
  )

  useEffect(() => {
    const contractChain = zealyRewardsContract?.chain
    if (contractChain === undefined || !wallet) return
    setIsCorrectNetwork(wallet?.isOnChain(contractChain))
  }, [wallet, wallet?.chain, zealyRewardsContract?.chain])

  useEffect(() => {
    if (!wallet?.account || !zealyRewardsContract?.address || !isCorrectNetwork)
      return

    const fetchRewardsAndStatus = async () => {
      try {
        const claimedDate = await wallet
          .interact(zealyRewardsContract)
          .getRewardClaimedAt(wallet.account!)
        const isClaimed = Number(claimedDate) !== 0
        setIsRewardClaimed(isClaimed)

        if (isClaimed) {
          setZealyRewardsData(ZealyRewardsDataModel)
          return
        }

        const zealyRewardsData = await fetchZealyRewards(
          zealyRewardsContract.address.toLowerCase(),
          wallet.account!.toLowerCase()
        )

        if (
          zealyRewardsData.error &&
          zealyRewardsData.message.includes('No proofs')
        ) {
          setZealyRewardsData(ZealyRewardsDataModel)
          return
        }

        const { rewardInWei, claimsEndsAt } = zealyRewardsData.data
        const claimsEndsAtDateFormat = new Date(Number(claimsEndsAt))
        const rewardInEther = Number(formatEther(rewardInWei))

        setZealyRewardsData({
          ...zealyRewardsData,
          data: {
            ...zealyRewardsData.data,
            claimsEndsAtDateFormat,
            rewardInEther,
          },
        })
      } catch (error) {
        console.error('Error fetching reward data or status:', error)
        // toast.error('Failed to load data about your zealy rewards.')
      } finally {
        setIsDataLoading(false)
      }
    }

    if (wallet?.account && zealyRewardsContract?.address && isCorrectNetwork) {
      fetchRewardsAndStatus()
    }
  }, [
    wallet,
    zealyRewardsContract,
    wallet?.account,
    zealyRewardsContract?.address,
    isCorrectNetwork,
  ])

  async function handleClaim() {
    if (!zealyRewardsContract) {
      toast.error('Failed to connect with contract.')
      return
    }

    if (
      !zealyRewardsData ||
      !zealyRewardsData.data.proofs ||
      !zealyRewardsData.data.rewardInWei
    ) {
      // toast.error('Failed to load data about your zealy rewards.')
      return
    }

    const MessageWithHash = ({
      message,
      txHash,
    }: {
      message: string
      txHash: string
    }) => (
      <div>
        <div>{message}</div>
        <Button
          className="mt-2"
          variant={ButtonVariant.Link}
          to={`https://bscscan.com/tx/${txHash}`}
        >
          View on Bsc Scan
        </Button>
      </div>
    )

    const toastTxPending = (txHash: string, id: Id) =>
      toast.update(id, {
        render: MessageWithHash({
          message: t('toast.waiting'),
          txHash,
        }),
        closeButton: false,
      })

    const toastPendingMessage = t('toast.pending')

    const toastId = toast.loading(toastPendingMessage)

    try {
      const proofs = zealyRewardsData.data.proofs
      const amount = zealyRewardsData.data.rewardInWei

      const txHash = await wallet
        ?.interact(zealyRewardsContract)
        .claimReward(amount, proofs, txHash => toastTxPending(txHash, toastId))

      txHash &&
        toast.update(toastId, {
          render: MessageWithHash({
            message: t('toast.success'),
            txHash,
          }),
          type: 'success',
          isLoading: false,
          autoClose: 5000,
          closeButton: true,
        })
      setZealyRewardsData(ZealyRewardsDataModel)
    } catch (error) {
      const render = t('toast.error')

      toast.update(toastId, {
        render,
        type: 'error',
        isLoading: false,
        autoClose: 5000,
        closeButton: true,
      })
      console.error('Error claiming rewards', error)
    }
  }

  return {
    isDataLoading,
    isCorrectNetwork,
    isRewardClaimed,
    zealyRewardsData,
    handleClaim,
  }
}
