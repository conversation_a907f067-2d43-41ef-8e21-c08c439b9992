[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "packageKey", "type": "string"}, {"indexed": true, "internalType": "address", "name": "withdrawalAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "doesExistLock", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "get", "outputs": [{"components": [{"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "withdrawn", "type": "bool"}, {"internalType": "uint8", "name": "networkId", "type": "uint8"}, {"internalType": "uint256", "name": "lockId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "lockUntil", "type": "uint256"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}, {"internalType": "string", "name": "packageKey", "type": "string"}], "internalType": "struct IMigrate.Deposit", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "remove", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "contractAddress", "type": "address"}], "name": "setInitialState", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "withdrawn", "type": "bool"}, {"internalType": "uint8", "name": "networkId", "type": "uint8"}, {"internalType": "uint256", "name": "lockId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "lockUntil", "type": "uint256"}, {"internalType": "address", "name": "withdrawalAddress", "type": "address"}, {"internalType": "string", "name": "packageKey", "type": "string"}], "internalType": "struct IMigrate.Deposit", "name": "deposit", "type": "tuple"}, {"internalType": "bool", "name": "update", "type": "bool"}], "name": "upsert", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "withdrawDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]