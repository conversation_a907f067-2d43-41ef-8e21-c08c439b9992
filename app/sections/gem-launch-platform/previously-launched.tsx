import { useTranslation } from 'react-i18next'

import { Namespace } from '~/i18n'
import type { TableHeaders } from '~/tenset-components'
import {
  ButtonVariant,
  CryptoCurrencyFormatter,
  H2,
  NumberFormatter,
  Table,
  Tag,
  TextS,
  TextXs,
  Tooltip,
} from '~/tenset-components'
import { UnicodeChars } from '~/tenset-components/utils'
import ReadMoreButton from '~/components/read-more-button'
import { InfoAlert } from 'modules/tenset-components/src/assets/icons'
import type { GemAttributesType } from '~/api/types'

interface GlpPreviouslyLaunchedProps {
  gems: GemAttributesType[]
}

export function GlpPreviouslyLaunched({ gems }: GlpPreviouslyLaunchedProps) {
  const { t } = useTranslation([Namespace.GEM_LAUNCH_PLATFORM])

  const headers: TableHeaders = {
    name: t('previously-launched.table.columns.name'),
    allTimeHigh: t('previously-launched.table.columns.all-time-high'),
    totalRaise: t('previously-launched.table.columns.total-raise'),
    allocationSize: {
      label: t('previously-launched.table.columns.allocation-size'),
      additionalContent: (
        <Tooltip text="Allocation size is calculated for tier 5 based on the guaranteed allocation of round 1 and round 2, fcfs. To see details, visit the gem's subpage.">
          <button>
            <InfoAlert color="#fafafa" className="ml-1 scale-[0.75]" />
          </button>
        </Tooltip>
      ),
    },
    participants: t('previously-launched.table.columns.participants'),
    details: t('previously-launched.table.columns.details'),
  }

  const items = gems.map(gem => {
    const { launchData, tokenData, tags } = gem

    const raised = launchData!.find(({ key }) => key === 'raised')
    const participants = launchData!.find(({ key }) => key === 'participants')
    const athSinceLaunch = launchData!.find(
      ({ key }) => key === 'athSinceLaunch'
    )
    const maxInvestment = launchData!.find(({ key }) => key === 'maxInvestment')
    const circulationSupply = tokenData!.find(
      ({ key }) => key === 'circulationSupply'
    )

    const currency = circulationSupply?.currency?.currency

    const visibleTags = tags?.filter(({ tableVisibility }) => tableVisibility)

    return {
      name: (
        <div className="flex items-center flex-wrap gap-4">
          {gem.card.cover.data && gem.card.cover.data.attributes.url && (
            <img
              width={24}
              height={24}
              src={gem.card.cover.data.attributes.url}
              alt={gem.title}
              className="rounded-full"
            />
          )}

          <TextS isBold>
            {gem.title}

            <TextS tag="span" className="text-neutral-100">
              {' '}
              &middot; {currency}
            </TextS>
          </TextS>

          {visibleTags &&
            visibleTags.map(({ label, color, tooltipText }) =>
              tooltipText ? (
                <Tooltip key={label} text={tooltipText}>
                  <button className="cursor-default">
                    <Tag color={color}>
                      <TextXs>{label}</TextXs>
                    </Tag>
                  </button>
                </Tooltip>
              ) : (
                <Tag key={label} color={color}>
                  <TextXs>{label}</TextXs>
                </Tag>
              )
            )}
        </div>
      ),
      allTimeHigh: athSinceLaunch?.number?.value ? (
        <NumberFormatter
          value={athSinceLaunch.number.value}
          suffix={athSinceLaunch.number.suffix}
        />
      ) : (
        UnicodeChars.enDash
      ),
      totalRaise: raised?.currency?.value ? (
        <CryptoCurrencyFormatter
          value={raised.currency.value}
          currency={raised.currency.currency}
        />
      ) : (
        UnicodeChars.enDash
      ),
      allocationSize: maxInvestment?.currency?.value ? (
        <CryptoCurrencyFormatter
          value={maxInvestment.currency.value}
          currency={maxInvestment.currency.currency}
        />
      ) : (
        UnicodeChars.enDash
      ),
      participants: participants?.number?.value ? (
        <NumberFormatter value={participants.number.value} />
      ) : (
        UnicodeChars.enDash
      ),
      details: (gem.name || gem.card.readMoreLink) && (
        <ReadMoreButton
          buttonVariant={ButtonVariant.Link}
          link={gem.card.readMoreLink ?? `/${gem.name}`}
        />
      ),
    }
  })

  return (
    <div className="flex flex-col gap-12">
      <H2 isBold>{t('previously-launched.title')}</H2>

      {/* @ts-expect-error undefined cell */}
      <Table headers={headers} items={items} />
    </div>
  )
}
