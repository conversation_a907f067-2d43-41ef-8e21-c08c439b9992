import type {
  Firestore,
  CollectionReference,
  QueryConstraint,
} from 'firebase/firestore'
import { collection, query, getDocs, onSnapshot } from 'firebase/firestore'
import { Observable } from 'rxjs'

export class FirestoreClient {
  protected db: Firestore

  constructor(database: Firestore) {
    this.db = database
  }

  collection<T>(name: string) {
    return new FirestoreCollectionReference<T>(this.db, name)
  }
}

class FirestoreCollectionReference<T> {
  protected db: Firestore

  protected ref: CollectionReference<T>

  constructor(database: Firestore, name: string) {
    this.ref = collection((this.db = database), name) as CollectionReference<T>
  }

  query(...constraints: QueryConstraint[]): Promise<T[]> {
    const q = query(this.ref, ...constraints)

    return getDocs(q).then(snapshot =>
      snapshot.docs.map(document => ({ ...document.data(), id: document.id }))
    )
  }

  listen(...constraints: QueryConstraint[]): Observable<T[]> {
    const q = query(this.ref, ...constraints)

    return new Observable(subscriber => {
      onSnapshot(q, snapshot => {
        const items: T[] = []

        for (let index = 0, x = snapshot.docs.length; index < x; index++) {
          items.push(snapshot.docs[index].data())
        }

        subscriber.next(items)
      })
    })
  }
}
