import { AssetProps } from './index'

export default function TransferCompleted({ size, className }: AssetProps) {
  return (
    <svg
      width={size ?? '124'}
      height={size ?? '124'}
      viewBox="0 0 124 124"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_2237_5)">
        <path
          d="M103.613 79.8379C100.73 78.5603 97.3554 79.8379 96.074 82.7241C94.7926 85.5989 96.074 88.9641 98.9685 90.2418C101.852 91.5195 105.227 90.2418 106.508 87.3556C107.789 84.4809 106.508 81.1156 103.613 79.8379Z"
          fill="#15121A"
        />
        <path
          d="M25.9191 35.3706C23.0361 34.0929 19.661 35.3706 18.3797 38.2568C17.0983 41.1315 18.3797 44.4968 21.2742 45.7745C24.1573 47.0521 27.5323 45.7745 28.8137 42.8883C30.095 40.0021 28.8137 36.6483 25.9191 35.3706Z"
          fill="#15121A"
        />
        <path
          d="M79.565 49.1055L57.347 71.2593C56.569 72.035 55.3106 72.035 54.5326 71.2593L49.0524 65.795L51.6266 63.2283C51.9927 62.8632 51.7754 62.2472 51.2605 62.1788L39.2248 60.6729C38.8244 60.6273 38.4812 60.9581 38.5384 61.3574L40.0485 73.3583C40.1172 73.8717 40.735 74.077 41.1011 73.7234L43.435 71.3962L52.3474 80.2828C54.3381 82.2678 57.5529 82.2678 59.5436 80.2828L85.1824 54.7181C86.7384 53.1666 86.7384 50.6683 85.1824 49.1169C83.6265 47.5654 81.121 47.5654 79.565 49.1169V49.1055Z"
          fill="#15121A"
        />
        <path
          d="M76.7621 103.133C76.7621 103.11 76.7392 103.087 76.7278 103.064C76.7049 103.019 76.682 102.984 76.6591 102.939C76.6362 102.916 76.6019 102.893 76.5676 102.87C76.5447 102.847 76.5218 102.825 76.4875 102.813L70.3781 99.7103C70.0921 99.562 69.7489 99.6761 69.6116 99.9613C69.4629 100.247 69.5773 100.589 69.8633 100.726L74.7256 103.201C58.6742 108.392 40.9753 104.011 29.2828 91.782C17.4073 79.3475 13.9179 61.1294 20.2904 45.2042C20.9425 45.4209 21.6061 45.535 22.2811 45.535C23.0362 45.535 23.8027 45.3981 24.5349 45.1129C26.1023 44.5083 27.3379 43.3447 28.0244 41.8161C28.7108 40.2874 28.7566 38.5877 28.1502 37.0248C27.5553 35.462 26.3769 34.2299 24.8438 33.5455C21.6747 32.1423 17.9565 33.5569 16.5493 36.7168C15.8628 38.2454 15.817 39.9452 16.4234 41.5081C16.9611 42.8998 17.9679 44.0063 19.2607 44.725C12.6823 61.0723 16.2518 79.8038 28.4591 92.5691C37.1312 101.638 49.0182 106.521 61.1569 106.521C65.8247 106.521 70.5269 105.791 75.1032 104.308L72.6663 109.19C72.529 109.475 72.6434 109.818 72.918 109.954C72.9981 110 73.0896 110.012 73.1697 110.012C73.3756 110.012 73.5816 109.897 73.6845 109.692L76.7392 103.578C76.7621 103.532 76.7735 103.475 76.785 103.429C76.785 103.406 76.7964 103.384 76.7964 103.372C76.7964 103.349 76.7964 103.327 76.7964 103.304C76.7964 103.258 76.7964 103.201 76.7735 103.155L76.7621 103.133ZM17.4874 41.1088C16.9954 39.8311 17.0298 38.4394 17.5904 37.1845C18.437 35.2794 20.3247 34.1501 22.2925 34.1501C22.9904 34.1501 23.6997 34.287 24.3747 34.595C25.6332 35.1539 26.5943 36.1692 27.0862 37.4469C27.5782 38.7246 27.5438 40.1163 26.9832 41.3712C26.4226 42.626 25.4044 43.5843 24.1231 44.0748C22.8417 44.5653 21.4459 44.5311 20.1874 43.9721C18.9289 43.4131 17.9679 42.4093 17.476 41.1316L17.4874 41.1088ZM77.849 47.3944L55.631 69.5483C55.0933 70.0844 54.1551 70.0844 53.6174 69.5483L48.5377 64.4832L50.7 62.3272C51.0203 62.0078 51.1347 61.54 50.986 61.1065C50.8373 60.673 50.4597 60.365 50.0135 60.308L37.9778 58.8022C37.6117 58.7566 37.2456 58.882 36.9825 59.1444C36.7194 59.4068 36.5935 59.7604 36.6393 60.1369L38.1495 72.1378C38.2067 72.5941 38.5156 72.9706 38.9503 73.1189C39.3851 73.2672 39.8541 73.1531 40.1745 72.8337L42.108 70.9058L50.6085 79.3817C51.7068 80.4769 53.1598 81.0359 54.6127 81.0359C56.0657 81.0359 57.5073 80.4883 58.617 79.3817L84.2673 53.8056C86.0406 52.0374 86.0406 49.1626 84.2673 47.3944C82.4939 45.6262 79.6109 45.6262 77.8375 47.3944H77.849ZM83.4664 52.9956L57.8162 78.5718C56.0543 80.3286 53.1941 80.3286 51.4322 78.5718L42.5198 69.6852C42.4054 69.5711 42.2567 69.514 42.1194 69.514C41.9821 69.514 41.8219 69.5711 41.719 69.6852L39.305 71.9895L37.8406 59.943L49.8991 61.5286L47.325 64.0954C47.0961 64.3235 47.0961 64.6772 47.325 64.9053L52.8051 70.3696C53.7776 71.3393 55.4594 71.3393 56.4318 70.3696L78.6498 48.2158C79.977 46.8925 82.1278 46.8925 83.455 48.2158C84.7821 49.5391 84.7821 51.6838 83.455 53.0071L83.4664 52.9956ZM103.019 78.2524C109.586 61.9051 106.005 43.1964 93.8089 30.4425C81.8075 17.894 63.6281 13.388 47.1533 18.7268L49.6131 13.8215C49.7504 13.5363 49.636 13.194 49.3614 13.0572C49.0754 12.9203 48.7322 13.0343 48.5949 13.3081L45.5402 19.4227C45.5173 19.4683 45.5059 19.5253 45.4944 19.571C45.4944 19.5938 45.483 19.6166 45.483 19.628C45.483 19.6508 45.483 19.6736 45.483 19.6965C45.483 19.7421 45.483 19.7991 45.5059 19.8448C45.5059 19.8676 45.5288 19.8904 45.5402 19.9132C45.5631 19.9588 45.586 19.9931 45.6088 20.0273C45.6317 20.0501 45.666 20.0729 45.6889 20.0957C45.7118 20.1185 45.7347 20.1414 45.769 20.1642L51.8784 23.2671C51.9585 23.3127 52.05 23.3241 52.1415 23.3241C52.3475 23.3241 52.5534 23.21 52.6564 23.0161C52.8051 22.7309 52.6907 22.3887 52.4047 22.2518L47.5423 19.7763C63.5938 14.5858 81.2926 18.9778 92.9851 31.1955C104.861 43.6185 108.35 61.8366 101.978 77.7618C98.9458 76.7466 95.5707 78.1839 94.2436 81.1499C93.5572 82.6786 93.5114 84.3783 94.1178 85.9412C94.7241 87.5041 95.8911 88.7361 97.4242 89.4206C98.2479 89.7856 99.1059 89.9681 99.9754 89.9681C100.731 89.9681 101.497 89.8312 102.229 89.546C103.797 88.9414 105.032 87.7778 105.719 86.2492C107.057 83.2604 105.845 79.7696 103.019 78.2182V78.2524ZM104.666 85.8271C104.106 87.082 103.087 88.0402 101.806 88.5307C100.525 89.0213 99.1288 88.9871 97.8703 88.4281C96.6119 87.8691 95.6508 86.8652 95.1589 85.5876C94.6669 84.3099 94.7012 82.9181 95.2618 81.6633C96.1085 79.7582 97.9962 78.6288 99.964 78.6288C100.662 78.6288 101.371 78.7657 102.046 79.0737C104.632 80.2259 105.799 83.2604 104.643 85.8385L104.666 85.8271Z"
          fill="url(#paint0_linear_2237_5)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2237_5"
          x1="38.5613"
          y1="100.486"
          x2="83.4997"
          y2="22.413"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#9031DE" />
          <stop offset="0.53" stopColor="#C12CCF" />
          <stop offset="0.77" stopColor="#D140CF" />
          <stop offset="1" stopColor="#DD4FCF" />
        </linearGradient>
        <clipPath id="clip0_2237_5">
          <rect
            width="91"
            height="97"
            fill="white"
            transform="translate(16 13)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
