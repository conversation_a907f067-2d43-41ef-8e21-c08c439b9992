import { Meta } from '@storybook/react'

import { TextL } from '../Typo/Text'
import { Button } from '../Button'
import { Input } from '../Input'

import {
  ExpansionPanel,
  ExpansionPanelContent,
  ExpansionPanelItem,
  ExpansionPanelTrigger,
} from './index'

type ExpansionPanelType = typeof ExpansionPanel

export default {
  title: 'Components/ExpansionPanel',
  component: ExpansionPanel,
  args: {
    type: 'multiple',
    collapsible: true,
  },
} as Meta<ExpansionPanelType>

export const Preview = {
  args: {
    children: (
      <>
        <ExpansionPanelItem value="item-1">
          <ExpansionPanelTrigger>Item 1</ExpansionPanelTrigger>
          <ExpansionPanelContent>
            <TextL>
              Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aut,
              inventore!
            </TextL>
          </ExpansionPanelContent>
        </ExpansionPanelItem>

        <ExpansionPanelItem value="item-2">
          <ExpansionPanelTrigger>Item 2</ExpansionPanelTrigger>
          <ExpansionPanelContent>
            <Button>Lorem ipsum</Button>
          </ExpansionPanelContent>
        </ExpansionPanelItem>

        <ExpansionPanelItem value="item-3">
          <ExpansionPanelTrigger>Item 3</ExpansionPanelTrigger>
          <ExpansionPanelContent>
            <Input placeholder="Lorem ipsum dolor sit amet, consectetur" />
          </ExpansionPanelContent>
        </ExpansionPanelItem>
      </>
    ),
  },
}
