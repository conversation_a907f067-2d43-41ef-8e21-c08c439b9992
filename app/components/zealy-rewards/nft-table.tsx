import { useTranslation } from 'react-i18next'

import { Loading2 } from 'modules/tenset-components/src/assets/icons'
import { Namespace } from '~/i18n'
import { DateFormatter, Table, Text, TextNumeric } from '~/tenset-components'

export interface TableComponentProps {
  rewardName: string
  timeToClaim: Date | null
  isDataLoading: boolean
}

export function NftTableComponent({
  rewardName,
  timeToClaim,
  isDataLoading,
}: TableComponentProps) {
  const { t } = useTranslation([Namespace.ZEALY])

  const renderReward = () => {
    if (isDataLoading) return <Loading2 />
    return <TextNumeric>{rewardName}</TextNumeric>
  }

  const renderTimeToClaim = () => {
    if (isDataLoading) return <Loading2 />
    if (timeToClaim === null) return <div>-</div>
    return <DateFormatter date={timeToClaim} />
  }

  const table = {
    items: [
      {
        type: t('table.your-rewards'),
        value: renderReward(),
      },
      {
        type: t('table.time-to-claim'),
        value: renderTimeToClaim(),
      },
    ],
  }

  return (
    <div>
      <Table
        items={table.items}
        isRaw
        typography={{
          header: Text,
          mainRow: Text,
          row: TextNumeric,
        }}
      />
    </div>
  )
}
