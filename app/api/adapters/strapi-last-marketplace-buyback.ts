import type {
  StrapiLastBuybackApiResponse,
  StrapiLastMarketplaceBuybackApiData,
} from '~/api/types'

const SECONDS_IN_ONE_DAY = 24 * 60 * 60

export function adaptStrapiLastMarketplaceBuyback(
  data: StrapiLastBuybackApiResponse
): StrapiLastMarketplaceBuybackApiData {
  const startDate = new Date(data.attributes.createdAt)
  const endDate = new Date(startDate.getTime() + 3 * SECONDS_IN_ONE_DAY * 1000)
  const now = new Date()

  if (now.getTime() > endDate.getTime()) {
    return {
      amount: 0,
      amountPerSecond: 0,
    }
  }

  const totalSeconds = (endDate.getTime() - startDate.getTime()) / 1000
  const secondsPassed = (now.getTime() - startDate.getTime()) / 1000

  const amountPerSecond = data.attributes.amount / totalSeconds

  const amount = amountPerSecond * secondsPassed

  return {
    amount,
    amountPerSecond,
  }
}
