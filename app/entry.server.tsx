/**
 * By default, <PERSON> will handle generating the HTTP Response for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/docs/en/main/file-conventions/entry.server
 */

import { resolve } from 'node:path'
import { PassThrough } from 'node:stream'

import type { EntryContext } from '@remix-run/node'
import { createReadableStreamFromReadable, redirect } from '@remix-run/node'
import { RemixServer } from '@remix-run/react'
import { createInstance } from 'i18next'
import resourcesToBackend from 'i18next-resources-to-backend'
import { isbot } from 'isbot'
import { renderToPipeableStream } from 'react-dom/server'
import { I18nextProvider, initReactI18next } from 'react-i18next'

import { detectLocale, i18nSharedConfig, i18next } from './i18n'
import { sentryInit } from './sentry/sentry.server'

import {
  getServerEnvironment,
  initializeEnvironment,
} from '~/environment/environment.server'

initializeEnvironment()

if (getServerEnvironment().SENTRY_ENABLED) sentryInit()

const ABORT_DELAY = 5000

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext
) {
  const callbackName = isbot(request.headers.get('user-agent'))
    ? 'onAllReady'
    : 'onShellReady'

  const instance = createInstance()
  const locale = detectLocale(request)
  const ns = i18next.getRouteNamespaces(remixContext)

  if (responseStatusCode === 404) {
    return redirect(`/${locale}/404`, {
      status: 301,
      headers: { 'Cache-Control': 'no-cache' },
    })
  }

  const ResourceBackend = resourcesToBackend(
    async (language, namespace, callback) => {
      const path = `../public/locales/${language}/${namespace}.json`

      try {
        // eslint-disable-next-line unicorn/prefer-module
        const resource = await require(path)

        callback(null, resource)
      } catch {
        callback(null, null)
      }
    }
  )

  await instance
    .use(initReactI18next)
    .use(ResourceBackend)
    .init({
      ...i18nSharedConfig,
      lng: locale,
      ns,
      backend: { loadPath: resolve('./public/locales/{{lng}}/{{ns}}.json') },
    })

  return new Promise((resolve, reject) => {
    let didError = false

    const { pipe, abort } = renderToPipeableStream(
      <I18nextProvider i18n={instance}>
        <RemixServer context={remixContext} url={request.url} />
      </I18nextProvider>,
      {
        [callbackName]: () => {
          const body = new PassThrough()

          responseHeaders.set('Content-Type', 'text/html')

          resolve(
            new Response(createReadableStreamFromReadable(body), {
              headers: responseHeaders,
              status: didError ? 500 : responseStatusCode,
            })
          )

          pipe(body)
        },
        onShellError(error: unknown) {
          reject(error)
        },
        onError(error: unknown) {
          didError = true

          console.error(error)
        },
      }
    )

    setTimeout(abort, ABORT_DELAY)
  })
}

export { wrapHandleErrorWithSentry as handleError } from '@sentry/remix'
